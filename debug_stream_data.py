#!/usr/bin/env python3
"""
调试流式API数据结构
"""

import requests
import json
import sys

# 配置
BASE_URL = "http://localhost:8000/api/v1"
USERNAME = "admin"
PASSWORD = "admin123"

def login():
    """登录获取token"""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 登录成功")
        print(f"登录响应: {result}")
        return result.get("access_token") or result.get("token")
    else:
        print(f"❌ 登录失败: {response.status_code} - {response.text}")
        return None

def test_stream_api(token):
    """测试流式API"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "requirement_text": "测试用户登录功能，包括正常登录、密码错误、用户不存在等场景",
        "background_knowledge": "这是一个Web应用的用户认证系统",
        "database_type": "tdsql-pg",
        "llm_model_id": 7
    }
    
    print("🚀 测试流式需求分析API...")
    
    try:
        response = requests.post(
            f"{BASE_URL}/tdpilot/analyze-requirement-stream",
            json=data,
            headers=headers,
            stream=True
        )
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.status_code} - {response.text}")
            return
        
        print("📡 开始接收流式数据...")
        
        complete_data = None
        chunk_count = 0
        
        for line in response.iter_lines(decode_unicode=True):
            if line.startswith('data: '):
                chunk_count += 1
                data_str = line[6:]  # 移除 'data: ' 前缀
                
                if data_str == '[DONE]':
                    print("🏁 流式传输完成")
                    break
                
                try:
                    chunk_data = json.loads(data_str)
                    chunk_type = chunk_data.get('type', 'unknown')
                    
                    print(f"📦 Chunk {chunk_count}: type={chunk_type}")
                    
                    if chunk_type == 'complete':
                        print("🎯 收到完成信号，完整数据结构:")
                        print(json.dumps(chunk_data, indent=2, ensure_ascii=False))
                        complete_data = chunk_data
                        
                        # 分析前端会收到什么数据
                        print(f"\n🔍 前端数据分析:")
                        print(f"  - chunk_data 类型: {type(chunk_data)}")
                        print(f"  - chunk_data 键: {list(chunk_data.keys())}")
                        
                        if 'data' in chunk_data:
                            data_field = chunk_data['data']
                            print(f"  - data 字段类型: {type(data_field)}")
                            print(f"  - data 字段键: {list(data_field.keys()) if isinstance(data_field, dict) else 'N/A'}")
                            
                            if 'analysis_result' in data_field:
                                analysis_result = data_field['analysis_result']
                                print(f"  - analysis_result 类型: {type(analysis_result)}")
                                print(f"  - analysis_result 键: {list(analysis_result.keys()) if isinstance(analysis_result, dict) else 'N/A'}")
                                
                                # 这是前端最终会收到的数据
                                print(f"\n📋 前端最终数据 (analysis_result):")
                                if isinstance(analysis_result, dict):
                                    for key, value in analysis_result.items():
                                        if key == 'test_scenarios':
                                            print(f"  - {key}: {len(value)} 个场景")
                                            for i, scenario in enumerate(value):
                                                print(f"    场景 {i+1}: {type(scenario)}")
                                                if isinstance(scenario, dict):
                                                    print(f"      scenario: {scenario.get('scenario', 'N/A')}")
                                                    print(f"      description: {scenario.get('description', 'N/A')}")
                                                    print(f"      test_points: {scenario.get('test_points', [])}")
                                        elif key == 'test_points':
                                            print(f"  - {key}: {len(value)} 个测试点")
                                            for i, point in enumerate(value[:3]):  # 只显示前3个
                                                print(f"    {i+1}: {point}")
                                        elif key == 'recommendations':
                                            print(f"  - {key}: {len(value)} 个建议")
                                            for i, rec in enumerate(value[:3]):  # 只显示前3个
                                                print(f"    {i+1}: {rec}")
                                        else:
                                            print(f"  - {key}: {type(value)}")
                    
                    elif chunk_type in ['llm_output', 'llm_reasoning']:
                        # 不打印内容，只显示类型
                        content_length = len(chunk_data.get('content', ''))
                        print(f"  📝 {chunk_type}: {content_length} 字符")
                    
                    elif chunk_type in ['start', 'progress']:
                        message = chunk_data.get('message', '')
                        print(f"  📢 {chunk_type}: {message}")
                    
                    elif chunk_type == 'error':
                        message = chunk_data.get('message', '')
                        print(f"  ❌ error: {message}")
                
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"原始数据: {data_str[:200]}...")
        
        print(f"\n📊 总计接收 {chunk_count} 个数据块")
        
        if complete_data:
            print("\n🎉 流式API测试完成")
            return complete_data
        else:
            print("\n⚠️ 未收到完成信号")
            return None
            
    except Exception as e:
        print(f"❌ 流式API测试失败: {e}")
        return None

def main():
    """主函数"""
    print("🔍 流式API数据结构调试")
    print("=" * 60)
    
    # 登录
    token = login()
    if not token:
        sys.exit(1)
    
    # 测试流式API
    result = test_stream_api(token)
    
    print("\n" + "=" * 60)
    if result:
        print("✅ 调试完成！请检查上述数据结构")
        print("💡 重点关注 'analysis_result' 字段的结构")
    else:
        print("❌ 调试失败！请检查后端服务状态")

if __name__ == "__main__":
    main()
