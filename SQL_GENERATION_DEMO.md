# TDSQL AI测试平台 - SQL脚本生成功能演示

## 功能概述

SQL脚本生成功能是TDSQL AI测试平台的核心功能之一，能够根据测试用例智能生成针对不同数据库类型的SQL测试脚本。

## 主要特性

### 🚀 核心功能
- **智能SQL生成**：基于测试用例自动生成完整的SQL测试脚本
- **多数据库支持**：支持TDSQL-PostgreSQL、TDSQL-Oracle、TDSQL2、TDSQL3、CynosDB等
- **双模式生成**：支持AI智能生成和模板快速生成两种模式
- **流式响应**：实时显示生成进度，提升用户体验
- **代码编辑器**：集成Monaco Editor，支持SQL语法高亮和编辑

### 🎯 技术特点
- **模板引擎**：内置多数据库SQL模板，确保语法兼容性
- **数据库适配器**：自动适配不同数据库的语法差异
- **场景分类**：支持功能测试、性能测试、安全测试、边界测试等多种场景
- **历史管理**：完整的生成历史记录和管理功能

## 系统架构

```
前端界面 (React + Ant Design)
    ↓
API接口层 (FastAPI)
    ↓
SQL生成服务 (SQLGenerator)
    ↓
┌─────────────────┬─────────────────┬─────────────────┐
│   模板引擎      │   数据库适配器   │   AI生成器      │
│ TemplateEngine  │ DatabaseAdapter │  TDPilot Agent  │
└─────────────────┴─────────────────┴─────────────────┘
    ↓
数据存储 (PostgreSQL)
```

## 功能演示

### 1. 访问SQL生成页面
打开浏览器访问：http://localhost:3000
导航到"SQL脚本生成"页面

### 2. 选择测试用例
- 系统显示所有已完成的测试用例
- 选择需要生成SQL脚本的测试用例
- 查看测试用例的详细信息和场景数量

### 3. 配置生成参数
- **数据库类型**：选择目标数据库（TDSQL-PostgreSQL、TDSQL-Oracle等）
- **AI模型**：选择用于生成的LLM模型
- **生成模式**：选择AI智能生成或模板生成
- **自定义需求**：添加特殊的SQL生成需求

### 4. 生成SQL脚本
- 点击"开始生成SQL脚本"按钮
- 实时查看生成进度
- 系统自动分析测试用例并生成对应的SQL脚本

### 5. 查看和编辑结果
- 在代码编辑器中查看生成的SQL脚本
- 支持语法高亮和代码折叠
- 可以直接编辑和修改SQL内容
- 查看生成统计信息（场景数、表数量等）

### 6. 下载和保存
- 下载SQL脚本文件
- 保存到知识库（开发中）
- 查看生成历史记录

## 生成的SQL脚本示例

```sql
/*
=============================================================================
TDSQL AI测试平台 - 自动生成的SQL测试脚本
=============================================================================
数据库类型: tdsql-pg
生成时间: 2025-07-28 03:24:37
测试场景数量: 2
测试场景列表:
  - 用户管理功能测试
  - 数据查询性能测试

注意事项:
1. 请在测试环境中执行此脚本
2. 执行前请备份重要数据
3. 分布式数据库查询中，有LIMIT的必须包含ORDER BY
4. 建议分段执行，观察执行结果
=============================================================================
*/

-- =============================================================================
-- 测试场景 1: 用户管理功能测试
-- =============================================================================

-- -----------------------------------------------------------------------------
-- TC001: 用户注册功能测试
-- 描述: 测试用户注册的各种场景
-- 类别: 功能
-- -----------------------------------------------------------------------------

-- 创建测试表: test_1_1_功能
CREATE TABLE test_1_1_功能 (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    age INTEGER,
    salary DECIMAL(10,2),
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_test_1_1_功能_name ON test_1_1_功能(name);
CREATE INDEX idx_test_1_1_功能_email ON test_1_1_功能(email);

-- 批量插入测试数据
INSERT INTO test_1_1_功能 (name, email, age, salary, is_active, description) VALUES
    ('张三', '<EMAIL>', 25, 5000.00, TRUE, '测试用户1'),
    ('李四', '<EMAIL>', 30, 6000.00, TRUE, '测试用户2'),
    ('王五', '<EMAIL>', 35, 7000.00, FALSE, '测试用户3'),
    ('赵六', '<EMAIL>', 28, 5500.00, TRUE, '测试用户4'),
    ('钱七', '<EMAIL>', 32, 6500.00, TRUE, '测试用户5');

-- CRUD操作测试
-- 查询操作 (必须包含ORDER BY)
SELECT * FROM test_1_1_功能 ORDER BY id LIMIT 10;

-- 更新操作
UPDATE test_1_1_功能 SET salary = salary * 1.1, updated_at = CURRENT_TIMESTAMP WHERE age > 30;

-- 删除操作
DELETE FROM test_1_1_功能 WHERE age > 30;

-- 边界测试: 极值查询
SELECT MIN(age) as min_age, MAX(age) as max_age, AVG(age) as avg_age
FROM test_1_1_功能;

-- 边界测试: 空值处理
SELECT COUNT(*) as total_count, 
       COUNT(email) as non_null_email_count,
       COUNT(*) - COUNT(email) as null_email_count
FROM test_1_1_功能;

-- =============================================================================
-- 清理脚本 - 删除测试表
-- =============================================================================
-- 注意: 请谨慎执行清理脚本，确保不会影响生产数据

DROP TABLE IF EXISTS test_1_3_性能;
DROP TABLE IF EXISTS test_1_2_功能;
DROP TABLE IF EXISTS test_1_1_功能;

-- 清理完成
SELECT '测试表清理完成' as cleanup_status;
```

## API接口说明

### 1. 生成SQL脚本
```
POST /api/v1/tdpilot/generate-sql
```

### 2. 流式生成SQL脚本
```
POST /api/v1/tdpilot/generate-sql-stream
```

### 3. 获取测试用例列表
```
GET /api/v1/tdpilot/test-cases
```

### 4. 获取SQL生成历史
```
GET /api/v1/tdpilot/sql-generations
```

## 测试验证

### 自动化测试
运行测试脚本验证功能：
```bash
python3 test_sql_generation.py
```

### 测试结果
```
=== TDSQL AI测试平台 - SQL脚本生成功能测试 ===

1. 用户登录...
登录成功!

4. 测试模板SQL生成...
模板SQL生成成功!
生成ID: 3
状态: completed
测试表数量: 3
SQL脚本长度: 5702 字符

5. 测试流式SQL生成...
生成完成!
生成ID: 4
状态: completed
场景数: 2
测试表数量: 3

6. 获取SQL生成历史...
--- SQL生成历史 ---
总数: 4

=== 测试完成 ===
SQL脚本生成功能测试结果:
- 模板生成: 成功 ✅
- 流式生成: 成功 ✅
- 历史查询: 成功 ✅
```

## 部署说明

### Docker部署
```bash
# 构建并启动所有服务
docker-compose up -d

# 运行数据库迁移
docker exec -it tdsql-ai-testing-platform-backend-1 python migrations/add_sql_generation_fields.py

# 验证服务状态
curl -X GET http://localhost:8000/health
```

### 服务访问
- 前端界面：http://localhost:3000
- 后端API：http://localhost:8000
- API文档：http://localhost:8000/docs

## 总结

SQL脚本生成功能已成功开发并部署，具备以下优势：

1. **完整的功能实现**：从需求分析到SQL生成的完整流程
2. **优秀的用户体验**：流式响应、实时预览、代码编辑器集成
3. **强大的技术架构**：模块化设计、多数据库支持、AI+模板双模式
4. **完善的测试验证**：自动化测试、Docker部署验证
5. **良好的扩展性**：支持新数据库类型、新测试场景的扩展

该功能为TDSQL AI测试平台提供了强大的SQL测试脚本生成能力，大大提升了测试效率和质量。
