#!/usr/bin/env python3
"""
测试CORS配置是否正常工作
"""

import requests
import json

def test_cors_preflight():
    """测试CORS预检请求"""
    print("🧪 测试CORS预检请求...")
    
    # 模拟浏览器的预检请求
    headers = {
        'Origin': 'http://************:3000',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type, Authorization'
    }
    
    try:
        response = requests.options('http://localhost:8000/api/v1/auth/login', headers=headers)
        print(f"预检请求状态码: {response.status_code}")
        print("响应头:")
        for key, value in response.headers.items():
            if 'access-control' in key.lower():
                print(f"  {key}: {value}")
        
        if response.status_code == 200:
            print("✅ CORS预检请求成功")
            return True
        else:
            print("❌ CORS预检请求失败")
            return False
            
    except Exception as e:
        print(f"❌ 预检请求异常: {e}")
        return False

def test_login_with_cors():
    """测试带CORS的登录请求"""
    print("\n🔐 测试登录请求...")
    
    headers = {
        'Origin': 'http://************:3000',
        'Content-Type': 'application/json'
    }
    
    data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        response = requests.post(
            'http://localhost:8000/api/v1/auth/login', 
            json=data, 
            headers=headers
        )
        
        print(f"登录请求状态码: {response.status_code}")
        print("响应头:")
        for key, value in response.headers.items():
            if 'access-control' in key.lower():
                print(f"  {key}: {value}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 登录成功")
            print(f"用户: {result.get('user', {}).get('username', 'N/A')}")
            return True
        else:
            print("❌ 登录失败")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 登录请求异常: {e}")
        return False

def test_health_check():
    """测试健康检查端点"""
    print("\n🏥 测试健康检查...")
    
    headers = {
        'Origin': 'http://************:3000'
    }
    
    try:
        response = requests.get('http://localhost:8000/health', headers=headers)
        print(f"健康检查状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 健康检查成功")
            print(f"应用: {result.get('app_name', 'N/A')}")
            print(f"版本: {result.get('version', 'N/A')}")
            return True
        else:
            print("❌ 健康检查失败")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def main():
    """主函数"""
    print("🌐 CORS配置测试")
    print("=" * 50)
    
    # 测试健康检查
    health_ok = test_health_check()
    
    # 测试CORS预检
    preflight_ok = test_cors_preflight()
    
    # 测试登录
    login_ok = test_login_with_cors()
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"  健康检查: {'✅ 通过' if health_ok else '❌ 失败'}")
    print(f"  CORS预检: {'✅ 通过' if preflight_ok else '❌ 失败'}")
    print(f"  登录测试: {'✅ 通过' if login_ok else '❌ 失败'}")
    
    if all([health_ok, preflight_ok, login_ok]):
        print("\n🎉 所有测试通过！CORS配置正常")
        print("现在您应该可以通过 http://************:3000 正常登录了")
    else:
        print("\n⚠️  部分测试失败，请检查后端服务状态")
        print("确保后端服务正在运行: python -m uvicorn app.main:app --host 0.0.0.0 --port 8000")

if __name__ == "__main__":
    main()
