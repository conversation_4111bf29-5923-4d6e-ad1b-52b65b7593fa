#!/usr/bin/env python3
"""
测试可编辑分析结果功能
"""

import requests
import json
import sys

# 配置
BASE_URL = "http://localhost:8000/api/v1"
USERNAME = "admin"
PASSWORD = "admin123"

def login():
    """登录获取token"""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 登录成功，用户: {result['user']['username']}")
        return result["access_token"]
    else:
        print(f"❌ 登录失败: {response.text}")
        return None

def create_test_analysis(token):
    """创建一个测试分析"""
    headers = {"Authorization": f"Bearer {token}"}
    
    analysis_data = {
        "title": "测试编辑功能",
        "requirement_text": "测试需求分析结果的编辑功能",
        "background_knowledge": "这是一个测试",
        "database_type": "tdsql-pg",
        "llm_model_id": 7
    }
    
    print("🚀 创建测试分析...")
    response = requests.post(f"{BASE_URL}/tdpilot/analyze-requirement", 
                           json=analysis_data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        analysis_id = result["id"]
        print(f"✅ 分析创建成功，ID: {analysis_id}")
        return analysis_id
    else:
        print(f"❌ 分析创建失败: {response.text}")
        return None

def test_update_analysis(token, analysis_id):
    """测试更新分析结果"""
    headers = {"Authorization": f"Bearer {token}"}
    
    # 准备更新数据
    update_data = {
        "analysis_id": analysis_id,
        "test_scenarios": [
            {
                "scenario": "用户登录场景",
                "description": "测试用户登录相关功能",
                "test_points": ["用户名登录", "邮箱登录", "密码验证"]
            },
            {
                "scenario": "数据管理场景", 
                "description": "测试数据CRUD操作",
                "test_points": ["数据创建", "数据查询", "数据更新", "数据删除"]
            }
        ],
        "test_points": [
            "用户名登录",
            "邮箱登录", 
            "密码验证",
            "数据创建",
            "数据查询",
            "数据更新",
            "数据删除"
        ],
        "recommendations": [
            "建议进行边界值测试",
            "建议进行并发测试",
            "建议进行安全测试"
        ]
    }
    
    print("🔄 测试更新分析结果...")
    response = requests.put(f"{BASE_URL}/tdpilot/analysis/{analysis_id}", 
                          json=update_data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 分析结果更新成功")
        
        # 验证更新结果
        print(f"📊 更新后的统计:")
        print(f"   - 测试场景数: {len(result['test_scenarios'])}")
        print(f"   - 测试点数: {len(result['test_points'])}")
        print(f"   - 建议数: {len(result['recommendations'])}")
        
        return True
    else:
        print(f"❌ 更新失败: {response.text}")
        return False

def test_get_analysis(token, analysis_id):
    """测试获取分析结果"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("📖 测试获取分析结果...")
    response = requests.get(f"{BASE_URL}/tdpilot/analysis/{analysis_id}", 
                          headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 获取分析结果成功")
        
        print(f"📋 分析结果详情:")
        print(f"   - ID: {result['id']}")
        print(f"   - 状态: {result['status']}")
        print(f"   - 测试场景数: {len(result.get('test_scenarios', []))}")
        print(f"   - 测试点数: {len(result.get('test_points', []))}")
        print(f"   - 建议数: {len(result.get('recommendations', []))}")
        
        # 显示具体内容
        if result.get('test_scenarios'):
            print("\n🎭 测试场景:")
            for i, scenario in enumerate(result['test_scenarios'], 1):
                print(f"   {i}. {scenario.get('scenario', 'N/A')}")
                print(f"      描述: {scenario.get('description', 'N/A')}")
                if scenario.get('test_points'):
                    print(f"      测试点: {', '.join(scenario['test_points'])}")
        
        return True
    else:
        print(f"❌ 获取失败: {response.text}")
        return False

def main():
    """主函数"""
    print("🧪 测试可编辑分析结果功能")
    print("=" * 50)
    
    # 登录
    token = login()
    if not token:
        sys.exit(1)
    
    # 创建测试分析
    analysis_id = create_test_analysis(token)
    if not analysis_id:
        sys.exit(1)
    
    # 等待分析完成（简化处理，实际应该轮询状态）
    import time
    print("⏳ 等待分析完成...")
    time.sleep(5)
    
    # 测试更新功能
    update_success = test_update_analysis(token, analysis_id)
    if not update_success:
        sys.exit(1)
    
    # 测试获取功能
    get_success = test_get_analysis(token, analysis_id)
    if not get_success:
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！可编辑分析结果功能正常工作")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
