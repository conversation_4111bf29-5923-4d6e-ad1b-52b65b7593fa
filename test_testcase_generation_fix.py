#!/usr/bin/env python3
"""
测试用例生成修复效果验证脚本
验证5个场景25个测试点是否能生成至少50个测试用例
"""

import requests
import json
import time
import sys

# 配置
BASE_URL = "http://localhost:8000/api/v1"
USERNAME = "admin"
PASSWORD = "admin123"

def login():
    """登录获取token"""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 登录成功，用户: {result['user']['username']}")
        return result["access_token"]
    else:
        print(f"❌ 登录失败: {response.text}")
        return None

def create_requirement_analysis(token):
    """创建需求分析，生成5个场景25个测试点的数据"""
    headers = {"Authorization": f"Bearer {token}"}
    
    # 构造一个复杂的需求，确保能生成5个场景25个测试点
    analysis_data = {
        "title": "复杂用户管理系统测试分析",
        "requirement_text": """
需要测试一个复杂的用户管理系统，包含以下功能模块：

1. 用户注册模块：用户注册、邮箱验证、手机验证、密码强度检查、重复注册检查
2. 用户登录模块：用户名登录、邮箱登录、手机登录、第三方登录、登录失败锁定
3. 用户信息管理：个人信息修改、头像上传、密码修改、账号注销、数据导出
4. 权限管理模块：角色分配、权限控制、资源访问、操作审计、权限继承
5. 系统安全模块：SQL注入防护、XSS防护、CSRF防护、数据加密、访问日志

每个模块都需要进行功能测试、性能测试、安全测试、边界测试和异常测试。
系统使用PostgreSQL数据库，需要支持高并发访问和数据一致性。
        """,
        "background_knowledge": "这是一个基于微服务架构的用户管理系统，支持多租户、多角色权限控制，需要保证数据安全和系统稳定性。",
        "database_type": "tdsql-pg",
        "llm_model_id": 7
    }
    
    print("🚀 开始需求分析...")
    response = requests.post(f"{BASE_URL}/tdpilot/analyze-requirement", 
                           json=analysis_data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        analysis_id = result["id"]
        print(f"✅ 需求分析创建成功，ID: {analysis_id}")
        
        # 等待分析完成
        print("⏳ 等待需求分析完成...")
        max_wait = 60  # 最多等待60秒
        wait_time = 0
        
        while wait_time < max_wait:
            check_response = requests.get(f"{BASE_URL}/tdpilot/requirement-analysis/{analysis_id}", 
                                        headers=headers)
            if check_response.status_code == 200:
                analysis_result = check_response.json()
                if analysis_result["status"] == "completed":
                    print("✅ 需求分析完成")
                    
                    # 统计测试场景和测试点数量
                    scenarios = analysis_result.get("test_scenarios", [])
                    total_test_points = 0
                    for scenario in scenarios:
                        test_points = scenario.get("test_points", [])
                        total_test_points += len(test_points)
                    
                    print(f"📊 分析结果统计:")
                    print(f"   - 测试场景数量: {len(scenarios)}")
                    print(f"   - 测试点总数: {total_test_points}")
                    print(f"   - 预期测试用例数: {total_test_points * 2}")
                    
                    return analysis_id, len(scenarios), total_test_points
                elif analysis_result["status"] == "failed":
                    print(f"❌ 需求分析失败: {analysis_result.get('error_message', '未知错误')}")
                    return None, 0, 0
            
            time.sleep(2)
            wait_time += 2
            print(f"⏳ 等待中... ({wait_time}s)")
        
        print("❌ 需求分析超时")
        return None, 0, 0
    else:
        print(f"❌ 需求分析创建失败: {response.text}")
        return None, 0, 0

def test_testcase_generation(token, analysis_id, expected_scenarios, expected_test_points):
    """测试测试用例生成"""
    headers = {"Authorization": f"Bearer {token}"}
    
    testcase_data = {
        "analysis_result_id": analysis_id,
        "llm_model_id": 7,
        "custom_requirements": "请确保为每个测试点生成至少2个不同类型的测试用例，包括正向测试和边界/异常测试"
    }
    
    print("🚀 开始测试用例生成...")
    response = requests.post(f"{BASE_URL}/tdpilot/generate-testcases", 
                           json=testcase_data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        test_case_id = result["id"]
        print(f"✅ 测试用例生成任务创建成功，ID: {test_case_id}")
        
        # 等待生成完成
        print("⏳ 等待测试用例生成完成...")
        max_wait = 120  # 最多等待120秒
        wait_time = 0
        
        while wait_time < max_wait:
            check_response = requests.get(f"{BASE_URL}/tdpilot/test-case/{test_case_id}", 
                                        headers=headers)
            if check_response.status_code == 200:
                test_case_result = check_response.json()
                if test_case_result["status"] == "completed":
                    print("✅ 测试用例生成完成")
                    
                    # 分析生成结果
                    test_scenarios = test_case_result.get("test_scenarios", [])
                    actual_scenarios = len(test_scenarios)
                    actual_test_points = 0
                    actual_test_cases = 0
                    
                    for scenario in test_scenarios:
                        test_points = scenario.get("test_points", [])
                        actual_test_points += len(test_points)
                        for point in test_points:
                            test_cases = point.get("test_cases", [])
                            actual_test_cases += len(test_cases)
                    
                    # 输出详细统计
                    print(f"\n📊 测试用例生成结果统计:")
                    print(f"   预期场景数: {expected_scenarios}, 实际场景数: {actual_scenarios}")
                    print(f"   预期测试点数: {expected_test_points}, 实际测试点数: {actual_test_points}")
                    print(f"   预期测试用例数: {expected_test_points * 2}, 实际测试用例数: {actual_test_cases}")
                    
                    # 检查是否达到预期
                    success = True
                    if actual_scenarios < expected_scenarios:
                        print(f"⚠️  场景数量不足，缺少 {expected_scenarios - actual_scenarios} 个场景")
                        success = False
                    
                    if actual_test_points < expected_test_points:
                        print(f"⚠️  测试点数量不足，缺少 {expected_test_points - actual_test_points} 个测试点")
                        success = False
                    
                    expected_test_cases = expected_test_points * 2
                    if actual_test_cases < expected_test_cases:
                        print(f"⚠️  测试用例数量不足，缺少 {expected_test_cases - actual_test_cases} 个测试用例")
                        success = False
                    
                    if success:
                        print("🎉 测试用例生成数量符合预期！")
                    else:
                        print("❌ 测试用例生成数量不符合预期")
                    
                    # 检查summary信息
                    summary = test_case_result.get("summary", {})
                    if summary:
                        print(f"\n📋 Summary信息:")
                        print(f"   - 预期测试点: {summary.get('expected_test_points', 'N/A')}")
                        print(f"   - 预期测试用例: {summary.get('expected_test_cases', 'N/A')}")
                        print(f"   - 实际测试点: {summary.get('actual_test_points', 'N/A')}")
                        print(f"   - 实际测试用例: {summary.get('actual_test_cases', 'N/A')}")
                        print(f"   - 生成完整性: {summary.get('generation_complete', 'N/A')}")
                        print(f"   - 缺少数量: {summary.get('shortage', 'N/A')}")
                    
                    return success, actual_scenarios, actual_test_points, actual_test_cases
                    
                elif test_case_result["status"] == "failed":
                    print(f"❌ 测试用例生成失败: {test_case_result.get('error_message', '未知错误')}")
                    return False, 0, 0, 0
            
            time.sleep(3)
            wait_time += 3
            print(f"⏳ 等待中... ({wait_time}s)")
        
        print("❌ 测试用例生成超时")
        return False, 0, 0, 0
    else:
        print(f"❌ 测试用例生成失败: {response.text}")
        return False, 0, 0, 0

def main():
    """主函数"""
    print("🧪 开始测试用例生成修复效果验证")
    print("=" * 60)
    
    # 登录
    token = login()
    if not token:
        sys.exit(1)
    
    # 创建需求分析
    analysis_id, expected_scenarios, expected_test_points = create_requirement_analysis(token)
    if not analysis_id:
        sys.exit(1)
    
    # 测试用例生成
    success, actual_scenarios, actual_test_points, actual_test_cases = test_testcase_generation(
        token, analysis_id, expected_scenarios, expected_test_points
    )
    
    # 最终结果
    print("\n" + "=" * 60)
    if success:
        print("🎉 修复验证成功！测试用例生成数量符合预期")
    else:
        print("❌ 修复验证失败！测试用例生成数量仍然不足")
    
    print(f"📊 最终统计:")
    print(f"   - 场景数: {actual_scenarios}")
    print(f"   - 测试点数: {actual_test_points}")
    print(f"   - 测试用例数: {actual_test_cases}")
    print(f"   - 预期最少用例数: {expected_test_points * 2}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
