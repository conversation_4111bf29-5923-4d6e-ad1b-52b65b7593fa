# LLM模型配置指南

本指南将帮助您配置和使用TDSQL AI测试平台支持的各种LLM模型。

## 支持的LLM提供商

### 1. OpenAI

**配置参数:**
- **模型名称**: `gpt-4`, `gpt-4-turbo`, `gpt-3.5-turbo`, `gpt-4o`, `gpt-4o-mini`
- **Base URL**: `https://api.openai.com/v1`
- **API Key**: 从 [OpenAI官网](https://platform.openai.com/api-keys) 获取

**推荐配置:**
```
模型名称: gpt-4
Base URL: https://api.openai.com/v1
API Key: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Max Tokens: 32768
Temperature: 0.7
```

### 2. Anthropic Claude

**配置参数:**
- **模型名称**: `claude-3-sonnet-20240229`, `claude-3-opus-20240229`, `claude-3-haiku-20240307`, `claude-3-5-sonnet-20241022`
- **Base URL**: `https://api.anthropic.com`
- **API Key**: 从 [Anthropic官网](https://console.anthropic.com/) 获取

**推荐配置:**
```
模型名称: claude-3-sonnet-20240229
Base URL: https://api.anthropic.com
API Key: sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Max Tokens: 32768
Temperature: 0.7
```

### 3. Google Gemini

**配置参数:**
- **模型名称**: `gemini-pro`, `gemini-pro-vision`, `gemini-1.5-pro`, `gemini-1.5-flash`
- **Base URL**: `https://generativelanguage.googleapis.com`
- **API Key**: 从 [Google AI Studio](https://makersuite.google.com/app/apikey) 获取

**推荐配置:**
```
模型名称: gemini-pro
Base URL: https://generativelanguage.googleapis.com
API Key: AIzaSyxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Max Tokens: 32768
Temperature: 0.7
```

### 4. DeepSeek

**配置参数:**
- **模型名称**: `deepseek-chat`, `deepseek-coder`, `deepseek-v2-chat`, `deepseek-v2-coder`
- **Base URL**: `https://api.deepseek.com`
- **API Key**: 从 [DeepSeek官网](https://platform.deepseek.com/api_keys) 获取

**推荐配置:**
```
模型名称: deepseek-chat
Base URL: https://api.deepseek.com
API Key: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
Max Tokens: 32768
Temperature: 0.7
```

### 5. Ollama (本地部署)

**配置参数:**
- **模型名称**: `llama2`, `llama3`, `codellama`, `mistral`, `qwen`, `gemma`
- **Base URL**: `http://localhost:11434` (默认)
- **API Key**: 不需要

**安装和配置:**
1. 安装Ollama: `curl -fsSL https://ollama.ai/install.sh | sh`
2. 下载模型: `ollama pull llama2`
3. 启动服务: `ollama serve`

**推荐配置:**
```
模型名称: llama2
Base URL: http://localhost:11434
Max Tokens: 32768
Temperature: 0.7
```

### 6. 自定义模型 (OpenAI兼容)

**配置参数:**
- **模型名称**: 根据您的模型设置
- **Base URL**: 您的API端点地址
- **API Key**: 您的API密钥

**推荐配置:**
```
模型名称: your-custom-model
Base URL: https://your-api-endpoint.com/v1
API Key: your-api-key
Max Tokens: 32768
Temperature: 0.7
```

## 配置步骤

### 方法1: 使用配置向导 (推荐)

1. 进入"LLM模型配置"页面
2. 点击"配置向导"按钮
3. 选择您要使用的提供商
4. 填写配置参数 (系统会自动填入默认值)
5. 测试连接确保配置正确
6. 完成配置

### 方法2: 快速添加

1. 进入"LLM模型配置"页面
2. 点击"快速添加"按钮
3. 手动填写所有配置参数
4. 保存配置

## 配置参数说明

### 通用参数

- **配置名称**: 为这个配置起一个便于识别的名字
- **提供商**: 选择LLM服务提供商
- **模型名称**: 具体的模型标识符
- **Base URL**: API端点地址
- **API Key**: 访问API所需的密钥
- **Max Tokens**: 单次请求的最大token数量 (建议: 32768)
- **Temperature**: 控制输出的随机性 (0-2, 建议: 0.7)

### 参数调优建议

**Temperature设置:**
- `0.0-0.3`: 更确定性的输出，适合需要精确结果的任务
- `0.4-0.7`: 平衡创造性和准确性，适合大多数场景
- `0.8-1.0`: 更有创造性的输出，适合需要多样性的任务

**Max Tokens设置:**
- 需求分析: 32768-8000
- 测试用例生成: 32768-6000  
- SQL脚本生成: 2000-32768

## 常见问题

### Q: API密钥如何获取？
A: 每个提供商都有自己的官网控制台，注册账号后可以生成API密钥。

### Q: 如何测试配置是否正确？
A: 在配置向导的最后一步，或者在模型列表中点击"测试"按钮。

### Q: 可以同时配置多个模型吗？
A: 可以，系统支持配置多个不同的模型，您可以根据需要选择使用。

### Q: Ollama模型如何安装？
A: 参考Ollama官方文档，先安装Ollama，然后下载所需的模型。

### Q: 自定义模型需要什么格式？
A: 需要兼容OpenAI的API格式，支持`/chat/completions`端点。

## 最佳实践

1. **多模型配置**: 建议配置2-3个不同的模型作为备选
2. **定期测试**: 定期测试模型连接，确保服务正常
3. **参数调优**: 根据实际使用效果调整Temperature和Max Tokens
4. **成本控制**: 注意API调用成本，合理设置Max Tokens
5. **安全性**: 妥善保管API密钥，不要泄露给他人

## 故障排除

### 连接失败
- 检查网络连接
- 验证API密钥是否正确
- 确认Base URL是否正确
- 检查API配额是否用完

### 响应异常
- 检查模型名称是否正确
- 调整Temperature和Max Tokens参数
- 查看错误日志获取详细信息

### 性能问题
- 选择更快的模型 (如GPT-3.5-turbo)
- 减少Max Tokens设置
- 考虑使用本地部署的Ollama模型
