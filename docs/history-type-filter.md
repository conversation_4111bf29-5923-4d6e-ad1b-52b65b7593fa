# 历史记录类型筛选功能

## 功能概述

历史记录现在支持按类型进行筛选，方便用户快速查找特定类型的历史记录。

## 支持的筛选类型

1. **全部** - 显示所有类型的历史记录（默认）
2. **需求分析** - 只显示需求分析记录
3. **测试用例生成** - 只显示测试用例生成记录
4. **SQL生成** - 只显示SQL生成记录

## 使用方法

### 前端界面

1. 进入测试生成页面
2. 点击"历史记录"标签页
3. 在历史记录表格上方找到"类型筛选"下拉框
4. 选择要筛选的类型，或点击清除按钮显示全部记录

### API接口

后端API `/api/v1/tdpilot/history` 现在支持 `type_filter` 查询参数：

```bash
# 获取所有历史记录
GET /api/v1/tdpilot/history

# 只获取需求分析记录
GET /api/v1/tdpilot/history?type_filter=requirement_analysis

# 只获取测试用例生成记录
GET /api/v1/tdpilot/history?type_filter=test_case_generation

# 只获取SQL生成记录
GET /api/v1/tdpilot/history?type_filter=sql_generation
```

## 实现细节

### 后端实现

- 在 `get_generation_history` 函数中添加了 `type_filter` 参数
- 根据筛选类型决定查询哪些数据表
- 保持原有的分页和排序功能

### 前端实现

- 添加了 `historyTypeFilter` 状态管理筛选器值
- 在历史记录Card中添加了类型筛选下拉框
- 更新了 `fetchHistory` 函数支持传递筛选参数
- 删除记录后保持当前筛选状态

## 注意事项

1. 筛选状态在删除记录后会保持，确保用户体验的连续性
2. 切换到历史记录标签页时会应用当前的筛选设置
3. 筛选器支持清除功能，可以快速回到显示全部记录的状态
