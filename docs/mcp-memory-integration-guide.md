# MCP与记忆管理集成指南

本文档详细说明了TDSQL AI测试平台中MCP服务与记忆管理系统的完整实现。

## 🔧 MCP服务集成

### 核心功能
MCP (Multi-Connection Protocol) 服务为TDPilot AI智能体提供了与外部数据库交互的能力，实现了：

1. **SQL执行与结果反馈**
2. **基于执行结果的SQL优化**
3. **多轮迭代优化机制**
4. **错误处理与重试逻辑**

### 工作流程

```mermaid
graph TD
    A[用户提交需求] --> B[TDPilot分析需求]
    B --> C[生成测试用例]
    C --> D[生成SQL脚本]
    D --> E[MCP执行SQL]
    E --> F{执行成功?}
    F -->|否| G[分析错误原因]
    G --> H[LLM优化SQL]
    H --> E
    F -->|是| I[检查性能]
    I --> J{性能满意?}
    J -->|否| G
    J -->|是| K[完成]
```

### API端点

#### 1. 执行并优化SQL
```http
POST /api/v1/tdpilot/execute-and-optimize-sql
```

**请求参数:**
```json
{
    "sql_generation_id": 1,
    "database_connection_id": 1,
    "database_type": "tdsql-pg",
    "max_iterations": 3
}
```

**响应示例:**
```json
{
    "success": true,
    "final_sql": "优化后的SQL脚本",
    "execution_result": {
        "success": true,
        "execution_time": 0.15,
        "rows_affected": 100
    },
    "optimization_history": [
        {
            "iteration": 1,
            "sql": "原始SQL",
            "result": {...}
        }
    ],
    "iterations": 2
}
```

### 优化策略

TDPilot智能体会基于以下因素进行SQL优化：

1. **语法错误修复**
2. **性能优化** (执行时间 > 10秒)
3. **成功率提升** (成功率 < 80%)
4. **分布式查询优化**

## 🧠 记忆管理系统

### 三层记忆架构

1. **短期记忆** (Redis)
   - 当前会话的对话历史
   - 临时上下文信息
   - 过期时间: 30天

2. **长期记忆** (Milvus向量数据库)
   - 重要对话内容的向量化存储
   - 语义搜索和相似度匹配
   - 永久存储

3. **工作记忆** (内存)
   - 当前任务的上下文
   - SQL执行历史
   - 实时状态信息

### 记忆存储策略

#### 重要消息判断
系统会自动判断哪些消息需要存储到长期记忆：

```python
def _is_important_message(content, role):
    important_keywords = [
        "需求分析", "测试用例", "SQL生成", 
        "数据库", "性能", "优化", "错误", 
        "问题", "解决方案", "建议"
    ]
    
    # 用户消息或包含重要关键词的助手消息
    if role == "user" or (role == "assistant" and 
        any(keyword in content for keyword in important_keywords)):
        return len(content) > 50
    
    return False
```

#### 向量化存储
重要消息会被转换为384维向量并存储到Milvus：

```python
# 生成嵌入向量
embedding = embedding_model.encode([content])[0].tolist()

# 存储到Milvus
data = [
    [memory_id],    # 记忆ID
    [user_id],      # 用户ID
    [session_id],   # 会话ID
    [content],      # 内容
    [role],         # 角色类型
    [embedding],    # 向量
    [timestamp]     # 时间戳
]
```

### 上下文增强对话

TDPilot智能体在生成响应时会自动获取相关上下文：

```python
async def _generate_with_memory_context(self, prompt, query, task_type):
    # 获取上下文记忆
    context = await memory_service.get_context_for_llm(
        user_id=self.user_id,
        session_id=self.session_id,
        current_query=query
    )
    
    # 构建包含记忆的完整提示
    memory_context = self._build_memory_context(context)
    full_prompt = f"{memory_context}\n\n{prompt}"
    
    # 生成响应
    result = await llm_service.generate_text(
        self.model_config, full_prompt
    )
```

### API端点

#### 1. 创建会话
```http
POST /api/v1/memory/sessions
```

#### 2. 添加消息
```http
POST /api/v1/memory/sessions/{session_id}/messages
```

#### 3. 搜索记忆
```http
POST /api/v1/memory/search
```

#### 4. 获取会话消息
```http
GET /api/v1/memory/sessions/{session_id}/messages
```

## 🔄 集成工作流程

### 完整的AI对话流程

1. **会话创建**
   ```python
   session_id = await tdpilot_service.create_session(
       user_id=user_id,
       session_name="TDSQL-PG测试用例生成"
   )
   ```

2. **需求分析** (带记忆增强)
   ```python
   result = await tdpilot_service.analyze_requirement(
       db=db,
       user_id=user_id,
       requirement_text="用户管理系统测试需求",
       session_id=session_id  # 关键：传入会话ID
   )
   ```

3. **SQL执行与优化**
   ```python
   optimization_result = await agent.execute_and_optimize_sql(
       sql_content=generated_sql,
       database_config=db_config,
       database_type="tdsql-pg",
       max_iterations=3
   )
   ```

4. **记忆存储**
   - 用户查询自动存储
   - 重要响应自动向量化
   - 执行结果记录到工作记忆

### 记忆检索示例

当用户询问类似问题时，系统会自动检索相关记忆：

```python
# 用户新查询: "如何优化SQL查询性能？"
relevant_memories = await memory_service.search_memory(
    user_id=user_id,
    query="如何优化SQL查询性能？",
    limit=5
)

# 返回相关的历史对话和解决方案
```

## 🚀 使用示例

### 1. 完整的测试用例生成流程

```python
# 1. 创建会话
session_id = await create_session("用户管理系统测试")

# 2. 分析需求 (自动记录到记忆)
analysis = await analyze_requirement(
    requirement_text="需要测试用户CRUD操作",
    session_id=session_id
)

# 3. 生成测试用例 (使用历史记忆)
test_cases = await generate_test_cases(
    analysis_id=analysis["id"],
    session_id=session_id
)

# 4. 生成SQL (基于上下文)
sql_result = await generate_sql_scripts(
    test_case_id=test_cases["id"],
    database_type="tdsql-pg"
)

# 5. 执行并优化SQL (MCP集成)
execution_result = await execute_and_optimize_sql(
    sql_generation_id=sql_result["id"],
    database_connection_id=1,
    max_iterations=3
)
```

### 2. 记忆搜索示例

```python
# 搜索相关的历史经验
memories = await search_memory(
    query="TDSQL-PG性能优化",
    limit=10
)

# 结果包含:
# - 历史的性能优化建议
# - 相关的SQL优化案例
# - 用户的反馈和评价
```

## 📊 性能监控

系统会自动记录以下指标：

1. **SQL执行性能**
   - 执行时间
   - 成功率
   - 优化迭代次数

2. **记忆系统性能**
   - 向量搜索响应时间
   - 记忆检索准确率
   - 存储空间使用情况

3. **AI对话质量**
   - 响应时间
   - Token使用量
   - 用户满意度

## 🔧 配置说明

### 环境变量
```bash
# Redis配置
REDIS_URL=redis://localhost:6379/0

# Milvus配置
MILVUS_HOST=localhost
MILVUS_PORT=19530

# 记忆管理配置
MEMORY_RETENTION_DAYS=30
MAX_MEMORY_SEARCH_RESULTS=10
EMBEDDING_MODEL=all-MiniLM-L6-v2
```

### 数据库表
系统会自动创建以下数据库表：
- `database_connections` - 数据库连接配置
- `sql_execution_logs` - SQL执行日志
- `memory_sessions` - 记忆会话 (Redis)
- `memory_vectors` - 向量记忆 (Milvus)

## 🎯 最佳实践

1. **会话管理**
   - 为每个项目创建独立会话
   - 定期清理过期会话
   - 合理设置会话名称

2. **记忆优化**
   - 重要信息手动标记
   - 定期清理无用记忆
   - 监控存储空间使用

3. **SQL优化**
   - 设置合理的迭代次数
   - 监控执行性能
   - 及时处理执行错误

这个集成系统为TDSQL AI测试平台提供了强大的智能化能力，通过MCP服务实现了真正的"测试-执行-优化"闭环，通过记忆管理实现了上下文感知的智能对话。
