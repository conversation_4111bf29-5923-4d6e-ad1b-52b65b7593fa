#!/usr/bin/env python3
"""
测试改进后的测试用例生成功能，包括调试信息
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1"

def register_and_login():
    """注册并登录用户"""
    # 注意：当前API使用硬编码的用户凭据
    # 注册用户（虽然不会真正创建用户，但测试API）
    register_data = {
        "username": f"testuser_{int(time.time())}",
        "email": f"test_{int(time.time())}@example.com",
        "password": "testpassword123"
    }

    register_response = requests.post(f"{BASE_URL}/auth/register", json=register_data)
    if register_response.status_code != 200:
        print(f"注册失败: {register_response.text}")
        # 继续尝试登录，因为注册可能只是模拟的
    else:
        print(f"注册响应: {register_response.json()}")

    # 使用硬编码的管理员凭据登录
    login_data = {
        "username": "admin",
        "password": "admin123"
    }

    login_response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if login_response.status_code == 200:
        token = login_response.json()["access_token"]
        print(f"✅ 登录成功，获得token")
        return {"Authorization": f"Bearer {token}"}
    else:
        print(f"❌ 登录失败: {login_response.text}")
        return None

def check_llm_models(headers):
    """检查可用的LLM模型"""
    response = requests.get(f"{BASE_URL}/llm/models", headers=headers)
    if response.status_code == 200:
        models = response.json()
        if models:
            print(f"找到 {len(models)} 个LLM模型")
            for model in models:
                print(f"  - {model['name']} (ID: {model['id']}, 状态: {model['status']})")
            return models[0]["id"]
        else:
            print("没有找到LLM模型")
            return None
    else:
        print(f"获取LLM模型失败: {response.text}")
        return None

def test_requirement_analysis(headers, model_id):
    """测试需求分析"""
    analysis_data = {
        "title": "用户管理系统测试分析",
        "requirement_text": "需要测试一个用户管理系统，包括用户注册、登录、信息修改、权限管理等功能。系统使用PostgreSQL数据库存储用户信息。",
        "background_knowledge": "这是一个基于Web的用户管理系统，支持多角色权限控制。",
        "database_type": "tdsql-pg",
        "llm_model_id": model_id
    }
    
    response = requests.post(f"{BASE_URL}/tdpilot/analyze-requirement", 
                           json=analysis_data, headers=headers)
    
    print(f"需求分析响应状态: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"分析ID: {result['id']}")
        print(f"状态: {result['status']}")
        return result["id"]
    else:
        print(f"需求分析失败-10: {response.text}")
        return None

def test_testcase_generation_with_debug(headers, analysis_id, model_id):
    """测试测试用例生成并获取调试信息"""
    testcase_data = {
        "analysis_result_id": analysis_id,
        "llm_model_id": model_id,
        "custom_requirements": "请特别关注数据安全和并发访问的测试场景"
    }
    
    print("\n🚀 开始生成测试用例...")
    response = requests.post(f"{BASE_URL}/tdpilot/generate-testcases", 
                           json=testcase_data, headers=headers)
    
    print(f"测试用例生成响应状态: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        testcase_id = result["id"]
        print(f"✅ 测试用例生成成功，ID: {testcase_id}")
        print(f"状态: {result['status']}")
        
        # 获取基本信息
        print(f"\n📊 测试用例基本信息:")
        if result.get('test_scenarios'):
            print(f"  - 生成的测试场景数量: {len(result['test_scenarios'])}")
        else:
            print(f"  - 没有生成测试场景")
        
        # 获取调试信息
        print(f"\n🔍 获取调试信息...")
        debug_response = requests.get(f"{BASE_URL}/tdpilot/testcase/{testcase_id}/debug", 
                                    headers=headers)
        
        if debug_response.status_code == 200:
            debug_info = debug_response.json()
            print(f"📋 调试信息:")
            print(f"  - 状态: {debug_info['status']}")
            print(f"  - 错误消息: {debug_info.get('error_message', '无')}")
            print(f"  - 测试场景数量: {debug_info['test_scenarios_count']}")
            print(f"  - 有可视化数据: {debug_info['has_visualization_data']}")
            print(f"  - 提示词长度: {debug_info.get('prompt_length', 0)} 字符")
            print(f"  - LLM原始输出长度: {debug_info.get('llm_raw_output_length', 0)} 字符")
            print(f"  - LLM推理内容长度: {debug_info.get('llm_reasoning_length', 0)} 字符")
            print(f"  - JSON解析错误: {debug_info.get('json_parse_error', '无')}")
            
            # 如果有JSON解析错误，获取原始输出
            if debug_info.get('json_parse_error'):
                print(f"\n⚠️ 检测到JSON解析错误，获取原始输出...")
                raw_response = requests.get(f"{BASE_URL}/tdpilot/testcase/{testcase_id}/raw-output", 
                                          headers=headers)
                
                if raw_response.status_code == 200:
                    raw_data = raw_response.json()
                    print(f"🔍 LLM原始输出:")
                    print(f"  - 输出长度: {len(raw_data['llm_raw_output'])} 字符")
                    print(f"  - 前500字符: {raw_data['llm_raw_output'][:500]}...")
                    
                    if raw_data.get('llm_reasoning'):
                        print(f"  - 推理内容: {raw_data['llm_reasoning'][:200]}...")
                    
                    print(f"  - JSON解析错误详情: {raw_data['json_parse_error']}")
                else:
                    print(f"❌ 获取原始输出失败: {raw_response.text}")
        else:
            print(f"❌ 获取调试信息失败: {debug_response.text}")
        
        return testcase_id
    else:
        print(f"❌ 测试用例生成失败: {response.text}")
        return None

def main():
    """主测试函数"""
    print("🧪 开始测试改进后的测试用例生成功能...")

    # 1. 登录获取token
    print("\n1️⃣ 用户登录...")
    headers = register_and_login()
    if not headers:
        print("❌ 登录失败，无法继续测试")
        return

    # 2. 检查LLM模型
    print("\n2️⃣ 检查LLM模型配置...")
    model_id = check_llm_models(headers)
    if not model_id:
        print("❌ 没有可用的LLM模型，请先配置模型")
        return

    # 3. 测试需求分析
    print("\n3️⃣ 测试需求分析...")
    analysis_id = test_requirement_analysis(headers, model_id)
    if not analysis_id:
        print("❌ 需求分析失败，无法继续测试")
        return

    # 4. 测试测试用例生成（带调试信息）
    print("\n4️⃣ 测试测试用例生成（带调试信息）...")
    testcase_id = test_testcase_generation_with_debug(headers, analysis_id, model_id)
    
    if testcase_id:
        print(f"\n✅ 测试完成！测试用例ID: {testcase_id}")
        print(f"💡 提示：如果发现问题，请查看上面的调试信息来定位原因")
    else:
        print(f"\n❌ 测试失败")

if __name__ == '__main__':
    main()
