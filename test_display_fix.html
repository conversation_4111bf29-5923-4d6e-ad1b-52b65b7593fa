<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试数据显示修复</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-case {
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            background: #fafafa;
        }
        .scenario {
            border: 1px solid #d9f7be;
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
            background: #f6ffed;
        }
        .test-point {
            background: #e6f7ff;
            padding: 4px 8px;
            margin: 2px 4px;
            border-radius: 4px;
            display: inline-block;
        }
        .tag {
            background: #1890ff;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-right: 8px;
        }
        .recommendation {
            background: #fff7e6;
            border-left: 4px solid #fa8c16;
            padding: 8px 12px;
            margin: 4px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 需求分析结果显示测试</h1>
    
    <div class="test-case">
        <h2>测试数据结构 1：标准格式</h2>
        <div id="test1"></div>
    </div>
    
    <div class="test-case">
        <h2>测试数据结构 2：缺少字段</h2>
        <div id="test2"></div>
    </div>
    
    <div class="test-case">
        <h2>测试数据结构 3：空数据</h2>
        <div id="test3"></div>
    </div>

    <script>
        // 模拟不同的数据结构
        const testData1 = {
            test_scenarios: [
                {
                    scenario: "用户登录场景",
                    description: "测试用户登录相关功能",
                    test_points: ["用户名登录", "邮箱登录", "密码验证"]
                },
                {
                    scenario: "数据管理场景",
                    description: "测试数据CRUD操作",
                    test_points: ["数据创建", "数据查询", "数据更新", "数据删除"]
                }
            ],
            test_points: ["用户名登录", "邮箱登录", "密码验证", "数据创建", "数据查询"],
            recommendations: ["建议进行边界值测试", "建议进行并发测试", "建议进行安全测试"]
        };

        const testData2 = {
            test_scenarios: [
                {
                    scenario: "基本功能测试",
                    // 缺少description
                    test_points: ["功能点1", "功能点2"]
                },
                {
                    // 缺少scenario
                    description: "这是一个没有标题的场景",
                    test_points: []
                }
            ],
            test_points: ["独立测试点1", "独立测试点2"],
            recommendations: []
        };

        const testData3 = {
            test_scenarios: [],
            test_points: [],
            recommendations: []
        };

        function renderAnalysisResult(data, containerId) {
            const container = document.getElementById(containerId);
            let html = '';

            // 渲染测试场景
            html += '<h3>📋 测试场景</h3>';
            if (data.test_scenarios && data.test_scenarios.length > 0) {
                data.test_scenarios.forEach((scenario, index) => {
                    html += `
                        <div class="scenario">
                            <h4><span class="tag">${index + 1}</span>${scenario.scenario || scenario.name || `场景 ${index + 1}`}</h4>
                            <p>${scenario.description || scenario.desc || '暂无描述'}</p>
                            ${scenario.test_points && scenario.test_points.length > 0 ? `
                                <div>
                                    <strong>测试点：</strong><br>
                                    ${scenario.test_points.map((point, idx) => 
                                        `<span class="test-point">${idx + 1}. ${typeof point === 'string' ? point : point.name || JSON.stringify(point)}</span>`
                                    ).join('')}
                                </div>
                            ` : '<em>暂无测试点</em>'}
                        </div>
                    `;
                });
            } else {
                html += '<p><em>暂无测试场景</em></p>';
            }

            // 渲染测试点
            html += '<h3>🎯 测试点</h3>';
            if (data.test_points && data.test_points.length > 0) {
                html += '<div>';
                data.test_points.forEach((point, index) => {
                    const pointText = typeof point === 'string' ? point : point.name || JSON.stringify(point);
                    html += `<span class="test-point"><span class="tag">${index + 1}</span>${pointText}</span>`;
                });
                html += '</div>';
            } else {
                html += '<p><em>暂无测试点</em></p>';
            }

            // 渲染建议
            html += '<h3>💡 建议</h3>';
            if (data.recommendations && data.recommendations.length > 0) {
                data.recommendations.forEach((rec, index) => {
                    const recText = typeof rec === 'string' ? rec : rec.text || JSON.stringify(rec);
                    html += `<div class="recommendation"><span class="tag">${index + 1}</span>${recText}</div>`;
                });
            } else {
                html += '<p><em>暂无建议</em></p>';
            }

            container.innerHTML = html;
        }

        // 渲染测试数据
        renderAnalysisResult(testData1, 'test1');
        renderAnalysisResult(testData2, 'test2');
        renderAnalysisResult(testData3, 'test3');

        console.log('🔍 测试数据结构:');
        console.log('标准格式:', testData1);
        console.log('缺少字段:', testData2);
        console.log('空数据:', testData3);
    </script>
</body>
</html>
