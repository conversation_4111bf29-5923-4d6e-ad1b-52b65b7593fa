#!/usr/bin/env python3
"""
测试API认证和数据获取
"""
import requests
import json

# 配置
BASE_URL = "http://localhost:8000"
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def login():
    """用户登录"""
    print("1. 用户登录...")
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", json=TEST_USER)
    
    if response.status_code == 200:
        token = response.json()["access_token"]
        print("登录成功!")
        return token
    else:
        print(f"登录失败: {response.text}")
        return None

def test_api_calls(token):
    """测试API调用"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n2. 测试LLM模型列表...")
    response = requests.get(f"{BASE_URL}/api/v1/llm/models", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if isinstance(data, dict):
            models = data.get("models", [])
        else:
            models = data  # 如果直接返回列表
        print(f"模型数量: {len(models)}")
        if models:
            print(f"第一个模型: {models[0]}")
    else:
        print(f"错误: {response.text}")
    
    print("\n3. 测试测试用例列表...")
    response = requests.get(f"{BASE_URL}/api/v1/tdpilot/test-cases", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        test_cases = data.get("test_cases", [])
        print(f"测试用例数量: {len(test_cases)}")
        if test_cases:
            print(f"第一个测试用例: {test_cases[0]}")
        else:
            print("测试用例列表为空")
            print(f"完整响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
    else:
        print(f"错误: {response.text}")
    
    print("\n4. 测试SQL生成历史...")
    response = requests.get(f"{BASE_URL}/api/v1/tdpilot/sql-generations", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        generations = data.get("generations", [])
        print(f"生成历史数量: {len(generations)}")
        if generations:
            print(f"第一个生成记录: {generations[0]}")
    else:
        print(f"错误: {response.text}")

def main():
    print("=== API认证和数据获取测试 ===")
    
    # 登录获取token
    token = login()
    if not token:
        return
    
    # 测试API调用
    test_api_calls(token)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
