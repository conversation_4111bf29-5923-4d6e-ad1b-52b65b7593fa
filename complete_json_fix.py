#!/usr/bin/env python3
"""
完整的JSON解析修复脚本
"""

def extract_json_from_llm_output(content):
    """
    从LLM输出中提取JSON内容
    支持多种格式：纯JSON、markdown代码块、thinking标签等
    """
    import json
    import re
    
    if not content or not content.strip():
        return None, "内容为空"
    
    content = content.strip()
    
    # 方法1: 尝试直接解析（如果是纯JSON）
    try:
        return json.loads(content), None
    except json.JSONDecodeError:
        pass
    
    # 方法2: 移除thinking标签后再解析
    if '<think>' in content and '</think>' in content:
        # 移除thinking标签
        content_without_thinking = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL).strip()
        try:
            return json.loads(content_without_thinking), None
        except json.JSONDecodeError:
            content = content_without_thinking  # 继续用移除thinking后的内容
    
    # 方法3: 提取JSON代码块
    json_patterns = [
        r'```json\s*(.*?)\s*```',  # ```json ... ```
        r'```\s*(.*?)\s*```',      # ``` ... ```
    ]
    
    for pattern in json_patterns:
        matches = re.findall(pattern, content, re.DOTALL)
        for match in matches:
            json_content = match.strip()
            try:
                return json.loads(json_content), None
            except json.JSONDecodeError:
                continue
    
    # 方法4: 查找大括号内容
    if '{' in content and '}' in content:
        start = content.find('{')
        end = content.rfind('}') + 1
        json_part = content[start:end]
        try:
            return json.loads(json_part), None
        except json.JSONDecodeError:
            pass
    
    # 方法5: 查找方括号内容（数组）
    if '[' in content and ']' in content:
        start = content.find('[')
        end = content.rfind(']') + 1
        json_part = content[start:end]
        try:
            return json.loads(json_part), None
        except json.JSONDecodeError:
            pass
    
    return None, f"无法从内容中提取有效JSON，内容长度: {len(content)}"

def apply_complete_fix():
    """应用完整的JSON解析修复"""
    
    # 读取文件
    with open('app/agents/tdpilot.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"原始文件长度: {len(content)} 字符")
    
    # 1. 在文件开头添加JSON提取函数
    import_section = 'from ..utils.logger import logger\n'
    import_pos = content.find(import_section) + len(import_section)
    
    function_code = '''
def extract_json_from_llm_output(content):
    """
    从LLM输出中提取JSON内容
    支持多种格式：纯JSON、markdown代码块、thinking标签等
    """
    import re
    
    if not content or not content.strip():
        return None, "内容为空"
    
    content = content.strip()
    
    # 方法1: 尝试直接解析（如果是纯JSON）
    try:
        return json.loads(content), None
    except json.JSONDecodeError:
        pass
    
    # 方法2: 移除thinking标签后再解析
    if '<think>' in content and '</think>' in content:
        # 移除thinking标签
        content_without_thinking = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL).strip()
        try:
            return json.loads(content_without_thinking), None
        except json.JSONDecodeError:
            content = content_without_thinking  # 继续用移除thinking后的内容
    
    # 方法3: 提取JSON代码块
    json_patterns = [
        r'```json\\\\s*(.*?)\\\\s*```',  # ```json ... ```
        r'```\\\\s*(.*?)\\\\s*```',      # ``` ... ```
    ]
    
    for pattern in json_patterns:
        matches = re.findall(pattern, content, re.DOTALL)
        for match in matches:
            json_content = match.strip()
            try:
                return json.loads(json_content), None
            except json.JSONDecodeError:
                continue
    
    # 方法4: 查找大括号内容
    if '{' in content and '}' in content:
        start = content.find('{')
        end = content.rfind('}') + 1
        json_part = content[start:end]
        try:
            return json.loads(json_part), None
        except json.JSONDecodeError:
            pass
    
    return None, f"无法从内容中提取有效JSON，内容长度: {len(content)}"

'''
    
    # 插入函数
    content = content[:import_pos] + function_code + content[import_pos:]
    print("✅ 添加了JSON提取函数")
    
    # 2. 替换需求分析中的JSON解析（第324行附近）
    old_analysis_code = '''            # 尝试解析JSON结果
            try:
                analysis_result = json.loads(result["content"])
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
                # 如果不是有效JSON，返回原始文本
                analysis_result = {
                    "raw_analysis": result["content"],
                    "test_scenarios": [],
                    "recommendations": [],
                    "database_considerations": [],
                    "risk_points": []
                }'''
    
    new_analysis_code = '''            # 尝试解析JSON结果
            analysis_result, parse_error = extract_json_from_llm_output(result["content"])
            if analysis_result is None:
                logger.error(f"JSON解析失败: {parse_error}")
                logger.debug(f"LLM原始输出: {result['content'][:500]}...")
                # 创建fallback结构
                analysis_result = {
                    "raw_analysis": result["content"],
                    "test_scenarios": [],
                    "recommendations": [],
                    "database_considerations": [],
                    "risk_points": [],
                    "parse_error": parse_error
                }
            else:
                logger.info("JSON解析成功")'''
    
    if old_analysis_code in content:
        content = content.replace(old_analysis_code, new_analysis_code)
        print("✅ 替换了需求分析的JSON解析")
    else:
        print("⚠️ 没有找到需求分析的JSON解析代码")
    
    # 3. 替换测试用例生成中的JSON解析（第422行附近）
    old_testcase_pattern = 'test_cases = json.loads(raw_content)'
    if old_testcase_pattern in content:
        new_testcase_code = '''test_cases, parse_error = extract_json_from_llm_output(raw_content)
                if test_cases is None:
                    logger.error(f"测试用例JSON解析失败: {parse_error}")
                    logger.debug(f"LLM原始输出: {raw_content[:500]}...")
                    test_cases = {
                        "test_scenarios": [],
                        "test_coverage": {},
                        "raw_content": raw_content,
                        "parse_error": parse_error
                    }
                else:
                    logger.info("测试用例JSON解析成功")'''
        
        content = content.replace(old_testcase_pattern, new_testcase_code)
        print("✅ 替换了测试用例生成的JSON解析")
    else:
        print("⚠️ 没有找到测试用例生成的JSON解析代码")
    
    # 写回文件
    with open('app/agents/tdpilot.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ 修复完成，新文件长度: {len(content)} 字符")
    return True

if __name__ == "__main__":
    success = apply_complete_fix()
    if success:
        print("🎉 完整JSON解析修复应用成功！")
    else:
        print("❌ 修复应用失败")
