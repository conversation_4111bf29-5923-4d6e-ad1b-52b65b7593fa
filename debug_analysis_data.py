#!/usr/bin/env python3
"""
调试需求分析数据结构
"""

import requests
import json
import sys

# 配置
BASE_URL = "http://localhost:8000/api/v1"
USERNAME = "admin"
PASSWORD = "admin123"

def login():
    """登录获取token"""
    login_data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 登录成功，用户: {result['user']['username']}")
        return result["access_token"]
    else:
        print(f"❌ 登录失败: {response.text}")
        return None

def create_simple_analysis(token):
    """创建一个简单的测试分析"""
    headers = {"Authorization": f"Bearer {token}"}
    
    analysis_data = {
        "requirement_text": "测试用户登录功能，包括用户名密码验证、记住登录状态、登录失败处理等场景",
        "background_knowledge": "这是一个Web应用的用户认证系统",
        "database_type": "tdsql-pg",
        "llm_model_id": 7
    }
    
    print("🚀 创建简单测试分析...")
    response = requests.post(f"{BASE_URL}/tdpilot/analyze-requirement", 
                           json=analysis_data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        analysis_id = result["id"]
        print(f"✅ 分析创建成功，ID: {analysis_id}")
        
        print("\n📊 返回的数据结构:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        return analysis_id, result
    else:
        print(f"❌ 分析创建失败: {response.text}")
        return None, None

def get_analysis_details(token, analysis_id):
    """获取分析详情"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"\n📖 获取分析详情 (ID: {analysis_id})...")
    response = requests.get(f"{BASE_URL}/tdpilot/analysis/{analysis_id}", 
                          headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 获取分析详情成功")
        
        print("\n📋 详细数据结构:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 分析数据结构
        print("\n🔍 数据结构分析:")
        print(f"  - 测试场景类型: {type(result.get('test_scenarios', []))}")
        print(f"  - 测试场景数量: {len(result.get('test_scenarios', []))}")
        
        if result.get('test_scenarios'):
            for i, scenario in enumerate(result['test_scenarios']):
                print(f"  - 场景 {i+1} 类型: {type(scenario)}")
                print(f"  - 场景 {i+1} 键: {list(scenario.keys()) if isinstance(scenario, dict) else 'N/A'}")
                if isinstance(scenario, dict):
                    print(f"    - scenario: {scenario.get('scenario', 'N/A')}")
                    print(f"    - description: {scenario.get('description', 'N/A')}")
                    print(f"    - test_points: {scenario.get('test_points', 'N/A')}")
        
        print(f"  - 测试点类型: {type(result.get('test_points', []))}")
        print(f"  - 测试点数量: {len(result.get('test_points', []))}")
        print(f"  - 建议类型: {type(result.get('recommendations', []))}")
        print(f"  - 建议数量: {len(result.get('recommendations', []))}")
        
        return result
    else:
        print(f"❌ 获取失败: {response.text}")
        return None

def check_database_data(token, analysis_id):
    """检查数据库中的原始数据"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print(f"\n🗄️ 检查数据库原始数据...")
    # 这里我们可以通过API获取更多调试信息
    # 暂时使用现有的API
    
    return get_analysis_details(token, analysis_id)

def main():
    """主函数"""
    print("🔍 需求分析数据结构调试")
    print("=" * 60)
    
    # 登录
    token = login()
    if not token:
        sys.exit(1)
    
    # 创建测试分析
    analysis_id, create_result = create_simple_analysis(token)
    if not analysis_id:
        sys.exit(1)
    
    # 等待分析完成
    import time
    print("\n⏳ 等待分析完成...")
    time.sleep(10)
    
    # 获取分析详情
    detail_result = get_analysis_details(token, analysis_id)
    if not detail_result:
        sys.exit(1)
    
    # 比较创建时和获取时的数据
    print("\n🔄 数据一致性检查:")
    
    create_scenarios = create_result.get('test_scenarios', [])
    detail_scenarios = detail_result.get('test_scenarios', [])
    
    print(f"  - 创建时场景数: {len(create_scenarios)}")
    print(f"  - 获取时场景数: {len(detail_scenarios)}")
    print(f"  - 数据一致: {'✅' if create_scenarios == detail_scenarios else '❌'}")
    
    if create_scenarios != detail_scenarios:
        print("\n⚠️ 数据不一致，详细对比:")
        print("创建时数据:")
        print(json.dumps(create_scenarios, indent=2, ensure_ascii=False))
        print("\n获取时数据:")
        print(json.dumps(detail_scenarios, indent=2, ensure_ascii=False))
    
    print("\n" + "=" * 60)
    print("🎯 调试完成！请检查上述数据结构是否符合前端期望")
    
    # 输出前端期望的数据格式
    print("\n📝 前端期望的数据格式:")
    expected_format = {
        "test_scenarios": [
            {
                "scenario": "场景名称",
                "description": "场景描述", 
                "test_points": ["测试点1", "测试点2"]
            }
        ],
        "test_points": ["独立测试点1", "独立测试点2"],
        "recommendations": ["建议1", "建议2"]
    }
    print(json.dumps(expected_format, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
