#!/usr/bin/env python3
"""
调试func未定义错误的脚本
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def login():
    """登录获取token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"登录失败: {response.status_code}")
        print(response.text)
        return None

def test_simple_analysis():
    """测试简单的需求分析"""
    token = login()
    if not token:
        return False
    
    print("🚀 开始调试func错误...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 使用最简单的数据
    data = {
        "requirement_text": "测试需求",
        "llm_model_id": 7
    }
    
    print("📡 发送请求...")
    response = requests.post(
        f"{BASE_URL}/tdpilot/analyze-requirement-stream",
        json=data,
        headers=headers,
        stream=True
    )
    
    if response.status_code != 200:
        print(f"❌ 请求失败: {response.status_code}")
        print(response.text)
        return False
    
    print("📡 开始接收流式数据...")
    
    for line in response.iter_lines():
        if line:
            line_str = line.decode('utf-8')
            print(f"原始行: {line_str}")
            
            if line_str.startswith('data: '):
                data_str = line_str[6:]
                
                if data_str == '[DONE]':
                    print("🏁 流式传输完成")
                    break
                
                try:
                    data = json.loads(data_str)
                    print(f"解析的数据: {data}",end='',flush=True)
                    
                    if data.get('type') == 'error':
                        print(f"❌ 错误: {data.get('message')}")
                        if 'traceback' in data:
                            print(f"🔍 错误详情:")
                            print(data['traceback'])
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"⚠️ 解析JSON失败: {e}")
                    print(f"原始数据: {data_str}")
    
    return True

if __name__ == "__main__":
    test_simple_analysis()
