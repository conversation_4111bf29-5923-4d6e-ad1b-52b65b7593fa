#!/usr/bin/env python3
"""
调试删除功能的脚本
"""

import psycopg2
import json

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'tdsql_ai_testing',
    'user': 'tdsql',
    'password': 'tdsql123'
}

def check_database_records():
    """检查数据库中的记录数量"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 检查需求分析记录
        cursor.execute("SELECT COUNT(*) FROM requirement_analyses WHERE user_id = 1")
        req_count = cursor.fetchone()[0]
        print(f"需求分析记录数量: {req_count}")
        
        # 检查测试用例记录
        cursor.execute("SELECT COUNT(*) FROM test_cases WHERE user_id = 1")
        test_count = cursor.fetchone()[0]
        print(f"测试用例记录数量: {test_count}")
        
        # 检查SQL生成记录
        cursor.execute("SELECT COUNT(*) FROM sql_generations WHERE user_id = 1")
        sql_count = cursor.fetchone()[0]
        print(f"SQL生成记录数量: {sql_count}")
        
        print(f"总记录数量: {req_count + test_count + sql_count}")
        
        # 显示最近的几条记录
        print("\n最近的需求分析记录:")
        cursor.execute("""
            SELECT id, title, status, created_at 
            FROM requirement_analyses 
            WHERE user_id = 1 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        for row in cursor.fetchall():
            print(f"  ID: {row[0]}, 标题: {row[1]}, 状态: {row[2]}, 创建时间: {row[3]}")
        
        print("\n最近的测试用例记录:")
        cursor.execute("""
            SELECT id, title, status, created_at 
            FROM test_cases 
            WHERE user_id = 1 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        for row in cursor.fetchall():
            print(f"  ID: {row[0]}, 标题: {row[1]}, 状态: {row[2]}, 创建时间: {row[3]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"数据库连接失败: {e}")

def test_delete_record(record_id, record_type):
    """测试删除指定记录"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        if record_type == "requirement_analysis":
            # 检查记录是否存在
            cursor.execute("SELECT id, title FROM requirement_analyses WHERE id = %s AND user_id = 1", (record_id,))
            record = cursor.fetchone()
            if record:
                print(f"找到需求分析记录: ID={record[0]}, 标题={record[1]}")
                
                # 删除关联的测试用例
                cursor.execute("DELETE FROM test_cases WHERE analysis_id = %s", (record_id,))
                deleted_tests = cursor.rowcount
                print(f"删除了 {deleted_tests} 条关联的测试用例")
                
                # 删除需求分析记录
                cursor.execute("DELETE FROM requirement_analyses WHERE id = %s AND user_id = 1", (record_id,))
                deleted_req = cursor.rowcount
                print(f"删除了 {deleted_req} 条需求分析记录")
                
                conn.commit()
                print("删除操作已提交")
            else:
                print(f"未找到ID为 {record_id} 的需求分析记录")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"删除操作失败: {e}")

if __name__ == "__main__":
    print("=== 删除功能调试 ===")
    print("\n1. 检查当前数据库记录:")
    check_database_records()
    
    print("\n2. 如果要测试删除功能，请手动调用 test_delete_record(record_id, 'requirement_analysis')")
    print("   例如: test_delete_record(1, 'requirement_analysis')")
