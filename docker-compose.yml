version: '3.8'

services:
  # 后端API服务
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*****************************************/tdsql_ai_testing
      - REDIS_URL=redis://redis:6379/0
      - MILVUS_HOST=milvus-standalone
      - MILVUS_PORT=19530
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=tdsql123
    depends_on:
      - postgres
      - redis
      - milvus-standalone
      - neo4j
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
      - ./backend/data:/app/data
    networks:
      - tdsql-ai-network

  # 前端服务
  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend
    networks:
      - tdsql-ai-network

  # PostgreSQL数据库
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=tdsql_ai_testing
      - POSTGRES_USER=tdsql
      - POSTGRES_PASSWORD=tdsql123
      - POSTGRES_HOST_AUTH_METHOD=trust
    ports:
      - "5432:5432"
    volumes:
      - ./postgres/data:/var/lib/postgresql/data
    networks:
      - tdsql-ai-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - ./redis/redis_data:/data
    networks:
      - tdsql-ai-network

  # Milvus向量数据库
  etcd:
    container_name: milvus-etcd
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=4294967296
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - ./etcd/data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    networks:
      - tdsql-ai-network

  minio:
    container_name: milvus-minio
    image: minio/minio:latest
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - ./minio/data:/minio_data
    command: minio server /minio_data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - tdsql-ai-network

  milvus-standalone:
    container_name: milvus-standalone
    image: milvusdb/milvus:latest
    volumes:
      - ./milvus/data:/var/lib/milvus
      - ./milvus/conf:/var/lib/milvus/conf
      - ./milvus/logs:/var/lib/milvus/logs
      - ./milvus/db:/var/lib/milvus/db
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - "etcd"
      - "minio"
    networks:
      - tdsql-ai-network

  # Neo4j图数据库
  neo4j:
    image: neo4j:5.26.2
    environment:
      - NEO4J_AUTH=neo4j/tdsql123
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - ./neo4j/data:/data
      - ./neo4j/logs:/logs
    networks:
      - tdsql-ai-network

volumes:
  postgres_data:
  redis_data:
  etcd_data:
  minio_data:
  milvus_data:
  neo4j_data:
  neo4j_logs:

networks:
  tdsql-ai-network:
    driver: bridge
