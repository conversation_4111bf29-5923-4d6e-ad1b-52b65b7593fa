#!/usr/bin/env python3
"""
测试流式API功能
"""
import requests
import json
import time

def test_stream_api():
    print("🚀 开始测试流式API功能...")
    
    # 1. 登录获取token
    print("\n1. 用户登录...")
    login_response = requests.post(
        'http://localhost:8000/api/v1/auth/login',
        json={
            'username': 'admin',
            'password': 'admin123'
        }
    )
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return
    
    token = login_response.json()['access_token']
    print(f"✅ 登录成功，Token: {token[:20]}...")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # 2. 测试流式需求分析
    print("\n2. 测试流式需求分析...")
    
    stream_data = {
        'requirement_text': '测试流式输出功能，验证AI分析过程的实时显示',
        'background_knowledge': '这是一个流式API测试',
        'database_type': 'tdsql-pg',
        'llm_model_id': 27
    }
    
    try:
        response = requests.post(
            'http://localhost:8000/api/v1/tdpilot/analyze-requirement-stream',
            json=stream_data,
            headers=headers,
            stream=True,
            timeout=120
        )
        
        if response.status_code != 200:
            print(f"❌ 流式请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return
        
        print("📡 开始接收流式数据...")
        analysis_result = None
        
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                if line_str.startswith('data: '):
                    data_str = line_str[6:]  # 移除 "data: " 前缀
                    
                    if data_str == '[DONE]':
                        print("🏁 流式传输完成")
                        break
                    
                    try:
                        data = json.loads(data_str)
                        
                        if data['type'] == 'start':
                            print(f"🎬 {data['message']}")
                        elif data['type'] == 'progress':
                            print(f"⏳ {data['message']}")
                        elif data['type'] == 'complete':
                            print(f"✅ 分析完成！")
                            analysis_result = data['data']
                            print(f"   - 分析ID: {analysis_result['id']}")
                            print(f"   - 状态: {analysis_result['status']}")
                            print(f"   - 测试场景数量: {len(analysis_result.get('test_scenarios', []))}")
                        elif data['type'] == 'error':
                            print(f"❌ 错误: {data['message']}")
                            
                    except json.JSONDecodeError as e:
                        print(f"⚠️ 解析JSON失败: {e}")
                        print(f"原始数据: {data_str}")
        
        if analysis_result:
            print(f"\n✅ 需求分析流式测试成功！分析ID: {analysis_result['id']}")
            
            # 3. 测试流式测试用例生成
            print("\n3. 测试流式测试用例生成...")
            
            testcase_data = {
                'analysis_result_id': analysis_result['id'],
                'llm_model_id': 27,
                'custom_requirements': '生成详细的测试用例'
            }
            
            testcase_response = requests.post(
                'http://localhost:8000/api/v1/tdpilot/generate-testcases-stream',
                json=testcase_data,
                headers=headers,
                stream=True,
                timeout=120
            )
            
            if testcase_response.status_code != 200:
                print(f"❌ 测试用例流式请求失败: {testcase_response.status_code}")
                return
            
            print("📡 开始接收测试用例生成流式数据...")
            
            for line in testcase_response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]
                        
                        if data_str == '[DONE]':
                            print("🏁 测试用例生成流式传输完成")
                            break
                        
                        try:
                            data = json.loads(data_str)
                            
                            if data['type'] == 'start':
                                print(f"🎬 {data['message']}")
                            elif data['type'] == 'progress':
                                print(f"⏳ {data['message']}")
                            elif data['type'] == 'complete':
                                print(f"✅ 测试用例生成完成！")
                                testcase_result = data['data']
                                print(f"   - 测试用例ID: {testcase_result['id']}")
                                print(f"   - 状态: {testcase_result['status']}")
                                print(f"   - 测试场景数量: {len(testcase_result.get('test_scenarios', []))}")
                            elif data['type'] == 'error':
                                print(f"❌ 错误: {data['message']}")
                                
                        except json.JSONDecodeError as e:
                            print(f"⚠️ 解析JSON失败: {e}")
            
            print("\n🎉 流式API测试完成！")
        else:
            print("❌ 未获得分析结果")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == '__main__':
    test_stream_api()
