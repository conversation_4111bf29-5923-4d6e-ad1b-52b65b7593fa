#!/usr/bin/env python3
"""
在容器内直接应用JSON解析修复
"""

import re
import os

def apply_fix():
    """应用JSON解析修复"""
    
    # 读取当前文件
    file_path = 'app/agents/tdpilot.py'
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"原始文件长度: {len(content)} 字符")
    
    # 定义改进的JSON解析函数
    improved_function = '''
def extract_json_from_content(content):
    """
    从LLM输出中提取JSON内容
    支持多种格式：纯JSON、markdown代码块、混合内容等
    """
    if not content or not content.strip():
        return None, "内容为空"
    
    content = content.strip()
    
    # 方法1: 尝试直接解析（如果是纯JSON）
    try:
        return json.loads(content), None
    except json.JSONDecodeError:
        pass
    
    # 方法2: 提取JSON代码块
    json_patterns = [
        r'```json\\s*(.*?)\\s*```',  # ```json ... ```
        r'```\\s*(.*?)\\s*```',      # ``` ... ```
    ]
    
    for pattern in json_patterns:
        match = re.search(pattern, content, re.DOTALL)
        if match:
            json_content = match.group(1).strip()
            try:
                return json.loads(json_content), None
            except json.JSONDecodeError:
                continue
    
    # 方法3: 查找大括号内容
    if '{' in content and '}' in content:
        start = content.find('{')
        end = content.rfind('}') + 1
        json_part = content[start:end]
        try:
            return json.loads(json_part), None
        except json.JSONDecodeError:
            pass
    
    return None, f"无法从内容中提取有效JSON: {content[:100]}..."

'''
    
    # 在文件开头添加改进的函数（在import之后）
    import_end = content.find('from ...utils.logger import logger')
    if import_end != -1:
        import_end = content.find('\n', import_end) + 1
        content = content[:import_end] + improved_function + content[import_end:]
        print("✅ 添加了改进的JSON解析函数")
    else:
        print("❌ 找不到合适的位置插入函数")
        return False
    
    # 替换需求分析中的JSON解析
    old_analysis_pattern = r'try:\s*analysis_result = json\.loads\(result\["content"\]\)\s*except json\.JSONDecodeError as e:\s*logger\.error\(f"JSON解析失败: \{e\}"\)'
    
    new_analysis_code = '''try:
                analysis_result, parse_error = extract_json_from_content(result["content"])
                if analysis_result is None:
                    logger.error(f"JSON解析失败: {parse_error}")
                    logger.debug(f"原始内容: {result['content'][:500]}...")
                    # 创建fallback结构
                    analysis_result = {
                        "test_scenarios": [],
                        "recommendations": [],
                        "database_considerations": [],
                        "risk_points": [],
                        "raw_content": result["content"],
                        "parse_error": parse_error
                    }
                else:
                    logger.info("JSON解析成功")
            except Exception as e:
                logger.error(f"分析过程出错: {str(e)}")'''
    
    # 查找并替换需求分析的JSON解析部分
    if 'analysis_result = json.loads(result["content"])' in content:
        # 找到具体的代码块进行替换
        lines = content.split('\n')
        new_lines = []
        i = 0
        while i < len(lines):
            line = lines[i]
            if 'analysis_result = json.loads(result["content"])' in line:
                # 找到try块的开始
                try_start = i
                while try_start > 0 and 'try:' not in lines[try_start]:
                    try_start -= 1
                
                # 找到except块的结束
                except_end = i
                while except_end < len(lines) and not (lines[except_end].strip().startswith('except') and 'json.JSONDecodeError' in lines[except_end]):
                    except_end += 1
                
                # 找到这个except块的结束
                while except_end < len(lines) and (lines[except_end].startswith('                ') or lines[except_end].strip() == '' or 'logger.error' in lines[except_end] or 'json.JSONDecodeError' in lines[except_end]):
                    except_end += 1
                
                # 替换整个try-except块
                indent = '            '  # 保持原有缩进
                new_block = []
                for new_line in new_analysis_code.split('\n'):
                    new_block.append(indent + new_line)
                
                new_lines.extend(lines[:try_start])
                new_lines.extend(new_block)
                new_lines.extend(lines[except_end:])
                break
            else:
                new_lines.append(line)
                i += 1
        
        if len(new_lines) > 0:
            content = '\n'.join(new_lines)
            print("✅ 替换了需求分析的JSON解析逻辑")
        else:
            print("❌ 无法替换需求分析的JSON解析逻辑")
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ 修复完成，新文件长度: {len(content)} 字符")
    return True

if __name__ == "__main__":
    success = apply_fix()
    if success:
        print("🎉 JSON解析修复应用成功！")
    else:
        print("❌ 修复应用失败")
