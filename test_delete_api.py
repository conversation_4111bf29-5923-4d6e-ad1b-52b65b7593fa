#!/usr/bin/env python3
"""
测试删除历史记录API的脚本
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8000"

def test_login():
    """测试登录并获取token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", json=login_data)
    if response.status_code == 200:
        token = response.json()["access_token"]
        print("✓ 登录成功")
        return token
    else:
        print(f"✗ 登录失败: {response.status_code} - {response.text}")
        return None

def test_get_history(token):
    """获取历史记录"""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{BASE_URL}/api/v1/tdpilot/history", headers=headers)
    if response.status_code == 200:
        history = response.json()
        print(f"✓ 获取历史记录成功，共 {len(history)} 条记录")
        return history
    else:
        print(f"✗ 获取历史记录失败: {response.status_code} - {response.text}")
        return []

def test_delete_single(token, item_type, item_id):
    """测试单个删除"""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.delete(f"{BASE_URL}/api/v1/tdpilot/history/{item_type}/{item_id}", headers=headers)
    if response.status_code == 200:
        result = response.json()
        print(f"✓ 单个删除成功: {result}")
        return True
    else:
        print(f"✗ 单个删除失败: {response.status_code} - {response.text}")
        return False

def test_batch_delete(token, items):
    """测试批量删除"""
    headers = {"Authorization": f"Bearer {token}"}
    data = {"items": items}
    
    response = requests.delete(f"{BASE_URL}/tdpilot/history/batch", headers=headers, json=data)
    if response.status_code == 200:
        result = response.json()
        print(f"✓ 批量删除成功: {result}")
        return True
    else:
        print(f"✗ 批量删除失败: {response.status_code} - {response.text}")
        return False

def main():
    print("开始测试删除历史记录API...")
    
    # 1. 登录获取token
    token = test_login()
    if not token:
        return
    
    # 2. 获取历史记录
    history = test_get_history(token)
    if not history:
        print("没有历史记录可以测试删除功能")
        return
    
    print("\n当前历史记录:")
    for i, item in enumerate(history[:5]):  # 只显示前5条
        print(f"  {i+1}. ID: {item['id']}, 类型: {item['type']}, 标题: {item.get('title', 'N/A')}")
    
    # 3. 测试单个删除（如果有记录的话）
    if len(history) > 0:
        first_item = history[0]
        print(f"\n测试删除第一条记录: ID={first_item['id']}, 类型={first_item['type']}")
        
        # 注意：这里实际会删除记录，请谨慎使用
        # test_delete_single(token, first_item['type'], first_item['id'])
        print("(跳过实际删除以保护数据)")
    
    # 4. 测试批量删除（模拟，不实际执行）
    if len(history) > 1:
        items_to_delete = [
            {"id": history[0]['id'], "type": history[0]['type']},
            {"id": history[1]['id'], "type": history[1]['type']}
        ]
        print(f"\n模拟批量删除测试: {items_to_delete}")
        # test_batch_delete(token, items_to_delete)
        print("(跳过实际删除以保护数据)")
    
    print("\n✓ API测试完成")

if __name__ == "__main__":
    main()
