#!/usr/bin/env python3
"""
测试流式API修复后的功能
"""
import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1"

def login():
    """登录获取token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"❌ 登录失败: {response.text}")
        return None

def test_requirement_analysis_stream(token):
    """测试需求分析流式API"""
    print("\n🧪 测试需求分析流式API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    data = {
        "requirement_text": "开发一个用户管理系统，包括用户注册、登录、信息管理等功能。需要支持用户权限控制，确保数据安全。",
        "background_knowledge": "这是一个基于TDSQL-PG的用户管理系统",
        "database_type": "tdsql-pg",
        "llm_model_id": 7
    }
    
    response = requests.post(
        f"{BASE_URL}/tdpilot/analyze-requirement-stream",
        json=data,
        headers=headers,
        stream=True,
        timeout=120
    )
    
    if response.status_code != 200:
        print(f"❌ 请求失败: {response.status_code}")
        print(response.text)
        return None
    
    print("📡 开始接收流式数据...")
    analysis_result = None
    
    for line in response.iter_lines():
        if line:
            line_str = line.decode('utf-8')
            if line_str.startswith('data: '):
                data_str = line_str[6:]
                
                if data_str == '[DONE]':
                    print("🏁 流式传输完成")
                    break
                
                try:
                    data = json.loads(data_str)
                    
                    if data['type'] == 'start':
                        print(f"🎬 {data['message']}")
                    elif data['type'] == 'progress':
                        print(f"⏳ {data['message']}")
                    elif data['type'] == 'stream':
                        print(f"📝 流式内容: {data.get('content', '')[:50]}...")
                    elif data['type'] == 'complete':
                        print(f"✅ 分析完成！")
                        analysis_result = data['data']['analysis_result']
                        print(f"   - 分析ID: {analysis_result.get('id')}")
                        print(f"   - 状态: {analysis_result.get('status')}")
                        print(f"   - 测试场景数量: {len(analysis_result.get('test_scenarios', []))}")
                        print(f"   - 测试点数量: {len(analysis_result.get('test_points', []))}")
                        print(f"   - 建议数量: {len(analysis_result.get('recommendations', []))}")
                    elif data['type'] == 'error':
                        error_msg = data['message']
                        print(f"❌ 错误: {error_msg}")
                        # 打印更详细的错误信息
                        if 'traceback' in data:
                            print(f"🔍 错误详情: {data['traceback']}")

                except json.JSONDecodeError as e:
                    print(f"⚠️ 解析JSON失败: {e}")
                    print(f"原始数据: {data_str}")
                    print(f"原始数据: {data_str[:100]}...")
    
    return analysis_result

def test_testcase_generation_stream(token, analysis_result):
    """测试测试用例生成流式API"""
    if not analysis_result or not analysis_result.get('id'):
        print("❌ 没有有效的分析结果，跳过测试用例生成测试")
        return None
    
    print("\n🧪 测试测试用例生成流式API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    data = {
        "analysis_result_id": analysis_result['id'],
        "llm_model_id": 7,
        "custom_requirements": "生成详细的测试用例，包括正常流程和异常流程"
    }
    
    response = requests.post(
        f"{BASE_URL}/tdpilot/generate-testcases-stream",
        json=data,
        headers=headers,
        stream=True,
        timeout=120
    )
    
    if response.status_code != 200:
        print(f"❌ 请求失败: {response.status_code}")
        print(response.text)
        return None
    
    print("📡 开始接收测试用例生成流式数据...")
    testcase_result = None
    
    for line in response.iter_lines():
        if line:
            line_str = line.decode('utf-8')
            if line_str.startswith('data: '):
                data_str = line_str[6:]
                
                if data_str == '[DONE]':
                    print("🏁 测试用例生成流式传输完成")
                    break
                
                try:
                    data = json.loads(data_str)
                    
                    if data['type'] == 'start':
                        print(f"🎬 {data['message']}")
                    elif data['type'] == 'progress':
                        print(f"⏳ {data['message']}")
                    elif data['type'] == 'stream':
                        print(f"📝 流式内容: {data.get('content', '')[:50]}...")
                    elif data['type'] == 'complete':
                        print(f"✅ 测试用例生成完成！")
                        testcase_result = data['data']['test_cases']
                        print(f"   - 测试用例ID: {testcase_result.get('id')}")
                        print(f"   - 状态: {testcase_result.get('status')}")
                        print(f"   - 测试场景数量: {len(testcase_result.get('test_scenarios', []))}")
                    elif data['type'] == 'error':
                        print(f"❌ 错误: {data['message']}")
                        
                except json.JSONDecodeError as e:
                    print(f"⚠️ 解析JSON失败: {e}")
                    print(f"原始数据: {data_str[:100]}...")
    
    return testcase_result

def main():
    """主函数"""
    print("🚀 开始测试流式API修复...")
    
    # 1. 登录
    print("\n1️⃣ 登录...")
    token = login()
    if not token:
        print("❌ 登录失败，退出测试")
        return
    
    print(f"✅ 登录成功，Token: {token[:20]}...")
    
    # 2. 测试需求分析流式API
    analysis_result = test_requirement_analysis_stream(token)
    
    if analysis_result:
        print(f"\n✅ 需求分析流式测试成功！")
        print(f"   - 分析结果包含ID: {analysis_result.get('id')}")
        print(f"   - 状态: {analysis_result.get('status')}")
        
        # 3. 测试测试用例生成流式API
        testcase_result = test_testcase_generation_stream(token, analysis_result)
        
        if testcase_result:
            print(f"\n✅ 测试用例生成流式测试成功！")
            print(f"   - 测试用例结果包含ID: {testcase_result.get('id')}")
            print(f"   - 状态: {testcase_result.get('status')}")
        else:
            print(f"\n❌ 测试用例生成流式测试失败")
    else:
        print(f"\n❌ 需求分析流式测试失败")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
