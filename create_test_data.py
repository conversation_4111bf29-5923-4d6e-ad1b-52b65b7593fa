#!/usr/bin/env python3
"""
创建测试数据脚本
"""
import asyncio
import asyncpg
import json
from datetime import datetime

async def create_test_data():
    """创建测试数据"""
    # 连接数据库
    conn = await asyncpg.connect("postgresql://tdsql:tdsql123@localhost:5432/tdsql_ai_testing")
    
    try:
        # 创建测试用例数据
        test_scenarios = [
            {
                "scenario": "用户管理功能测试",
                "test_cases": [
                    {
                        "case_id": "TC001",
                        "name": "用户注册功能测试",
                        "description": "测试用户注册的各种场景",
                        "category": "功能",
                        "priority": "高",
                        "steps": [
                            "输入有效的用户信息",
                            "点击注册按钮",
                            "验证注册成功"
                        ],
                        "expected_result": "用户注册成功，返回用户ID"
                    },
                    {
                        "case_id": "TC002", 
                        "name": "用户登录功能测试",
                        "description": "测试用户登录的各种场景",
                        "category": "功能",
                        "priority": "高",
                        "steps": [
                            "输入正确的用户名和密码",
                            "点击登录按钮",
                            "验证登录成功"
                        ],
                        "expected_result": "用户登录成功，跳转到主页"
                    }
                ]
            },
            {
                "scenario": "数据查询性能测试",
                "test_cases": [
                    {
                        "case_id": "TC003",
                        "name": "大数据量查询测试",
                        "description": "测试大数据量情况下的查询性能",
                        "category": "性能",
                        "priority": "中",
                        "steps": [
                            "准备大量测试数据",
                            "执行复杂查询",
                            "记录查询时间"
                        ],
                        "expected_result": "查询时间在可接受范围内"
                    }
                ]
            }
        ]
        
        # 首先创建需求分析记录
        analysis_query = """
        INSERT INTO requirement_analyses (
            user_id, title, requirement_text, database_type, llm_model_id,
            analysis_result, status, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING id
        """

        analysis_result_text = """
        功能需求分析：
        1. 用户注册功能 - 支持用户创建新账户
        2. 用户登录功能 - 支持用户身份验证
        3. 用户信息管理 - 支持用户信息的增删改查
        4. 用户权限控制 - 支持不同用户角色的权限管理

        非功能需求分析：
        1. 性能要求 - 系统响应时间小于2秒
        2. 安全要求 - 密码加密存储，防止SQL注入
        3. 可用性要求 - 系统可用性达到99.9%

        测试范围：用户管理模块的完整功能测试
        风险评估：中等风险，需要重点关注安全性
        """

        analysis_id = await conn.fetchval(
            analysis_query,
            1,  # user_id
            "用户管理系统需求分析",
            "分析用户管理系统的功能和非功能需求，包括用户注册、登录、信息管理等核心功能。需要测试系统的完整CRUD操作，以及性能和安全性测试。",
            "tdsql-pg",  # database_type
            7,  # llm_model_id
            analysis_result_text,
            "completed",
            datetime.now(),
            datetime.now()
        )

        print(f"创建需求分析成功，ID: {analysis_id}")

        # 插入测试用例
        insert_query = """
        INSERT INTO test_cases (
            analysis_id, user_id, title, llm_model_id,
            test_scenarios, status, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id
        """

        test_case_id = await conn.fetchval(
            insert_query,
            analysis_id,  # analysis_id
            1,  # user_id (假设admin用户ID为1)
            "用户管理系统测试用例",
            7,  # llm_model_id
            json.dumps(test_scenarios),
            "completed",
            datetime.now(),
            datetime.now()
        )
        
        print(f"创建测试用例成功，ID: {test_case_id}")
        
        # 创建一个用户记录（如果不存在）
        user_query = """
        INSERT INTO users (id, username, email, full_name, hashed_password, is_active, created_at)
        VALUES (1, 'admin', '<EMAIL>', 'Administrator', 'hashed_password', true, $1)
        ON CONFLICT (id) DO NOTHING
        """
        
        await conn.execute(user_query, datetime.now())
        print("确保admin用户存在")
        
        print("测试数据创建完成!")
        
    except Exception as e:
        print(f"创建测试数据失败: {e}")
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(create_test_data())
