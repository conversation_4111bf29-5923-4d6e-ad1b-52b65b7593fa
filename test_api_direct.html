<!DOCTYPE html>
<html>
<head>
    <title>直接测试API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>直接测试API接口</h1>
    
    <div>
        <button onclick="testLogin()">1. 测试登录</button>
        <button onclick="testModels()">2. 测试模型列表</button>
        <button onclick="testAnalysis()">3. 测试需求分析</button>
    </div>
    
    <div id="results"></div>

    <script>
        let authToken = '';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        async function testLogin() {
            addResult('正在测试登录...', 'loading');
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;
                    addResult(`✅ 登录成功！Token: ${authToken.substring(0, 20)}...`, 'success');
                } else {
                    addResult(`❌ 登录失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 登录错误: ${error.message}`, 'error');
            }
        }
        
        async function testModels() {
            if (!authToken) {
                addResult('❌ 请先登录', 'error');
                return;
            }
            
            addResult('正在获取模型列表...', 'loading');
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/llm/models', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ 获取模型成功！共 ${data.length} 个模型`, 'success');
                    data.forEach(model => {
                        addResult(`  - 模型ID: ${model.id}, 名称: ${model.name}, 提供商: ${model.provider}`, 'info');
                    });
                } else {
                    addResult(`❌ 获取模型失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ 获取模型错误: ${error.message}`, 'error');
            }
        }
        
        async function testAnalysis() {
            if (!authToken) {
                addResult('❌ 请先登录', 'error');
                return;
            }
            
            addResult('正在测试需求分析（可能需要1-2分钟）...', 'loading');
            const startTime = Date.now();
            
            try {
                const response = await fetch('http://localhost:8000/api/v1/tdpilot/analyze-requirement', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        title: "API测试",
                        requirement_text: "测试需求分析功能是否正常工作",
                        background_knowledge: "这是一个API测试",
                        database_type: "tdsql-pg",
                        llm_model_id: 27
                    }),
                    signal: AbortSignal.timeout(150000) // 2.5分钟超时
                });
                
                const endTime = Date.now();
                const duration = (endTime - startTime) / 1000;
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ 需求分析成功！耗时: ${duration.toFixed(1)}秒`, 'success');
                    addResult(`  - 分析ID: ${data.id}`, 'info');
                    addResult(`  - 状态: ${data.status}`, 'info');
                } else {
                    addResult(`❌ 需求分析失败-8: ${response.status} ${response.statusText}，耗时: ${duration.toFixed(1)}秒`, 'error');
                }
            } catch (error) {
                const endTime = Date.now();
                const duration = (endTime - startTime) / 1000;
                
                if (error.name === 'TimeoutError') {
                    addResult(`⏰ 需求分析超时！耗时: ${duration.toFixed(1)}秒`, 'error');
                } else {
                    addResult(`❌ 需求分析错误: ${error.message}，耗时: ${duration.toFixed(1)}秒`, 'error');
                }
            }
        }
    </script>
</body>
</html>
