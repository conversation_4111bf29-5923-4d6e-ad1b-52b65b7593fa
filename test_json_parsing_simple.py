#!/usr/bin/env python3
"""
简单测试JSON解析修复效果
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_simple():
    """简单测试"""
    print("🧪 简单测试JSON解析修复...")
    
    # 登录
    login_data = {"username": "admin", "password": "admin123"}
    login_response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    # 获取模型
    models_response = requests.get(f"{BASE_URL}/llm/models", headers=headers)
    models = models_response.json()
    
    # 使用第一个可用模型
    model_id = models[0]["id"]
    model_name = models[0]["name"]
    print(f"使用模型: {model_name}")
    
    # 测试测试用例生成（这个已经成功了）
    print("\n测试测试用例生成...")
    
    # 先创建一个简单的需求分析
    analysis_data = {
        "title": "简单测试",
        "requirement_text": "测试基本功能",
        "database_type": "tdsql-pg",
        "llm_model_id": model_id
    }
    
    analysis_response = requests.post(f"{BASE_URL}/tdpilot/analyze-requirement", 
                                    json=analysis_data, headers=headers)
    
    if analysis_response.status_code == 200:
        analysis_result = analysis_response.json()
        analysis_id = analysis_result["id"]
        print(f"✅ 需求分析完成: {analysis_id}")
        
        # 生成测试用例
        testcase_data = {
            "analysis_result_id": analysis_id,
            "llm_model_id": model_id,
            "custom_requirements": "生成简单的测试用例"
        }
        
        testcase_response = requests.post(f"{BASE_URL}/tdpilot/generate-testcases", 
                                        json=testcase_data, headers=headers)
        
        if testcase_response.status_code == 200:
            testcase_result = testcase_response.json()
            test_scenarios = testcase_result.get("test_scenarios", [])
            print(f"✅ 测试用例生成成功: {len(test_scenarios)} 个场景")
            
            if len(test_scenarios) > 0:
                print("🎉 JSON解析修复验证成功！")
                return True
            else:
                print("⚠️ 没有生成测试场景")
                return False
        else:
            print(f"❌ 测试用例生成失败: {testcase_response.text}")
            return False
    else:
        print(f"❌ 需求分析失败-12: {analysis_response.text}")
        return False

if __name__ == "__main__":
    success = test_simple()
    if success:
        print("\n✅ 测试通过！JSON解析修复生效。")
    else:
        print("\n❌ 测试失败。")
