#!/usr/bin/env python3
"""
使用DeepSeek模型测试需求分析
"""
import requests
import json

def test_with_deepseek():
    print("🔍 使用DeepSeek模型测试需求分析...")
    
    # 1. 登录获取token
    print("\n1. 用户登录...")
    login_response = requests.post(
        'http://localhost:8000/api/v1/auth/login',
        json={
            'username': 'admin',
            'password': 'admin123'
        }
    )
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return
    
    token = login_response.json()['access_token']
    print(f"✅ 登录成功")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # 2. 使用DeepSeek模型进行需求分析
    print("\n2. 使用DeepSeek模型进行需求分析...")
    analysis_data = {
        'requirement_text': '测试用户注册功能，包括用户名唯一性验证、邮箱格式验证、密码强度验证，以及数据库存储的完整性',
        'background_knowledge': '这是一个Web应用的用户注册模块，使用TDSQL-PostgreSQL数据库',
        'database_type': 'tdsql-pg',
        'llm_model_id': 7  # DeepSeek模型ID
    }
    
    try:
        analysis_response = requests.post(
            'http://localhost:8000/api/v1/tdpilot/analyze-requirement',
            json=analysis_data,
            headers=headers,
            timeout=180  # 3分钟超时
        )
        
        if analysis_response.status_code != 200:
            print(f"❌ 需求分析失败-6: {analysis_response.status_code}")
            print(f"响应内容: {analysis_response.text}")
            return
        
        analysis_result = analysis_response.json()
        print(f"✅ 需求分析成功，分析ID: {analysis_result['id']}")
        print(f"📊 分析结果:")
        print(json.dumps(analysis_result, indent=2, ensure_ascii=False))
        
        # 检查是否有实际内容
        test_scenarios = analysis_result.get('test_scenarios', [])
        test_points = analysis_result.get('test_points', [])
        recommendations = analysis_result.get('recommendations', [])
        
        print(f"\n🔍 内容检查:")
        print(f"   - 测试场景数量: {len(test_scenarios)}")
        print(f"   - 测试点数量: {len(test_points)}")
        print(f"   - 建议数量: {len(recommendations)}")
        
        if test_scenarios:
            print(f"   - 第一个测试场景: {test_scenarios[0]}")
        
        if len(test_scenarios) > 0:
            # 3. 生成测试用例
            print(f"\n3. 生成测试用例...")
            testcase_data = {
                'analysis_result_id': analysis_result['id'],
                'llm_model_id': 7,  # DeepSeek模型ID
                'custom_requirements': '生成详细的测试用例，包含正向和负向测试'
            }
            
            testcase_response = requests.post(
                'http://localhost:8000/api/v1/tdpilot/generate-testcases',
                json=testcase_data,
                headers=headers,
                timeout=180
            )
            
            if testcase_response.status_code != 200:
                print(f"❌ 测试用例生成失败: {testcase_response.status_code}")
                print(f"响应内容: {testcase_response.text}")
                return
            
            testcase_result = testcase_response.json()
            print(f"✅ 测试用例生成成功，测试用例ID: {testcase_result['id']}")
            print(f"📊 测试用例结果:")
            print(json.dumps(testcase_result, indent=2, ensure_ascii=False))
            
            # 检查测试用例内容
            test_scenarios_generated = testcase_result.get('test_scenarios', [])
            print(f"\n🔍 测试用例内容检查:")
            print(f"   - 生成的测试场景数量: {len(test_scenarios_generated)}")
            
            if test_scenarios_generated:
                print(f"   - 第一个生成的测试场景: {test_scenarios_generated[0]}")
        else:
            print("⚠️ 需求分析没有生成测试场景，跳过测试用例生成")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时，DeepSeek模型可能需要更长时间")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == '__main__':
    test_with_deepseek()
