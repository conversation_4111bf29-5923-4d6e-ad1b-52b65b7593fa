#!/usr/bin/env python3
"""
测试LLMModelUsage创建的脚本
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def login():
    """登录获取token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"登录失败: {response.status_code}")
        print(response.text)
        return None

def test_simple_analysis():
    """测试简单的需求分析，但不使用流式API"""
    token = login()
    if not token:
        return False
    
    print("🚀 测试非流式需求分析...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 使用非流式API
    data = {
        "requirement_text": "简单测试",
        "llm_model_id": 7
    }
    
    print("📡 发送非流式请求...")
    response = requests.post(
        f"{BASE_URL}/tdpilot/analyze-requirement",
        json=data,
        headers=headers
    )
    
    if response.status_code != 200:
        print(f"❌ 请求失败: {response.status_code}")
        print(response.text)
        return False
    
    result = response.json()
    print(f"✅ 非流式分析成功，ID: {result.get('id')}")
    return True

if __name__ == "__main__":
    test_simple_analysis()
