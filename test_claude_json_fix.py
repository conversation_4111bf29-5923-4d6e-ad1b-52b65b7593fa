#!/usr/bin/env python3
"""
测试Claude模型的JSON解析修复
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1"

def get_auth_token():
    """获取认证token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    else:
        print(f"❌ 登录失败: {response.text}")
        return None

def test_claude_model():
    """专门测试Claude模型的JSON解析"""
    print("🧪 开始测试Claude模型的JSON解析修复...")
    
    # 1. 获取认证
    print("\n1️⃣ 获取认证token...")
    headers = get_auth_token()
    if not headers:
        return False
    print("✅ 认证成功")
    
    # 2. 获取Claude模型
    print("\n2️⃣ 查找Claude模型...")
    models_response = requests.get(f"{BASE_URL}/llm/models", headers=headers)
    if models_response.status_code != 200:
        print(f"❌ 获取模型失败: {models_response.text}")
        return False
    
    models = models_response.json()
    claude_model = None
    
    for model in models:
        if 'claude' in model['name'].lower() or 'anthropic' in model.get('provider', '').lower():
            claude_model = model
            break
    
    if not claude_model:
        print("❌ 没有找到Claude模型")
        print("可用模型:")
        for model in models:
            print(f"  - {model['name']} ({model['provider']})")
        return False
    
    print(f"✅ 找到Claude模型: {claude_model['name']} (ID: {claude_model['id']})")
    
    # 3. 测试需求分析
    print("\n3️⃣ 测试需求分析...")
    analysis_data = {
        "title": "Claude JSON解析测试",
        "requirement_text": "测试Claude模型的JSON输出解析功能。需要验证系统能否正确处理Claude返回的包含thinking标签或markdown格式的内容。",
        "background_knowledge": "Claude模型经常在输出中包含<think>标签来展示推理过程，或者使用markdown代码块来格式化JSON输出。",
        "database_type": "tdsql-pg",
        "llm_model_id": claude_model['id']
    }
    
    print("📝 发送需求分析请求...")
    analysis_response = requests.post(f"{BASE_URL}/tdpilot/analyze-requirement", 
                                    json=analysis_data, headers=headers)
    
    if analysis_response.status_code != 200:
        print(f"❌ 需求分析失败-9: {analysis_response.text}")
        return False
    
    analysis_result = analysis_response.json()
    analysis_id = analysis_result["id"]
    print(f"✅ 需求分析完成，ID: {analysis_id}")
    print(f"状态: {analysis_result['status']}")
    
    # 检查分析结果
    scenarios = analysis_result.get('test_scenarios', [])
    print(f"📊 生成的测试场景数量: {len(scenarios)}")
    
    if len(scenarios) > 0:
        print("✅ 需求分析JSON解析成功！")
        for i, scenario in enumerate(scenarios[:2], 1):
            scenario_name = scenario.get("scenario", "未知场景")
            print(f"   {i}. {scenario_name}")
    else:
        print("⚠️ 需求分析没有生成测试场景")
        if 'raw_analysis' in analysis_result:
            print("发现原始分析内容，可能是JSON解析失败的fallback")
    
    # 4. 测试测试用例生成
    print("\n4️⃣ 测试测试用例生成...")
    testcase_data = {
        "analysis_result_id": analysis_id,
        "llm_model_id": claude_model['id'],
        "custom_requirements": "请生成详细的测试用例，特别关注JSON格式的正确性。"
    }
    
    print("📝 发送测试用例生成请求...")
    testcase_response = requests.post(f"{BASE_URL}/tdpilot/generate-testcases", 
                                    json=testcase_data, headers=headers)
    
    if testcase_response.status_code != 200:
        print(f"❌ 测试用例生成失败: {testcase_response.text}")
        return False
    
    testcase_result = testcase_response.json()
    testcase_id = testcase_result["id"]
    print(f"✅ 测试用例生成完成，ID: {testcase_id}")
    print(f"状态: {testcase_result['status']}")
    
    # 检查测试用例结果
    test_scenarios = testcase_result.get('test_scenarios', [])
    print(f"📊 生成的测试场景数量: {len(test_scenarios)}")
    
    if len(test_scenarios) > 0:
        print("✅ 测试用例JSON解析成功！")
        total_cases = 0
        for i, scenario in enumerate(test_scenarios[:3], 1):
            scenario_name = scenario.get("scenario", "未知场景")
            cases_count = len(scenario.get("test_cases", []))
            total_cases += cases_count
            print(f"   {i}. {scenario_name} ({cases_count}个测试用例)")
        print(f"📈 总计生成 {total_cases} 个测试用例")
    else:
        print("⚠️ 测试用例生成没有生成测试场景")
        if 'raw_content' in testcase_result:
            print("发现原始内容，可能是JSON解析失败的fallback")
    
    # 5. 检查后端日志
    print("\n5️⃣ 检查最新的后端日志...")
    try:
        import subprocess
        result = subprocess.run(['docker-compose', 'logs', 'backend', '--tail=10'], 
                              capture_output=True, text=True, cwd='.')
        logs = result.stdout
        
        if 'JSON解析成功' in logs:
            print("✅ 日志显示JSON解析成功")
        elif 'JSON解析失败' in logs:
            print("❌ 日志显示JSON解析失败")
            # 查找具体错误
            lines = logs.split('\n')
            for line in lines:
                if 'JSON解析失败' in line:
                    print(f"   错误详情: {line}")
        else:
            print("ℹ️ 日志中没有找到JSON解析相关信息")
    except Exception as e:
        print(f"⚠️ 无法获取日志: {e}")
    
    # 6. 总结
    print(f"\n📋 Claude模型测试总结:")
    print(f"   - 模型: {claude_model['name']}")
    print(f"   - 需求分析ID: {analysis_id}")
    print(f"   - 测试用例ID: {testcase_id}")
    print(f"   - 需求分析场景数: {len(scenarios)}")
    print(f"   - 测试用例场景数: {len(test_scenarios)}")
    
    success = len(scenarios) > 0 and len(test_scenarios) > 0
    return success

if __name__ == "__main__":
    success = test_claude_model()
    if success:
        print("\n🎉 Claude模型JSON解析修复验证成功！")
    else:
        print("\n❌ Claude模型JSON解析仍有问题，需要进一步调试。")
