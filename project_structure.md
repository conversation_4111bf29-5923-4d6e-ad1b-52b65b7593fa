# 数据库AI测试智能化平台 - 项目架构

## 整体架构设计

```
tdsql-ai-testing-platform/
├── backend/                    # 后端服务
│   ├── app/                   # 主应用目录
│   │   ├── __init__.py
│   │   ├── main.py           # FastAPI应用入口
│   │   ├── config/           # 配置管理
│   │   │   ├── __init__.py
│   │   │   ├── settings.py   # 应用配置
│   │   │   └── database.py   # 数据库配置
│   │   ├── core/             # 核心模块
│   │   │   ├── __init__.py
│   │   │   ├── auth.py       # 认证授权
│   │   │   ├── security.py   # 安全相关
│   │   │   └── exceptions.py # 异常处理
│   │   ├── models/           # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── user.py       # 用户模型
│   │   │   ├── llm_model.py  # LLM模型配置
│   │   │   ├── test_case.py  # 测试用例模型
│   │   │   └── knowledge.py  # 知识库模型
│   │   ├── api/              # API路由
│   │   │   ├── __init__.py
│   │   │   ├── v1/           # API版本1
│   │   │   │   ├── __init__.py
│   │   │   │   ├── auth.py   # 认证相关API
│   │   │   │   ├── llm.py    # LLM模型管理API
│   │   │   │   ├── tdpilot.py # TDPilot AI智能体API
│   │   │   │   ├── knowledge.py # 知识库API
│   │   │   │   ├── memory.py # 记忆管理API
│   │   │   │   └── mcp.py    # MCP服务API
│   │   ├── services/         # 业务服务层
│   │   │   ├── __init__.py
│   │   │   ├── llm_service.py # LLM服务
│   │   │   ├── tdpilot_service.py # TDPilot服务
│   │   │   ├── knowledge_service.py # 知识库服务
│   │   │   ├── memory_service.py # 记忆服务
│   │   │   └── mcp_service.py # MCP服务
│   │   ├── agents/           # AI智能体
│   │   │   ├── __init__.py
│   │   │   ├── tdpilot.py    # TDPilot AI智能体
│   │   │   ├── requirement_analyzer.py # 需求分析器
│   │   │   ├── test_generator.py # 测试用例生成器
│   │   │   └── sql_generator.py # SQL生成器
│   │   ├── utils/            # 工具函数
│   │   │   ├── __init__.py
│   │   │   ├── logger.py     # 日志工具
│   │   │   ├── validators.py # 验证工具
│   │   │   └── helpers.py    # 辅助函数
│   │   └── database/         # 数据库相关
│   │       ├── __init__.py
│   │       ├── connection.py # 数据库连接
│   │       ├── migrations/   # 数据库迁移
│   │       └── repositories/ # 数据访问层
│   ├── requirements.txt      # Python依赖
│   ├── Dockerfile           # Docker配置
│   └── docker-compose.yml   # Docker编排
├── frontend/                # 前端应用
│   ├── public/             # 静态资源
│   ├── src/                # 源代码
│   │   ├── components/     # 组件
│   │   │   ├── common/     # 通用组件
│   │   │   ├── auth/       # 认证组件
│   │   │   ├── llm/        # LLM配置组件
│   │   │   ├── tdpilot/    # TDPilot界面组件
│   │   │   ├── knowledge/  # 知识库组件
│   │   │   └── mcp/        # MCP管理组件
│   │   ├── pages/          # 页面
│   │   │   ├── Login.jsx   # 登录页面
│   │   │   ├── Dashboard.jsx # 仪表板
│   │   │   ├── ModelConfig.jsx # 模型配置
│   │   │   ├── TestGeneration.jsx # 测试生成
│   │   │   ├── SQLGeneration.jsx # SQL生成
│   │   │   ├── Knowledge.jsx # 知识库管理
│   │   │   └── Memory.jsx  # 记忆管理
│   │   ├── services/       # API服务
│   │   ├── utils/          # 工具函数
│   │   ├── hooks/          # React Hooks
│   │   ├── store/          # 状态管理
│   │   ├── App.jsx         # 主应用组件
│   │   └── main.jsx        # 应用入口
│   ├── package.json        # 依赖配置
│   ├── vite.config.js      # Vite配置
│   └── Dockerfile          # Docker配置
├── infrastructure/         # 基础设施
│   ├── docker/            # Docker配置
│   │   ├── milvus/        # Milvus向量数据库
│   │   ├── redis/         # Redis缓存
│   │   ├── neo4j/         # Neo4j图数据库
│   │   └── postgres/      # PostgreSQL数据库
│   ├── k8s/               # Kubernetes配置
│   └── scripts/           # 部署脚本
├── docs/                  # 文档
│   ├── api/               # API文档
│   ├── architecture/      # 架构文档
│   └── deployment/        # 部署文档
├── tests/                 # 测试
│   ├── backend/           # 后端测试
│   └── frontend/          # 前端测试
├── .gitignore            # Git忽略文件
├── docker-compose.yml    # 整体Docker编排
└── README.md             # 项目说明
```

## 技术栈选择

### 后端技术栈
- **Web框架**: FastAPI (高性能、自动API文档生成)
- **数据库**: PostgreSQL (主数据库)
- **向量数据库**: Milvus (知识库RAG)
- **图数据库**: Neo4j (知识图谱)
- **缓存**: Redis (会话和上下文记忆)
- **认证**: JWT + OAuth2
- **异步处理**: Celery + Redis
- **API文档**: Swagger/OpenAPI

### 前端技术栈
- **框架**: React 18 + Vite
- **UI库**: Ant Design
- **状态管理**: Zustand
- **路由**: React Router
- **HTTP客户端**: Axios
- **图表**: ECharts/D3.js
- **代码编辑器**: Monaco Editor

### AI/ML技术栈
- **LLM集成**: LangChain
- **向量化**: Sentence Transformers
- **模型支持**: OpenAI API、Anthropic Claude、Google Gemini、DeepSeek、Ollama
- **Prompt工程**: 自定义Prompt模板系统

### 基础设施
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes (可选)
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack
- **CI/CD**: GitHub Actions

## 核心模块设计

### 1. TDPilot AI智能体
- 需求分析引擎
- 测试用例生成器
- SQL脚本生成器
- 执行验证器

### 2. LLM模型管理
- 多模型提供商支持
- 模型配置和验证
- 负载均衡和故障转移

### 3. 知识库系统
- 文档向量化和存储
- 智能检索和推荐
- 知识图谱构建

### 4. 上下文记忆
- 会话状态管理
- 用户偏好记录
- 多用户隔离

### 5. MCP服务
- 多数据库连接管理
- SQL执行和结果验证
- 错误处理和重试机制
