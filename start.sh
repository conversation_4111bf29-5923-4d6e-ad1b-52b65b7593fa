#!/bin/bash

# TDSQL AI测试智能化平台启动脚本

echo "🚀 启动TDSQL AI测试智能化平台..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查.env文件
if [ ! -f .env ]; then
    echo "📝 创建环境配置文件..."
    cp .env.example .env
    echo "✅ 已创建.env文件，请根据需要修改配置"
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p backend/logs
mkdir -p backend/uploads
mkdir -p backend/data/knowledge_base
mkdir -p backend/data/sql_cases
mkdir -p backend/data/test_cases

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker compose ps

# 显示访问信息
echo ""
echo "🎉 TDSQL AI测试智能化平台启动完成！"
echo ""
echo "📊 服务访问地址："
echo "   前端界面: http://localhost:3000"
echo "   后端API: http://localhost:8000"
echo "   API文档: http://localhost:8000/api/v1/docs"
echo ""
echo "🗄️ 数据库管理："
echo "   PostgreSQL: localhost:5432"
echo "   Redis: localhost:6379"
echo "   Milvus: localhost:19530"
echo "   Neo4j: http://localhost:7474 (neo4j/password)"
echo ""
echo "📝 默认登录账号："
echo "   用户名: admin"
echo "   密码: admin123"
echo ""
echo "🤖 LLM模型配置："
echo "   1. 登录系统后，进入'LLM模型配置'页面"
echo "   2. 点击'配置向导'按钮，按步骤配置您的LLM模型"
echo "   3. 支持的提供商: OpenAI、Anthropic、Google、DeepSeek、Ollama"
echo "   4. 详细配置指南: docs/llm-configuration-guide.md"
echo ""
echo "🛠️ 常用命令："
echo "   查看日志: docker compose logs -f [service_name]"
echo "   停止服务: docker compose down"
echo "   重启服务: docker compose restart"
echo ""
