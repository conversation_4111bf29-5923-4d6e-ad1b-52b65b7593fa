#!/usr/bin/env python3
"""
测试用例生成功能测试脚本
"""
import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8000/api/v1"
TEST_USER = {
    "username": "admin",
    "password": "admin123",
    "email": "<EMAIL>"
}

def register_and_login():
    """注册并登录用户"""
    # 直接登录，不注册
    login_response = requests.post(f"{BASE_URL}/auth/login", json={
        "username": TEST_USER["username"],
        "password": TEST_USER["password"]
    })

    if login_response.status_code == 200:
        token = login_response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    else:
        print(f"登录失败: {login_response.text}")
        return None

def test_requirement_analysis(headers, model_id):
    """测试需求分析"""
    analysis_data = {
        "title": "用户管理系统测试",
        "requirement_text": "需要测试一个用户管理系统，包括用户注册、登录、信息修改、权限管理等功能。系统使用PostgreSQL数据库存储用户信息。",
        "background_knowledge": "这是一个基于Web的用户管理系统，支持多角色权限控制。",
        "database_type": "tdsql-pg",
        "llm_model_id": model_id
    }
    
    response = requests.post(f"{BASE_URL}/tdpilot/analyze-requirement", 
                           json=analysis_data, headers=headers)
    
    print(f"需求分析响应状态: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"分析ID: {result['id']}")
        print(f"状态: {result['status']}")
        return result["id"]
    else:
        print(f"需求分析失败-14: {response.text}")
        return None

def test_testcase_generation(headers, analysis_id, model_id):
    """测试测试用例生成"""
    testcase_data = {
        "analysis_result_id": analysis_id,
        "llm_model_id": model_id,
        "custom_requirements": "请特别关注数据安全和并发访问的测试场景"
    }
    
    response = requests.post(f"{BASE_URL}/tdpilot/generate-testcases", 
                           json=testcase_data, headers=headers)
    
    print(f"测试用例生成响应状态: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"测试用例ID: {result['id']}")
        print(f"状态: {result['status']}")
        print(f"测试场景数量: {len(result.get('test_scenarios', []))}")
        
        # 打印部分测试场景
        for i, scenario in enumerate(result.get('test_scenarios', [])[:2]):
            print(f"\n场景 {i+1}: {scenario.get('scenario', scenario.get('name', 'Unknown'))}")
            if 'test_cases' in scenario:
                print(f"  包含 {len(scenario['test_cases'])} 个测试用例")
        
        return result["id"]
    else:
        print(f"测试用例生成失败: {response.text}")
        return None

def check_llm_models(headers):
    """检查可用的LLM模型"""
    response = requests.get(f"{BASE_URL}/llm/models", headers=headers)

    print(f"LLM模型查询响应状态: {response.status_code}")
    if response.status_code == 200:
        models = response.json()
        print(f"可用模型数量: {len(models)}")
        for model in models:
            print(f"  模型ID: {model['id']}, 名称: {model['name']}, 提供商: {model['provider']}")
        return models[0]['id'] if len(models) > 0 else None
    else:
        print(f"查询LLM模型失败: {response.text}")
        return None

def main():
    """主测试函数"""
    print("开始测试修复后的测试用例生成功能...")

    # 1. 登录获取token
    print("\n1. 用户登录...")
    headers = register_and_login()
    if not headers:
        print("登录失败，无法继续测试")
        return

    # 2. 检查LLM模型
    print("\n2. 检查LLM模型配置...")
    model_id = check_llm_models(headers)
    if not model_id:
        print("没有可用的LLM模型，请先配置模型")
        return

    # 3. 测试需求分析（现在是同步的）
    print("\n3. 测试需求分析（同步处理）...")
    print("⏳ 这可能需要1-2分钟，请耐心等待...")
    analysis_id = test_requirement_analysis(headers, model_id)
    if not analysis_id:
        print("需求分析失败，无法继续测试")
        return

    # 4. 测试测试用例生成（现在是同步的）
    print("\n4. 测试测试用例生成（同步处理）...")
    print("⏳ 这可能需要1-2分钟，请耐心等待...")
    testcase_id = test_testcase_generation(headers, analysis_id, model_id)
    if testcase_id:
        print(f"\n✅ 测试用例生成功能测试成功！测试用例ID: {testcase_id}")
        print("🎉 前端页面现在应该能正确显示进度并完成整个流程！")
    else:
        print("\n❌ 测试用例生成功能测试失败")

if __name__ == "__main__":
    main()
