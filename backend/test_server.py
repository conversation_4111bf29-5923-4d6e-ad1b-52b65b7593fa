#!/usr/bin/env python3
"""
简化的测试服务器，用于测试 LLM 服务重构
"""
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio

# 导入我们重构的 LLM 服务
from app.services.llm_service import LLMService

app = FastAPI(title="TDSQL AI Testing Platform - Test Server")

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化 LLM 服务
llm_service = LLMService()

# 数据模型
class LLMGenerateRequest(BaseModel):
    model_id: int
    messages: List[Dict[str, str]]
    tools: Optional[List[Dict[str, Any]]] = None
    tool_choice: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    enable_stream: Optional[bool] = True

class LLMGenerateResponse(BaseModel):
    success: bool
    content: str
    reasoning_content: str = ""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    response_time: float
    model: str
    tool_calls: List[Dict[str, Any]] = []
    error: Optional[str] = None

class LLMTestRequest(BaseModel):
    model_id: int
    test_prompt: str = "Hello, this is a test message."
    tools: Optional[List[Dict[str, Any]]] = None
    tool_choice: Optional[str] = None

# 模拟的模型配置
MOCK_MODELS = {
    1: {
        "id": 1,
        "name": "OpenAI GPT-3.5",
        "provider": "openai",
        "model_name": "gpt-3.5-turbo",
        "api_key": "mock-key",
        "base_url": "https://api.openai.com/v1",
        "max_tokens": 1000,
        "temperature": 0.7,
        "enable_stream": False
    },
    2: {
        "id": 2,
        "name": "DeepSeek Chat",
        "provider": "deepseek",
        "model_name": "deepseek-chat",
        "api_key": "mock-key",
        "base_url": "https://api.deepseek.com",
        "max_tokens": 1000,
        "temperature": 0.7,
        "enable_stream": False
    },
    3: {
        "id": 3,
        "name": "Local Ollama",
        "provider": "ollama",
        "model_name": "llama2",
        "base_url": "http://localhost:11434",
        "max_tokens": 1000,
        "temperature": 0.7,
        "enable_stream": False
    }
}

@app.get("/")
async def root():
    return {"message": "TDSQL AI Testing Platform - Test Server", "status": "running"}

@app.get("/api/v1/llm/models")
async def get_models():
    """获取模型列表"""
    return [
        {
            "id": model["id"],
            "name": model["name"],
            "provider": model["provider"],
            "model_name": model["model_name"],
            "is_active": True,
            "enable_stream": model["enable_stream"],
            "created_at": "2024-01-01T00:00:00",
            "status": "available"
        }
        for model in MOCK_MODELS.values()
    ]

@app.post("/api/v1/llm/models/generate", response_model=LLMGenerateResponse)
async def generate_text(request: LLMGenerateRequest):
    """生成文本"""
    if request.model_id not in MOCK_MODELS:
        raise HTTPException(status_code=404, detail="模型不存在")
    
    model_config = MOCK_MODELS[request.model_id].copy()
    
    # 应用请求参数
    if request.max_tokens:
        model_config["max_tokens"] = request.max_tokens
    if request.temperature is not None:
        model_config["temperature"] = request.temperature
    if request.enable_stream is not None:
        model_config["enable_stream"] = request.enable_stream
    
    # 准备生成参数
    generate_kwargs = {}
    if request.tools:
        generate_kwargs["tools"] = request.tools
    if request.tool_choice:
        generate_kwargs["tool_choice"] = request.tool_choice
    if request.max_tokens:
        generate_kwargs["max_tokens"] = request.max_tokens
    if request.temperature is not None:
        generate_kwargs["temperature"] = request.temperature
    if request.enable_stream is not None:
        generate_kwargs["enable_stream"] = request.enable_stream
    
    # 对于演示目的，如果是模拟模型，返回模拟响应
    if model_config.get("api_key") == "mock-key" or model_config.get("provider") == "ollama":
        # 模拟成功响应
        import time
        response_time = 0.5

        # 模拟思考内容处理
        content = request.messages[-1]["content"] if request.messages else "Hello! This is a mock response."
        if "<thinking>" in content and "</thinking>" in content:
            start = content.find("<thinking>") + len("<thinking>")
            end = content.find("</thinking>")
            reasoning_content = content[start:end].strip()
            main_content = content[end + len("</thinking>"):].strip()
        else:
            reasoning_content = "This is a simulated thinking process for demonstration."
            main_content = f"Mock response to: {content}"

        # 模拟工具调用
        tool_calls = []
        if request.tools:
            tool_calls = [{
                "id": "call_mock_123",
                "type": "function",
                "function": {
                    "name": request.tools[0].get("function", {}).get("name", "mock_function"),
                    "arguments": '{"param": "mock_value"}'
                }
            }]

        mock_result = {
            "success": True,
            "content": main_content,
            "reasoning_content": reasoning_content,
            "prompt_tokens": 25,
            "completion_tokens": 50,
            "total_tokens": 75,
            "response_time": response_time,
            "model": model_config["model_name"],
            "tool_calls": tool_calls
        }

        return LLMGenerateResponse(**mock_result)

    # 调用真实的 LLM 服务
    result = await llm_service.generate_text(
        model_config=model_config,
        messages=request.messages,
        **generate_kwargs
    )

    return LLMGenerateResponse(**result)

@app.post("/api/v1/llm/models/test")
async def test_model(request: LLMTestRequest):
    """测试模型"""
    if request.model_id not in MOCK_MODELS:
        raise HTTPException(status_code=404, detail="模型不存在")
    
    model_config = MOCK_MODELS[request.model_id].copy()
    
    # 准备测试参数
    test_kwargs = {"enable_stream": model_config["enable_stream"]}
    if request.tools:
        test_kwargs["tools"] = request.tools
    if request.tool_choice:
        test_kwargs["tool_choice"] = request.tool_choice
    
    # 对于演示目的，如果是模拟模型，返回模拟响应
    if model_config.get("api_key") == "mock-key" or model_config.get("provider") == "ollama":
        # 模拟成功响应
        import time
        response_time = 0.3

        content = request.test_prompt
        reasoning_content = "This is a simulated thinking process for the test."
        main_content = f"Mock test response to: {content}"

        # 模拟工具调用
        tool_calls = []
        if request.tools:
            tool_calls = [{
                "id": "call_test_123",
                "type": "function",
                "function": {
                    "name": request.tools[0].get("function", {}).get("name", "test_function"),
                    "arguments": '{"test_param": "test_value"}'
                }
            }]

        mock_result = {
            "success": True,
            "content": main_content,
            "reasoning_content": reasoning_content,
            "prompt_tokens": 15,
            "completion_tokens": 30,
            "total_tokens": 45,
            "response_time": response_time,
            "model": model_config["model_name"],
            "tool_calls": tool_calls
        }

        return mock_result

    # 调用真实的 LLM 服务
    result = await llm_service.generate_text(
        model_config,
        request.test_prompt,
        **test_kwargs
    )

    return result

@app.get("/api/v1/llm/providers")
async def get_providers():
    """获取提供商列表"""
    return [
        {
            "name": "openai",
            "display_name": "OpenAI",
            "default_model": "gpt-3.5-turbo",
            "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"],
            "requires_api_key": True,
            "supports_custom_base_url": True,
            "config_fields": ["model_name", "base_url", "api_key"]
        },
        {
            "name": "deepseek",
            "display_name": "DeepSeek",
            "default_model": "deepseek-chat",
            "models": ["deepseek-chat", "deepseek-coder"],
            "requires_api_key": True,
            "supports_custom_base_url": True,
            "config_fields": ["model_name", "base_url", "api_key"]
        },
        {
            "name": "ollama",
            "display_name": "Ollama (本地部署)",
            "default_model": "llama2",
            "models": ["llama2", "llama3", "codellama"],
            "requires_api_key": False,
            "supports_custom_base_url": True,
            "config_fields": ["model_name", "base_url"]
        }
    ]

if __name__ == "__main__":
    print("启动测试服务器...")
    print("访问 http://localhost:8001 查看 API")
    print("访问 http://localhost:8001/docs 查看 API 文档")
    uvicorn.run(app, host="0.0.0.0", port=8000)
