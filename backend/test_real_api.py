#!/usr/bin/env python3
"""
测试真实的 API 调用，查找 'ChatCompletionChunk' object has no attribute 'type' 错误
"""
import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.llm_service import OpenAICompatibleProvider

async def test_with_mock_openai_response():
    """测试模拟的 OpenAI 响应结构"""
    print("=== 测试模拟 OpenAI 响应结构 ===")
    
    # 创建一个模拟的 ChatCompletionChunk 对象
    class MockUsage:
        def __init__(self):
            self.prompt_tokens = 10
            self.completion_tokens = 20
            self.total_tokens = 30
    
    class MockDelta:
        def __init__(self):
            self.content = "Hello"
            self.tool_calls = None
    
    class MockChoice:
        def __init__(self):
            self.delta = MockDelta()
    
    class MockChatCompletionChunk:
        def __init__(self):
            self.choices = [MockChoice()]
            self.usage = MockUsage()
            self.model = "gpt-3.5-turbo"
            # 注意：这里没有 'type' 属性，这可能是错误的原因
    
    # 测试我们的代码是否能处理这种结构
    provider = OpenAICompatibleProvider({
        "model_name": "gpt-3.5-turbo",
        "api_key": "test"
    })
    
    # 模拟处理 chunk 的逻辑
    chunk = MockChatCompletionChunk()
    
    try:
        # 测试我们的属性访问逻辑
        content = ""
        prompt_tokens = 0
        completion_tokens = 0
        total_tokens = 0
        tool_calls = []
        
        # 处理内容增量
        if (hasattr(chunk, 'choices') and 
            chunk.choices and
            len(chunk.choices) > 0 and
            hasattr(chunk.choices[0], 'delta') and
            chunk.choices[0].delta):
            
            delta = chunk.choices[0].delta
            
            # 处理文本内容
            if hasattr(delta, 'content') and delta.content:
                content += delta.content
                print(f"✓ 成功处理内容: {content}")
            
            # 处理工具调用
            if hasattr(delta, 'tool_calls') and delta.tool_calls:
                print("✓ 检测到工具调用")
            else:
                print("✓ 无工具调用")

        # 获取使用统计
        if hasattr(chunk, 'usage') and chunk.usage:
            if hasattr(chunk.usage, 'prompt_tokens'):
                prompt_tokens = chunk.usage.prompt_tokens or 0
            if hasattr(chunk.usage, 'completion_tokens'):
                completion_tokens = chunk.usage.completion_tokens or 0
            if hasattr(chunk.usage, 'total_tokens'):
                total_tokens = chunk.usage.total_tokens or 0
            print(f"✓ Token 统计: {prompt_tokens}/{completion_tokens}/{total_tokens}")

        # 获取实际的模型名称
        if hasattr(chunk, 'model') and chunk.model:
            model_name = chunk.model
            print(f"✓ 模型名称: {model_name}")
        
        print("✓ 所有属性访问都成功，没有错误")
        
    except Exception as e:
        print(f"✗ 发现错误: {e}")
        import traceback
        traceback.print_exc()

async def test_anthropic_style_chunk():
    """测试 Anthropic 风格的 chunk（可能有 type 属性）"""
    print("\n=== 测试 Anthropic 风格的 chunk ===")
    
    class MockAnthropicChunk:
        def __init__(self):
            self.type = "message_start"  # Anthropic 可能有这个属性
            self.choices = None
            self.message = None
            self.delta = None
    
    chunk = MockAnthropicChunk()
    
    try:
        # 测试我们的代码是否会错误地访问 type 属性
        content = ""
        
        # 我们的代码应该只检查 choices，不应该访问 type
        if (hasattr(chunk, 'choices') and 
            chunk.choices and
            len(chunk.choices) > 0):
            print("✓ 处理 choices")
        else:
            print("✓ 没有 choices，跳过")
        
        # 检查是否有错误的 type 访问
        if hasattr(chunk, 'type'):
            print(f"✓ 检测到 type 属性: {chunk.type}")
        
        print("✓ Anthropic 风格 chunk 处理成功")
        
    except Exception as e:
        print(f"✗ 发现错误: {e}")
        import traceback
        traceback.print_exc()

async def test_error_scenarios():
    """测试可能导致错误的场景"""
    print("\n=== 测试错误场景 ===")
    
    # 场景1：chunk 没有 choices 属性
    class EmptyChunk:
        pass
    
    chunk1 = EmptyChunk()
    
    try:
        if (hasattr(chunk1, 'choices') and 
            chunk1.choices and
            len(chunk1.choices) > 0):
            print("处理 choices")
        else:
            print("✓ 场景1：空 chunk 处理成功")
    except Exception as e:
        print(f"✗ 场景1错误: {e}")
    
    # 场景2：choices 是空列表
    class EmptyChoicesChunk:
        def __init__(self):
            self.choices = []
    
    chunk2 = EmptyChoicesChunk()
    
    try:
        if (hasattr(chunk2, 'choices') and 
            chunk2.choices and
            len(chunk2.choices) > 0):
            print("处理 choices")
        else:
            print("✓ 场景2：空 choices 处理成功")
    except Exception as e:
        print(f"✗ 场景2错误: {e}")
    
    # 场景3：delta 是 None
    class NullDeltaChunk:
        def __init__(self):
            self.choices = [type('obj', (object,), {'delta': None})()]
    
    chunk3 = NullDeltaChunk()
    
    try:
        if (hasattr(chunk3, 'choices') and 
            chunk3.choices and
            len(chunk3.choices) > 0 and
            hasattr(chunk3.choices[0], 'delta') and
            chunk3.choices[0].delta):
            print("处理 delta")
        else:
            print("✓ 场景3：null delta 处理成功")
    except Exception as e:
        print(f"✗ 场景3错误: {e}")

async def main():
    """主测试函数"""
    print("开始测试 ChatCompletionChunk 错误...")
    
    await test_with_mock_openai_response()
    await test_anthropic_style_chunk()
    await test_error_scenarios()
    
    print("\n=== 测试总结 ===")
    print("如果所有测试都通过，说明我们的代码能正确处理各种 chunk 格式")
    print("如果有错误，我们需要进一步修复")

if __name__ == "__main__":
    asyncio.run(main())
