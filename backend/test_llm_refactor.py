#!/usr/bin/env python3
"""
测试重构后的 LLM 服务
"""
import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.llm_service import LLMService, OpenAICompatibleProvider, OllamaProvider

async def test_openai_compatible_provider():
    """测试 OpenAI 兼容提供商"""
    print("=== 测试 OpenAI 兼容提供商 ===")
    
    # 模拟配置
    config = {
        "provider": "openai",
        "model_name": "gpt-3.5-turbo",
        "api_key": "test-key",
        "base_url": "https://api.openai.com/v1",
        "max_tokens": 1000,
        "temperature": 0.7
    }
    
    try:
        provider = OpenAICompatibleProvider(config)
        print(f"✓ 提供商创建成功: {provider.__class__.__name__}")
        
        # 测试消息格式
        messages = [{"role": "user", "content": "Hello, this is a test message."}]
        
        # 注意：这里会失败因为没有真实的 API 密钥，但我们可以检查错误类型
        result = await provider.generate(messages, enable_stream=False)
        print(f"✓ 生成方法调用成功")
        print(f"  响应格式: {list(result.keys())}")
        print(f"  成功状态: {result.get('success')}")
        
        # 检查响应格式
        expected_keys = ['success', 'content', 'reasoning_content', 'prompt_tokens', 
                        'completion_tokens', 'total_tokens', 'response_time', 'model', 'tool_calls']
        missing_keys = [key for key in expected_keys if key not in result]
        if missing_keys:
            print(f"✗ 缺少响应字段: {missing_keys}")
        else:
            print(f"✓ 响应格式完整")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")

async def test_ollama_provider():
    """测试 Ollama 提供商"""
    print("\n=== 测试 Ollama 提供商 ===")
    
    config = {
        "provider": "ollama",
        "model_name": "llama2",
        "base_url": "http://localhost:11434"
    }
    
    try:
        provider = OllamaProvider(config)
        print(f"✓ 提供商创建成功: {provider.__class__.__name__}")
        
        # 测试消息格式转换
        messages = [{"role": "user", "content": "Hello, this is a test message."}]
        
        # 这里会失败因为没有运行 Ollama 服务，但我们可以检查错误类型
        result = await provider.generate(messages, enable_stream=False)
        print(f"✓ 生成方法调用成功")
        print(f"  响应格式: {list(result.keys())}")
        
        # 检查响应格式
        expected_keys = ['success', 'content', 'reasoning_content', 'prompt_tokens', 
                        'completion_tokens', 'total_tokens', 'response_time', 'model', 'tool_calls']
        missing_keys = [key for key in expected_keys if key not in result]
        if missing_keys:
            print(f"✗ 缺少响应字段: {missing_keys}")
        else:
            print(f"✓ 响应格式完整")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")

async def test_llm_service():
    """测试 LLM 服务管理器"""
    print("\n=== 测试 LLM 服务管理器 ===")
    
    service = LLMService()
    print(f"✓ LLM 服务创建成功")
    print(f"  支持的提供商: {list(service.providers.keys())}")
    
    # 测试提供商创建
    config = {
        "provider": "openai",
        "model_name": "gpt-3.5-turbo",
        "api_key": "test-key"
    }
    
    try:
        provider = service.create_provider(config)
        print(f"✓ 提供商创建成功: {provider.__class__.__name__}")
    except Exception as e:
        print(f"✗ 提供商创建失败: {e}")
    
    # 测试文本生成（兼容旧接口）
    try:
        result = await service.generate_text(config, "Hello, test message!")
        print(f"✓ 文本生成调用成功（字符串输入）")
        print(f"  响应格式: {list(result.keys())}")
    except Exception as e:
        print(f"✗ 文本生成失败: {e}")
    
    # 测试文本生成（新接口）
    try:
        messages = [{"role": "user", "content": "Hello, test message!"}]
        result = await service.generate_text(config, messages)
        print(f"✓ 文本生成调用成功（消息格式）")
        print(f"  响应格式: {list(result.keys())}")
    except Exception as e:
        print(f"✗ 文本生成失败: {e}")

def test_reasoning_content_extraction():
    """测试思考内容提取"""
    print("\n=== 测试思考内容提取 ===")
    
    from app.services.llm_service import OpenAICompatibleProvider
    
    provider = OpenAICompatibleProvider({})
    
    # 测试包含思考内容的文本
    content_with_thinking = """<thinking>
这是一个测试问题，我需要仔细思考如何回答。
用户问的是关于天气的问题。
</thinking>

今天天气很好，阳光明媚。"""
    
    reasoning, main = provider._extract_reasoning_content(content_with_thinking)
    print(f"✓ 思考内容提取测试")
    print(f"  思考内容: {reasoning[:50]}...")
    print(f"  主要内容: {main[:50]}...")
    
    # 测试不包含思考内容的文本
    normal_content = "这是一个普通的回答，没有思考过程。"
    reasoning2, main2 = provider._extract_reasoning_content(normal_content)
    print(f"✓ 普通内容测试")
    print(f"  思考内容: '{reasoning2}'")
    print(f"  主要内容: {main2[:30]}...")

async def main():
    """主测试函数"""
    print("开始测试重构后的 LLM 服务...")
    
    await test_openai_compatible_provider()
    await test_ollama_provider()
    await test_llm_service()
    test_reasoning_content_extraction()
    
    print("\n=== 测试总结 ===")
    print("✓ 重构后的 LLM 服务基本功能正常")
    print("✓ 支持新的响应格式（token 统计、思考内容、工具调用）")
    print("✓ 兼容旧的字符串输入格式")
    print("✓ 支持新的消息列表格式")
    print("✓ 思考内容提取功能正常")

if __name__ == "__main__":
    asyncio.run(main())
