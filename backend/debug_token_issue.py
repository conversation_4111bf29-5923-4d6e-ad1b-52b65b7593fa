#!/usr/bin/env python3
"""
调试OpenAI和Google token统计问题
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def debug_openai_response():
    """调试OpenAI响应结构"""
    print("=== 调试OpenAI响应结构 ===")
    
    try:
        import openai
        
        # 创建一个模拟的响应对象来理解结构
        print("OpenAI库版本:", openai.__version__)
        
        # 模拟测试（不实际调用API）
        config = {
            "model_name": "gpt-3.5-turbo",
            "api_key": "sk-test-key",  # 无效的key，用于测试错误处理
            "max_tokens": 100,
            "temperature": 0.7
        }
        
        client = openai.AsyncOpenAI(
            api_key=config["api_key"],
            base_url="https://api.openai.com/v1"
        )
        
        try:
            response = await client.chat.completions.create(
                model=config["model_name"],
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10,
                temperature=0.7,
                stream=False
            )
            
            print("响应成功!")
            print("Response type:", type(response))
            print("Response usage:", response.usage)
            if response.usage:
                print("Usage type:", type(response.usage))
                print("Usage attributes:", dir(response.usage))
                
        except Exception as e:
            print(f"API调用失败（预期的）: {str(e)}")
            print("错误类型:", type(e))
            
    except ImportError as e:
        print(f"导入错误: {e}")

async def debug_google_response():
    """调试Google响应结构"""
    print("\n=== 调试Google响应结构 ===")
    
    try:
        import openai
        
        # Google使用OpenAI兼容的API
        config = {
            "model_name": "gemini-pro",
            "api_key": "test-key",  # 无效的key
            "base_url": "https://generativelanguage.googleapis.com/v1beta/openai/"
        }
        
        client = openai.AsyncOpenAI(
            api_key=config["api_key"],
            base_url=config["base_url"]
        )
        
        try:
            response = await client.chat.completions.create(
                model=config["model_name"],
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10,
                temperature=0.7,
                stream=False
            )
            
            print("响应成功!")
            print("Response usage:", response.usage)
            
        except Exception as e:
            print(f"API调用失败（预期的）: {str(e)}")
            print("错误类型:", type(e))
            
    except ImportError as e:
        print(f"导入错误: {e}")

async def test_provider_implementations():
    """测试我们的提供商实现"""
    print("\n=== 测试提供商实现 ===")
    
    try:
        from app.services.llm_service import OpenAIProvider, GoogleProvider
        
        # 测试OpenAI提供商
        print("\n--- 测试OpenAI提供商 ---")
        openai_config = {
            "provider": "openai",
            "model_name": "gpt-3.5-turbo",
            "api_key": "sk-test-key",  # 无效key
            "max_tokens": 50,
            "temperature": 0.0
        }
        
        try:
            provider = OpenAIProvider(openai_config)
            result = await provider.generate("Hello", enable_stream=False)
            
            print(f"结果: {result}")
            print(f"成功: {result.get('success')}")
            print(f"Token使用: {result.get('tokens_used', 0)}")
            print(f"错误: {result.get('error', 'None')}")
            
        except Exception as e:
            print(f"OpenAI提供商异常: {str(e)}")
        
        # 测试Google提供商
        print("\n--- 测试Google提供商 ---")
        google_config = {
            "provider": "google",
            "model_name": "gemini-pro",
            "api_key": "test-key",  # 无效key
            "max_tokens": 50,
            "temperature": 0.0
        }
        
        try:
            provider = GoogleProvider(google_config)
            result = await provider.generate("Hello", enable_stream=False)
            
            print(f"结果: {result}")
            print(f"成功: {result.get('success')}")
            print(f"Token使用: {result.get('tokens_used', 0)}")
            print(f"错误: {result.get('error', 'None')}")
            
        except Exception as e:
            print(f"Google提供商异常: {str(e)}")
            
    except ImportError as e:
        print(f"导入错误: {e}")

if __name__ == "__main__":
    print("开始调试token统计问题...")
    asyncio.run(debug_openai_response())
    asyncio.run(debug_google_response())
    asyncio.run(test_provider_implementations())
