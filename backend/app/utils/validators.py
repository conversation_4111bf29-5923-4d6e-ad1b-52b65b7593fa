"""
验证工具函数
"""
import re
from typing import Optional, List
from email_validator import validate_email, EmailNotValidError


def validate_username(username: str) -> tuple[bool, Optional[str]]:
    """验证用户名"""
    if not username:
        return False, "用户名不能为空"
    
    if len(username) < 3:
        return False, "用户名至少需要3个字符"
    
    if len(username) > 50:
        return False, "用户名不能超过50个字符"
    
    # 只允许字母、数字、下划线和连字符
    if not re.match(r'^[a-zA-Z0-9_-]+$', username):
        return False, "用户名只能包含字母、数字、下划线和连字符"
    
    return True, None


def validate_password(password: str) -> tuple[bool, Optional[str]]:
    """验证密码强度"""
    if not password:
        return False, "密码不能为空"
    
    if len(password) < 6:
        return False, "密码至少需要6个字符"
    
    if len(password) > 128:
        return False, "密码不能超过128个字符"
    
    # 检查是否包含至少一个字母和一个数字
    has_letter = re.search(r'[a-zA-Z]', password)
    has_digit = re.search(r'\d', password)
    
    if not (has_letter and has_digit):
        return False, "密码必须包含至少一个字母和一个数字"
    
    return True, None


def validate_email_address(email: str) -> tuple[bool, Optional[str]]:
    """验证邮箱地址"""
    if not email:
        return False, "邮箱地址不能为空"
    
    try:
        # 使用email-validator库验证
        valid = validate_email(email)
        return True, None
    except EmailNotValidError as e:
        return False, f"邮箱地址格式不正确: {str(e)}"


def validate_database_connection(config: dict) -> tuple[bool, Optional[str]]:
    """验证数据库连接配置"""
    required_fields = ["host", "port", "database", "username", "password"]
    
    for field in required_fields:
        if field not in config or not config[field]:
            return False, f"缺少必需字段: {field}"
    
    # 验证端口号
    try:
        port = int(config["port"])
        if port < 1 or port > 65535:
            return False, "端口号必须在1-65535之间"
    except ValueError:
        return False, "端口号必须是数字"
    
    return True, None


def validate_llm_config(config: dict) -> tuple[bool, Optional[str]]:
    """验证LLM配置"""
    required_fields = ["name", "provider", "model_name"]
    
    for field in required_fields:
        if field not in config or not config[field]:
            return False, f"缺少必需字段: {field}"
    
    # 验证提供商
    supported_providers = ["openai", "anthropic", "google", "deepseek", "ollama", "custom"]
    if config["provider"] not in supported_providers:
        return False, f"不支持的提供商: {config['provider']}"
    
    # 验证温度参数
    if "temperature" in config:
        try:
            temp = float(config["temperature"])
            if temp < 0 or temp > 2:
                return False, "温度参数必须在0-2之间"
        except ValueError:
            return False, "温度参数必须是数字"
    
    # 验证最大token数
    if "max_tokens" in config:
        try:
            max_tokens = int(config["max_tokens"])
            if max_tokens < 1 or max_tokens > 32000:
                return False, "最大token数必须在1-32000之间"
        except ValueError:
            return False, "最大token数必须是数字"
    
    return True, None


def validate_file_upload(filename: str, file_size: int, allowed_extensions: List[str]) -> tuple[bool, Optional[str]]:
    """验证文件上传"""
    if not filename:
        return False, "文件名不能为空"
    
    # 检查文件扩展名
    file_ext = '.' + filename.split('.')[-1].lower() if '.' in filename else ''
    if file_ext not in allowed_extensions:
        return False, f"不支持的文件类型，支持的类型: {', '.join(allowed_extensions)}"
    
    # 检查文件大小（10MB限制）
    max_size = 10 * 1024 * 1024  # 10MB
    if file_size > max_size:
        return False, f"文件大小不能超过{max_size // (1024 * 1024)}MB"
    
    return True, None


def sanitize_sql(sql: str) -> str:
    """SQL注入防护 - 基础清理"""
    if not sql:
        return ""
    
    # 移除危险的SQL关键字（这只是基础防护，实际应用中应该使用参数化查询）
    dangerous_keywords = [
        "DROP DATABASE", "DROP TABLE", "DELETE FROM", "TRUNCATE",
        "ALTER TABLE", "CREATE USER", "DROP USER", "GRANT", "REVOKE"
    ]
    
    sql_upper = sql.upper()
    for keyword in dangerous_keywords:
        if keyword in sql_upper:
            # 在生产环境中，这里应该抛出异常或记录日志
            pass
    
    return sql.strip()


def validate_json_structure(data: dict, required_fields: List[str]) -> tuple[bool, Optional[str]]:
    """验证JSON数据结构"""
    if not isinstance(data, dict):
        return False, "数据必须是JSON对象"
    
    for field in required_fields:
        if field not in data:
            return False, f"缺少必需字段: {field}"
    
    return True, None
