"""
记忆管理API
"""
import time
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from ...api.v1.auth import get_current_user
from ...services.memory_service import memory_service

router = APIRouter()


# Pydantic模型
class MemorySession(BaseModel):
    session_name: str
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = {}


class MemorySessionResponse(BaseModel):
    id: str
    session_name: str
    description: Optional[str]
    message_count: int
    created_at: str
    updated_at: str
    is_active: bool


class MemoryMessage(BaseModel):
    role: str  # user, assistant, system
    content: str
    metadata: Optional[Dict[str, Any]] = {}


class MemoryMessageResponse(BaseModel):
    id: str
    session_id: str
    role: str
    content: str
    timestamp: str
    metadata: Dict[str, Any]


class ContextSearchRequest(BaseModel):
    query: str
    session_id: Optional[str] = None
    limit: int = 10


@router.post("/sessions", response_model=dict)
async def create_memory_session(
    session: MemorySession,
    current_user: dict = Depends(get_current_user)
):
    """创建记忆会话"""
    try:
        session_id = await memory_service.create_session(
            user_id=current_user["id"],
            session_name=session.session_name,
            description=session.description,
            metadata=session.metadata
        )

        return {
            "message": "会话创建成功",
            "session_id": session_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"会话创建失败: {str(e)}")


@router.get("/sessions", response_model=List[MemorySessionResponse])
async def get_memory_sessions(
    current_user: dict = Depends(get_current_user),
    limit: int = 20,
    offset: int = 0
):
    """获取用户的记忆会话列表"""
    # 这里应该从Redis获取用户的会话列表
    return [
        {
            "id": "session_1_1704067200",
            "session_name": "TDSQL-PG测试用例生成",
            "description": "为TDSQL-PG数据库生成测试用例",
            "message_count": 15,
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T02:00:00Z",
            "is_active": True
        },
        {
            "id": "session_1_1704063600",
            "session_name": "SQL性能优化讨论",
            "description": "讨论SQL查询性能优化策略",
            "message_count": 8,
            "created_at": "2024-01-01T01:00:00Z",
            "updated_at": "2024-01-01T01:30:00Z",
            "is_active": False
        }
    ]


@router.get("/sessions/{session_id}", response_model=dict)
async def get_memory_session(
    session_id: str,
    current_user: dict = Depends(get_current_user)
):
    """获取记忆会话详情"""
    # 这里应该从Redis获取会话详情
    return {
        "id": session_id,
        "session_name": "TDSQL-PG测试用例生成",
        "description": "为TDSQL-PG数据库生成测试用例",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T02:00:00Z",
        "is_active": True,
        "metadata": {
            "database_type": "tdsql-pg",
            "llm_model": "gpt-4"
        }
    }


@router.post("/sessions/{session_id}/messages", response_model=dict)
async def add_memory_message(
    session_id: str,
    message: MemoryMessage,
    current_user: dict = Depends(get_current_user)
):
    """向会话添加记忆消息"""
    try:
        message_id = await memory_service.add_message(
            session_id=session_id,
            role=message.role,
            content=message.content,
            metadata=message.metadata
        )

        return {
            "message": "消息添加成功",
            "message_id": message_id
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"消息添加失败: {str(e)}")


@router.get("/sessions/{session_id}/messages", response_model=List[MemoryMessageResponse])
async def get_memory_messages(
    session_id: str,
    current_user: dict = Depends(get_current_user),
    limit: int = 50,
    offset: int = 0
):
    """获取会话的记忆消息"""
    try:
        messages = await memory_service.get_session_messages(
            session_id=session_id,
            limit=limit,
            offset=offset
        )

        # 转换格式
        result = []
        for msg in messages:
            result.append({
                "id": msg.get("message_id", ""),
                "session_id": msg.get("session_id", ""),
                "role": msg.get("role", ""),
                "content": msg.get("content", ""),
                "timestamp": msg.get("timestamp", ""),
                "metadata": msg.get("metadata", {})
            })

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取消息失败: {str(e)}")


@router.delete("/sessions/{session_id}", response_model=dict)
async def delete_memory_session(
    session_id: str,
    current_user: dict = Depends(get_current_user)
):
    """删除记忆会话"""
    try:
        # 这里应该从Redis删除会话及其所有消息
        return {"message": "会话删除成功"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"会话删除失败: {str(e)}")


@router.post("/search", response_model=List[dict])
async def search_memory_context(
    search_request: ContextSearchRequest,
    current_user: dict = Depends(get_current_user)
):
    """搜索上下文记忆"""
    try:
        memories = await memory_service.search_memory(
            user_id=current_user["id"],
            query=search_request.query,
            session_id=search_request.session_id,
            limit=search_request.limit
        )

        return [
            {
                "session_id": memory["session_id"],
                "memory_id": memory["memory_id"],
                "content": memory["content"],
                "relevance_score": memory["relevance_score"],
                "timestamp": memory["timestamp"],
                "memory_type": memory["memory_type"]
            }
            for memory in memories
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上下文搜索失败: {str(e)}")


@router.get("/stats", response_model=dict)
async def get_memory_stats(current_user: dict = Depends(get_current_user)):
    """获取记忆统计信息"""
    return {
        "total_sessions": 25,
        "active_sessions": 3,
        "total_messages": 450,
        "storage_used": "2.5MB",
        "last_activity": "2024-01-01T02:00:00Z"
    }
