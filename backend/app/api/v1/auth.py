"""
认证相关API
"""
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Optional
from datetime import <PERSON><PERSON><PERSON>

from ...config.database import get_db
from ...core.security import (
    create_access_token, 
    verify_password, 
    get_password_hash,
    verify_token
)
from ...core.exceptions import AuthenticationError, ValidationError

router = APIRouter()
security = HTTPBearer()


# Pydantic模型
class UserLogin(BaseModel):
    username: str
    password: str


class UserRegister(BaseModel):
    username: str
    email: str
    password: str
    full_name: Optional[str] = None


class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int


class UserInfo(BaseModel):
    id: int
    username: str
    email: str
    full_name: Optional[str]
    is_active: bool
    created_at: str


# 依赖注入：获取当前用户
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """获取当前用户"""
    try:
        payload = verify_token(credentials.credentials)
        username = payload.get("sub")
        if username is None:
            raise AuthenticationError("Invalid token")
        
        # 这里应该从数据库查询用户信息
        # user = db.query(User).filter(User.username == username).first()
        # if user is None:
        #     raise AuthenticationError("User not found")
        
        # 临时返回用户信息
        return {"username": username, "id": 1}
    except Exception as e:
        raise AuthenticationError("Could not validate credentials")


@router.post("/login", response_model=Token)
async def login(user_data: UserLogin, db: Session = Depends(get_db)):
    """用户登录"""
    try:
        # 这里应该从数据库验证用户
        # user = db.query(User).filter(User.username == user_data.username).first()
        # if not user or not verify_password(user_data.password, user.hashed_password):
        #     raise AuthenticationError("Incorrect username or password")
        
        # 临时验证逻辑
        if user_data.username != "admin" or user_data.password != "admin123":
            raise AuthenticationError("Incorrect username or password")
        
        # 创建访问令牌
        access_token_expires = timedelta(minutes=30)
        access_token = create_access_token(
            data={"sub": user_data.username},
            expires_delta=access_token_expires
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": 1800  # 30分钟
        }
    except AuthenticationError:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/register", response_model=dict)
async def register(user_data: UserRegister, db: Session = Depends(get_db)):
    """用户注册"""
    try:
        # 验证输入
        if len(user_data.password) < 6:
            raise ValidationError("Password must be at least 6 characters long")
        
        # 这里应该检查用户是否已存在
        # existing_user = db.query(User).filter(
        #     (User.username == user_data.username) | (User.email == user_data.email)
        # ).first()
        # if existing_user:
        #     raise ConflictError("Username or email already exists")
        
        # 创建新用户
        # hashed_password = get_password_hash(user_data.password)
        # new_user = User(
        #     username=user_data.username,
        #     email=user_data.email,
        #     full_name=user_data.full_name,
        #     hashed_password=hashed_password
        # )
        # db.add(new_user)
        # db.commit()
        
        return {"message": "User registered successfully"}
    except (ValidationError, ConflictError):
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.get("/me", response_model=UserInfo)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """获取当前用户信息"""
    return {
        "id": current_user["id"],
        "username": current_user["username"],
        "email": "<EMAIL>",
        "full_name": "Administrator",
        "is_active": True,
        "created_at": "2024-01-01T00:00:00Z"
    }


@router.post("/logout")
async def logout(current_user: dict = Depends(get_current_user)):
    """用户登出"""
    # 这里可以实现令牌黑名单逻辑
    return {"message": "Logged out successfully"}
