"""
TDPilot AI智能体API
"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
from datetime import datetime
import json
import asyncio
import traceback
from ...api.v1.auth import get_current_user
from ...config.database import get_db
from ...services.tdpilot_service import tdpilot_service
from ...core.exceptions import LLMError, ValidationError
from ...models.test_case import RequirementAnalysis, TestCase, SQLGeneration
from ...utils.logger import logger

router = APIRouter()


# Pydantic模型
class RequirementAnalysisRequest(BaseModel):
    requirement_text: str
    background_knowledge: Optional[str] = None
    design_document_url: Optional[str] = None
    database_type: str = "tdsql-pg"  # tdsql-pg, tdsql-oracle, tdsql2, tdsql3, cynosdb
    llm_model_id: int
    session_id: Optional[str] = None


class TestCaseGenerationRequest(BaseModel):
    analysis_result_id: int
    llm_model_id: int
    custom_requirements: Optional[str] = None


class SQLGenerationRequest(BaseModel):
    test_case_id: int
    llm_model_id: int
    database_type: str = "tdsql-pg"
    title: Optional[str] = None
    custom_requirements: Optional[str] = None
    use_ai_generation: bool = True  # True: 使用AI生成, False: 使用模板生成


class SQLExecutionRequest(BaseModel):
    sql_content: str
    database_connection_id: int


class SQLExecutionWithOptimizationRequest(BaseModel):
    sql_generation_id: int
    database_connection_id: int
    database_type: str = "tdsql-pg"
    max_iterations: int = 3


class RequirementAnalysisResponse(BaseModel):
    id: int
    status: str  # analyzing, completed, failed
    test_scenarios: List[Dict[str, Any]]
    test_points: List[str]
    recommendations: List[str]
    created_at: str


class TestCaseResponse(BaseModel):
    id: int
    test_scenarios: List[Dict[str, Any]]
    visualization_data: Dict[str, Any]
    status: str
    created_at: str


class SQLGenerationResponse(BaseModel):
    id: int
    sql_content: str
    test_tables: List[str]
    status: str
    created_at: str


@router.post("/analyze-requirement", response_model=RequirementAnalysisResponse)
async def analyze_requirement(
    request: RequirementAnalysisRequest,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """分析用户需求"""
    try:
        result = await tdpilot_service.analyze_requirement(
            db=db,
            user_id=current_user["id"],
            title=f"需求分析 - {request.requirement_text[:50]}...",
            requirement_text=request.requirement_text,
            background_knowledge=request.background_knowledge,
            design_document_url=request.design_document_url,
            database_type=request.database_type,
            llm_model_id=request.llm_model_id,
            session_id=request.session_id
        )

        if result["result"]["success"]:
            analysis_result = result["result"]["analysis_result"]

            # 提取所有测试场景中的测试点
            all_test_points = []
            for scenario in analysis_result.get("test_scenarios", []):
                if isinstance(scenario, dict) and "test_points" in scenario:
                    all_test_points.extend(scenario["test_points"])

            return {
                "id": result["analysis_id"],
                "status": result["status"],
                "test_scenarios": analysis_result.get("test_scenarios", []),
                "test_points": all_test_points,
                "recommendations": analysis_result.get("recommendations", []),
                "created_at": "2024-01-01T00:00:00Z"
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"需求分析失败3: {result['result']['error']}"
            )

    except (LLMError, ValidationError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"需求分析失败2: {str(e)}")


@router.post("/analyze-requirement-stream")
async def analyze_requirement_stream(
    request: RequirementAnalysisRequest,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """流式分析用户需求"""

    async def generate_stream():
        analysis = None
        try:
            # 创建需求分析记录
            analysis = RequirementAnalysis(
                user_id=current_user["id"],
                title=f"需求分析 - {request.requirement_text[:50]}...",
                requirement_text=request.requirement_text,
                background_knowledge=request.background_knowledge,
                design_document_url=request.design_document_url,
                database_type=request.database_type,
                llm_model_id=request.llm_model_id,
                status="analyzing"
            )
            db.add(analysis)
            db.commit()
            db.refresh(analysis)

            # 发送开始信号
            yield f"data: {json.dumps({'type': 'start', 'message': '开始分析需求...'})}\n\n"
            await asyncio.sleep(0.1)

            # 发送进度更新
            yield f"data: {json.dumps({'type': 'progress', 'message': '正在连接AI模型...'})}\n\n"
            await asyncio.sleep(0.5)

            yield f"data: {json.dumps({'type': 'progress', 'message': '正在分析需求...'})}\n\n"
            await asyncio.sleep(0.5)

            # 流式分析需求
            analysis_result = None
            async for chunk in tdpilot_service._analyze_requirement_with_ai_stream(
                db=db,
                user_id=current_user["id"],
                llm_model_id=request.llm_model_id,
                requirement_text=request.requirement_text,
                background_knowledge=request.background_knowledge,
                design_document_url=request.design_document_url,
                database_type=request.database_type
            ):
                # 如果是完成信号，保存结果到数据库
                if chunk.get("type") == "complete" and chunk.get("data", {}).get("success"):
                    try:
                        analysis_result = chunk["data"]["analysis_result"]

                        # 更新分析结果
                        analysis.status = "completed"
                        analysis.test_scenarios = analysis_result.get("test_scenarios", [])

                        # 提取所有测试场景中的测试点
                        all_test_points = []
                        logger.info(f"分析结果结构: {analysis_result.keys()}")
                        logger.info(f"测试场景数量: {len(analysis_result.get('test_scenarios', []))}")

                        for i, scenario in enumerate(analysis_result.get("test_scenarios", [])):
                            logger.info(f"场景 {i}: {scenario}")
                            if isinstance(scenario, dict) and "test_points" in scenario:
                                scenario_points = scenario["test_points"]
                                logger.info(f"场景 {i} 的测试点: {scenario_points}")
                                all_test_points.extend(scenario_points)

                        logger.info(f"提取到的测试点数量: {len(all_test_points)}")
                        logger.info(f"所有测试点: {all_test_points}")
                        analysis.test_points = all_test_points
                        analysis.recommendations = analysis_result.get("recommendations", [])
                        analysis.analysis_result = str(analysis_result)
                        analysis.completed_at = datetime.now()

                        # 先提交分析结果的更新
                        db.commit()
                        logger.info(f"分析结果已保存，ID: {analysis.id}")

                        # 记录使用统计
                        try:
                            from ...models.llm_model import LLMModelUsage
                            usage = LLMModelUsage(
                                model_id=request.llm_model_id,
                                user_id=current_user["id"],
                                usage_type="requirement_analysis",
                                tokens_used=chunk["data"].get("tokens_used", 0),
                                response_time=chunk["data"].get("response_time", 0),
                                success=True
                            )
                            db.add(usage)
                            db.commit()
                            logger.info(f"LLMModelUsage记录已保存，ID: {usage.id}")
                        except Exception as usage_error:
                            logger.error(f"保存LLMModelUsage时出错: {usage_error}")
                            logger.error(f"错误详情: {traceback.format_exc()}")
                            # 不中断流程，继续处理

                        # 创建新的返回数据结构，包含数据库ID
                        enhanced_analysis_result = dict(analysis_result)
                        enhanced_analysis_result["id"] = analysis.id
                        enhanced_analysis_result["status"] = analysis.status
                        enhanced_analysis_result["created_at"] = analysis.created_at.isoformat()

                        # 更新chunk中的数据
                        chunk["data"]["analysis_result"] = enhanced_analysis_result

                    except Exception as save_error:
                        logger.error(f"保存分析结果时出错: {save_error}")
                        logger.error(f"错误详情: {traceback.format_exc()}")
                        # 发送错误信息但不中断流
                        yield f"data: {json.dumps({'type': 'error', 'message': f'保存结果失败: {str(save_error)}'})}\n\n"
                        continue

                yield f"data: {json.dumps(chunk)}\n\n"

        except Exception as e:
            error_msg = f'需求分析失败1: {str(e)}'
            error_traceback = traceback.format_exc()
            logger.error(f"需求分析流式API错误: {error_msg}\n{error_traceback}")

            # 如果有分析记录，更新失败状态
            if analysis:
                analysis.status = "failed"
                analysis.analysis_result = error_msg

                # 记录失败统计
                from ...models.llm_model import LLMModelUsage
                usage = LLMModelUsage(
                    model_id=request.llm_model_id,
                    user_id=current_user["id"],
                    usage_type="requirement_analysis",
                    success=False,
                    error_message=error_msg
                )
                db.add(usage)
                db.commit()

            yield f"data: {json.dumps({'type': 'error', 'message': error_msg, 'traceback': error_traceback})}\n\n"

        # 发送结束信号
        yield f"data: [DONE]\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


@router.get("/analysis/{analysis_id}", response_model=RequirementAnalysisResponse)
async def get_analysis_status(
    analysis_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取需求分析状态"""
    try:
        from ...models.test_case import RequirementAnalysis

        analysis = db.query(RequirementAnalysis).filter(
            RequirementAnalysis.id == analysis_id,
            RequirementAnalysis.user_id == current_user["id"]
        ).first()

        if not analysis:
            raise HTTPException(status_code=404, detail="需求分析不存在")

        return {
            "id": analysis.id,
            "status": analysis.status,
            "test_scenarios": analysis.test_scenarios or [],
            "test_points": analysis.test_points or [],
            "recommendations": analysis.recommendations or [],
            "created_at": analysis.created_at.isoformat() if analysis.created_at else "2024-01-01T00:00:00Z"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分析状态失败: {str(e)}")


@router.post("/generate-testcases", response_model=TestCaseResponse)
async def generate_test_cases(
    request: TestCaseGenerationRequest,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """生成测试用例"""
    try:
        result = await tdpilot_service.generate_test_cases(
            db=db,
            user_id=current_user["id"],
            analysis_id=request.analysis_result_id,
            title=f"测试用例生成 - {request.analysis_result_id}",
            llm_model_id=request.llm_model_id,
            custom_requirements=request.custom_requirements
        )

        if result["result"]["success"]:
            test_cases = result["result"]["test_cases"]
            return {
                "id": result["test_case_id"],
                "test_scenarios": test_cases.get("test_scenarios", []),
                "visualization_data": test_cases.get("test_coverage", {}),
                "status": result["status"],
                "created_at": "2024-01-01T00:00:00Z"
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"测试用例生成失败: {result['result']['error']}"
            )

    except (LLMError, ValidationError) as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试用例生成失败: {str(e)}")


@router.post("/generate-testcases-stream")
async def generate_test_cases_stream(
    request: TestCaseGenerationRequest,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """流式生成测试用例"""

    async def generate_stream():
        test_case = None
        try:
            # 获取需求分析结果
            analysis_result = db.query(RequirementAnalysis).filter(
                RequirementAnalysis.id == request.analysis_result_id,
                RequirementAnalysis.user_id == current_user["id"]
            ).first()

            if not analysis_result:
                raise HTTPException(status_code=404, detail="需求分析结果不存在")

            # 创建测试用例记录
            test_case = TestCase(
                analysis_id=request.analysis_result_id,
                user_id=current_user["id"],
                title=f"测试用例 - {analysis_result.title}",
                llm_model_id=request.llm_model_id,
                custom_requirements=request.custom_requirements,
                status="generating"
            )
            db.add(test_case)
            db.commit()
            db.refresh(test_case)

            # 发送开始信号
            yield f"data: {json.dumps({'type': 'start', 'message': '开始生成测试用例...'})}\n\n"
            await asyncio.sleep(0.1)

            # 发送进度更新
            yield f"data: {json.dumps({'type': 'progress', 'message': '正在连接AI模型...'})}\n\n"
            await asyncio.sleep(0.5)

            yield f"data: {json.dumps({'type': 'progress', 'message': '正在分析需求结果...'})}\n\n"
            await asyncio.sleep(0.5)

            yield f"data: {json.dumps({'type': 'progress', 'message': '正在生成测试场景...'})}\n\n"
            await asyncio.sleep(0.5)

            # 准备分析结果数据
            analysis_data = {
                "test_scenarios": analysis_result.test_scenarios or [],
                "test_points": analysis_result.test_points or [],
                "recommendations": analysis_result.recommendations or []
            }

            # 流式生成测试用例
            test_cases_result = None
            async for chunk in tdpilot_service._generate_test_cases_with_ai_stream(
                db=db,
                user_id=current_user["id"],
                llm_model_id=request.llm_model_id,
                analysis_result=analysis_data,
                custom_requirements=request.custom_requirements
            ):
                # 如果是完成信号，保存结果到数据库
                if chunk.get("type") == "complete" and chunk.get("data", {}).get("success"):
                    test_cases_result = chunk["data"]["test_cases"]

                    # 更新测试用例结果
                    test_case.status = "completed"
                    test_case.test_scenarios = test_cases_result.get("test_scenarios", [])
                    test_case.visualization_data = test_cases_result.get("test_coverage", {})

                    # 记录使用统计
                    from ...models.llm_model import LLMModelUsage
                    usage = LLMModelUsage(
                        model_id=request.llm_model_id,
                        user_id=current_user["id"],
                        usage_type="test_generation",
                        tokens_used=chunk["data"].get("tokens_used", 0),
                        response_time=chunk["data"].get("response_time", 0),
                        success=True
                    )
                    db.add(usage)
                    db.commit()

                    # 创建新的返回数据结构，包含数据库ID
                    enhanced_test_cases = dict(test_cases_result)
                    enhanced_test_cases["id"] = test_case.id
                    enhanced_test_cases["status"] = test_case.status
                    enhanced_test_cases["created_at"] = test_case.created_at.isoformat()

                    # 更新chunk中的数据
                    chunk["data"]["test_cases"] = enhanced_test_cases

                yield f"data: {json.dumps(chunk)}\n\n"

        except Exception as e:
            error_msg = f'测试用例生成失败: {str(e)}'

            # 如果有测试用例记录，更新失败状态
            if test_case:
                test_case.status = "failed"
                test_case.error_message = error_msg

                # 记录失败统计
                from ...models.llm_model import LLMModelUsage
                usage = LLMModelUsage(
                    model_id=request.llm_model_id,
                    user_id=current_user["id"],
                    usage_type="test_generation",
                    success=False,
                    error_message=error_msg
                )
                db.add(usage)
                db.commit()

            yield f"data: {json.dumps({'type': 'error', 'message': error_msg})}\n\n"

        # 发送结束信号
        yield f"data: [DONE]\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


@router.get("/testcase/{testcase_id}", response_model=TestCaseResponse)
async def get_testcase_status(
    testcase_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取测试用例生成状态"""
    try:
        from ...models.test_case import TestCase

        testcase = db.query(TestCase).filter(
            TestCase.id == testcase_id,
            TestCase.user_id == current_user["id"]
        ).first()

        if not testcase:
            raise HTTPException(status_code=404, detail="测试用例不存在")

        return {
            "id": testcase.id,
            "test_scenarios": testcase.test_scenarios or [],
            "visualization_data": testcase.visualization_data or {},
            "status": testcase.status,
            "created_at": testcase.created_at.isoformat() if testcase.created_at else "2024-01-01T00:00:00Z"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取测试用例状态失败: {str(e)}")


@router.get("/testcase/{testcase_id}/debug")
async def get_testcase_debug_info(
    testcase_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取测试用例调试信息"""
    try:
        from ...models.test_case import TestCase

        testcase = db.query(TestCase).filter(
            TestCase.id == testcase_id,
            TestCase.user_id == current_user["id"]
        ).first()

        if not testcase:
            raise HTTPException(status_code=404, detail="测试用例不存在")

        debug_info = {
            "id": testcase.id,
            "title": testcase.title,
            "status": testcase.status,
            "error_message": testcase.error_message,
            "debug_info": testcase.debug_info or {},
            "test_scenarios_count": len(testcase.test_scenarios or []),
            "has_visualization_data": bool(testcase.visualization_data),
            "created_at": testcase.created_at.isoformat() if testcase.created_at else None,
            "updated_at": testcase.updated_at.isoformat() if testcase.updated_at else None,
            "completed_at": testcase.completed_at.isoformat() if testcase.completed_at else None
        }

        # 如果有调试信息，添加更多详细信息
        if testcase.debug_info:
            debug_data = testcase.debug_info
            debug_info.update({
                "llm_raw_output_length": len(debug_data.get("llm_raw_output", "")),
                "llm_reasoning_length": len(debug_data.get("llm_reasoning", "")),
                "json_parse_error": debug_data.get("json_parse_error"),
                "prompt_length": debug_data.get("prompt_length", 0),
                "has_llm_raw_output": bool(debug_data.get("llm_raw_output")),
                "has_llm_reasoning": bool(debug_data.get("llm_reasoning"))
            })

        return debug_info

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取调试信息失败: {str(e)}")


@router.get("/testcase/{testcase_id}/raw-output")
async def get_testcase_raw_output(
    testcase_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取测试用例的LLM原始输出"""
    try:
        from ...models.test_case import TestCase

        testcase = db.query(TestCase).filter(
            TestCase.id == testcase_id,
            TestCase.user_id == current_user["id"]
        ).first()

        if not testcase:
            raise HTTPException(status_code=404, detail="测试用例不存在")

        if not testcase.debug_info:
            raise HTTPException(status_code=404, detail="没有调试信息")

        debug_data = testcase.debug_info
        return {
            "id": testcase.id,
            "llm_raw_output": debug_data.get("llm_raw_output", ""),
            "llm_reasoning": debug_data.get("llm_reasoning", ""),
            "json_parse_error": debug_data.get("json_parse_error"),
            "prompt_length": debug_data.get("prompt_length", 0)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取原始输出失败: {str(e)}")


@router.post("/generate-sql", response_model=SQLGenerationResponse)
async def generate_sql(
    request: SQLGenerationRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """生成SQL脚本"""
    try:
        # 调用TDPilot服务生成SQL脚本
        result = await tdpilot_service.generate_sql_scripts(
            db=db,
            user_id=current_user["id"],
            test_case_id=request.test_case_id,
            title=request.title or f"SQL脚本生成 - {request.database_type}",
            database_type=request.database_type,
            llm_model_id=request.llm_model_id,
            custom_requirements=request.custom_requirements,
            use_ai_generation=request.use_ai_generation
        )

        if result["result"]["success"]:
            sql_generation_id = result["sql_generation_id"]
            sql_result = result["result"]

            return {
                "id": sql_generation_id,
                "sql_content": sql_result["sql_content"],
                "test_tables": sql_result.get("test_tables", []),
                "status": result["status"],
                "created_at": datetime.now().isoformat()
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=f"SQL脚本生成失败: {result['result'].get('error', '未知错误')}"
            )

    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except LLMError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"SQL脚本生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"SQL脚本生成失败: {str(e)}")


@router.post("/generate-sql-stream")
async def generate_sql_stream(
    request: SQLGenerationRequest,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """流式生成SQL脚本"""

    async def generate_stream():
        try:
            # 发送开始信号
            yield f"data: {json.dumps({'type': 'start', 'message': '开始生成SQL脚本...'})}\n\n"
            await asyncio.sleep(0.1)

            # 发送进度更新
            yield f"data: {json.dumps({'type': 'progress', 'message': '正在分析测试用例...'})}\n\n"
            await asyncio.sleep(0.5)

            yield f"data: {json.dumps({'type': 'progress', 'message': '正在生成SQL脚本...'})}\n\n"
            await asyncio.sleep(0.5)

            if request.use_ai_generation:
                # 使用AI流式生成
                yield f"data: {json.dumps({'type': 'progress', 'message': '正在连接AI模型...'})}\n\n"

                # 获取测试用例
                test_case = db.query(TestCase).filter(
                    TestCase.id == request.test_case_id,
                    TestCase.user_id == current_user["id"]
                ).first()

                if not test_case:
                    raise HTTPException(status_code=404, detail="测试用例不存在")

                if test_case.status != "completed":
                    raise HTTPException(status_code=400, detail="测试用例未完成")

                # 创建SQL生成记录
                from ...models.test_case import SQLGeneration
                sql_generation = SQLGeneration(
                    test_case_id=request.test_case_id,
                    user_id=current_user["id"],
                    title=request.title or f"SQL脚本生成 - {request.database_type}",
                    database_type=request.database_type,
                    llm_model_id=request.llm_model_id,
                    custom_requirements=request.custom_requirements,
                    use_ai_generation=True,
                    generation_method="ai",
                    status="generating"
                )
                db.add(sql_generation)
                db.commit()
                db.refresh(sql_generation)

                # 准备测试用例数据
                test_cases_data = {
                    "test_scenarios": test_case.test_scenarios or []
                }

                # 流式生成SQL脚本
                sql_result = None
                async for chunk in tdpilot_service._generate_sql_with_ai_stream(
                    db=db,
                    user_id=current_user["id"],
                    llm_model_id=request.llm_model_id,
                    test_cases_data=test_cases_data,
                    database_type=request.database_type,
                    custom_requirements=request.custom_requirements
                ):
                    # 如果是完成信号，保存结果到数据库
                    if chunk.get("type") == "complete" and chunk.get("data", {}).get("success"):
                        sql_result = chunk["data"]

                        # 更新SQL生成记录
                        sql_generation.status = "completed"
                        sql_generation.sql_content = sql_result.get("sql_content", "")
                        sql_generation.test_tables = sql_result.get("test_tables", [])
                        sql_generation.scenario_count = len(test_cases_data.get("test_scenarios", []))
                        sql_generation.completed_at = datetime.now()
                        sql_generation.generated_at = datetime.now()

                        db.commit()

                        # 修改返回的数据，包含数据库ID
                        chunk["data"]["id"] = sql_generation.id
                        chunk["data"]["created_at"] = sql_generation.created_at.isoformat()

                    # 如果是错误信号，更新失败状态
                    elif chunk.get("type") == "error":
                        sql_generation.status = "failed"
                        sql_generation.error_message = chunk.get("message", "生成失败")
                        db.commit()

                    yield f"data: {json.dumps(chunk)}\n\n"
            else:
                # 使用模板生成（非流式）
                yield f"data: {json.dumps({'type': 'progress', 'message': '正在使用模板生成SQL脚本...'})}\n\n"

                result = await tdpilot_service.generate_sql_scripts(
                    db=db,
                    user_id=current_user["id"],
                    test_case_id=request.test_case_id,
                    title=request.title or f"SQL脚本生成 - {request.database_type}",
                    database_type=request.database_type,
                    llm_model_id=request.llm_model_id,
                    custom_requirements=request.custom_requirements,
                    use_ai_generation=False
                )

                if result["result"]["success"]:
                    response_data = {
                        "type": "complete",
                        "data": {
                            "id": result["sql_generation_id"],
                            "sql_content": result["result"]["sql_content"],
                            "test_tables": result["result"].get("test_tables", []),
                            "status": result["status"],
                            "scenario_count": result["result"].get("scenario_count", 0),
                            "generated_at": result["result"].get("generated_at"),
                            "created_at": datetime.now().isoformat()
                        }
                    }
                    yield f"data: {json.dumps(response_data)}\n\n"
                else:
                    error_data = {
                        "type": "error",
                        "message": f"SQL脚本生成失败: {result['result'].get('error', '未知错误')}"
                    }
                    yield f"data: {json.dumps(error_data)}\n\n"

        except Exception as e:
            logger.error(f"流式SQL生成失败: {str(e)}")

            # 如果有SQL生成记录，更新失败状态
            if 'sql_generation' in locals():
                try:
                    sql_generation.status = "failed"
                    sql_generation.error_message = str(e)
                    db.commit()
                except Exception as db_error:
                    logger.error(f"更新SQL生成记录失败: {str(db_error)}")

            error_data = {
                "type": "error",
                "message": f"SQL脚本生成失败: {str(e)}"
            }
            yield f"data: {json.dumps(error_data)}\n\n"
        finally:
            # 发送结束信号
            yield f"data: {json.dumps({'type': 'end'})}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream"
        }
    )


@router.get("/test-cases", response_model=dict)
async def get_test_cases(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取用户的测试用例列表"""
    try:
        from ...models.test_case import TestCase

        test_cases = db.query(TestCase).filter(
            TestCase.user_id == current_user["id"],
            TestCase.status == "completed"
        ).order_by(TestCase.created_at.desc()).limit(50).all()

        test_cases_data = []
        for tc in test_cases:
            test_cases_data.append({
                "id": tc.id,
                "title": tc.title,
                "status": tc.status,
                "test_scenarios": tc.test_scenarios or [],
                "created_at": tc.created_at.isoformat() if tc.created_at else None,
                "description": f"包含 {len(tc.test_scenarios or [])} 个测试场景"
            })

        return {
            "test_cases": test_cases_data,
            "total": len(test_cases_data)
        }

    except Exception as e:
        logger.error(f"获取测试用例列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取测试用例列表失败: {str(e)}")


@router.get("/sql-generations", response_model=dict)
async def get_sql_generations(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """获取用户的SQL生成历史"""
    try:
        from ...models.test_case import SQLGeneration

        sql_generations = db.query(SQLGeneration).filter(
            SQLGeneration.user_id == current_user["id"]
        ).order_by(SQLGeneration.created_at.desc()).limit(50).all()

        generations_data = []
        for sg in sql_generations:
            generations_data.append({
                "id": sg.id,
                "title": sg.title,
                "status": sg.status,
                "database_type": sg.database_type,
                "sql_content": sg.sql_content,
                "test_tables": sg.test_tables or [],
                "scenario_count": sg.scenario_count or 0,
                "generation_time": sg.generation_time or 0,
                "use_ai_generation": sg.use_ai_generation,
                "generation_method": sg.generation_method,
                "created_at": sg.created_at.isoformat() if sg.created_at else None,
                "generated_at": sg.generated_at.isoformat() if sg.generated_at else None
            })

        return {
            "generations": generations_data,
            "total": len(generations_data)
        }

    except Exception as e:
        logger.error(f"获取SQL生成历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取SQL生成历史失败: {str(e)}")


@router.delete("/sql-generations/{generation_id}", response_model=dict)
async def delete_sql_generation(
    generation_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """删除SQL生成记录"""
    try:
        from ...models.test_case import SQLGeneration

        # 查找记录
        generation = db.query(SQLGeneration).filter(
            SQLGeneration.id == generation_id,
            SQLGeneration.user_id == current_user["id"]
        ).first()

        if not generation:
            raise HTTPException(status_code=404, detail="SQL生成记录不存在")

        # 删除记录
        db.delete(generation)
        db.commit()

        return {
            "success": True,
            "message": "SQL生成记录删除成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除SQL生成记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除SQL生成记录失败: {str(e)}")


@router.post("/execute-sql", response_model=dict)
async def execute_sql(
    request: SQLExecutionRequest,
    current_user: dict = Depends(get_current_user)
):
    """执行SQL脚本"""
    try:
        # 这里应该调用MCP服务执行SQL
        return {
            "success": True,
            "execution_id": "exec_123456",
            "results": [
                {
                    "statement": "CREATE TABLE test_users...",
                    "status": "success",
                    "rows_affected": 0,
                    "execution_time": 0.05
                },
                {
                    "statement": "INSERT INTO test_users...",
                    "status": "success",
                    "rows_affected": 3,
                    "execution_time": 0.02
                }
            ],
            "total_execution_time": 0.15,
            "executed_at": "2024-01-01T00:00:00Z"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"SQL执行失败: {str(e)}")


@router.post("/execute-and-optimize-sql", response_model=dict)
async def execute_and_optimize_sql(
    request: SQLExecutionWithOptimizationRequest,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """执行SQL并基于结果进行优化"""
    try:
        # 获取SQL生成记录
        from ...models.test_case import SQLGeneration
        sql_generation = db.query(SQLGeneration).filter(
            SQLGeneration.id == request.sql_generation_id,
            SQLGeneration.user_id == current_user["id"]
        ).first()

        if not sql_generation:
            raise HTTPException(status_code=404, detail="SQL生成记录不存在")

        # 获取数据库连接配置
        from ...models.mcp import DatabaseConnection  # 需要创建这个模型
        db_connection = db.query(DatabaseConnection).filter(
            DatabaseConnection.id == request.database_connection_id,
            DatabaseConnection.user_id == current_user["id"]
        ).first()

        if not db_connection:
            raise HTTPException(status_code=404, detail="数据库连接不存在")

        # 获取LLM模型配置
        model_config = tdpilot_service._get_model_config(db, sql_generation.llm_model_id)

        # 获取智能体实例
        agent = tdpilot_service._get_or_create_agent(current_user["id"], model_config)

        # 准备数据库配置
        database_config = {
            "connection_id": db_connection.id,
            "host": db_connection.host,
            "port": db_connection.port,
            "database": db_connection.database,
            "username": db_connection.username,
            "password": db_connection.password,
            "connection_params": db_connection.connection_params or {}
        }

        # 执行SQL并优化
        result = await agent.execute_and_optimize_sql(
            sql_content=sql_generation.sql_content,
            database_config=database_config,
            database_type=request.database_type,
            max_iterations=request.max_iterations
        )

        # 更新SQL生成记录
        if result["success"]:
            sql_generation.sql_content = result["final_sql"]
            sql_generation.status = "executed"
            db.commit()

        return {
            "success": result["success"],
            "final_sql": result.get("final_sql"),
            "execution_result": result.get("execution_result"),
            "optimization_history": result.get("optimization_history", []),
            "iterations": result.get("iterations", 0),
            "error": result.get("error")
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"SQL执行和优化失败: {str(e)}")


@router.post("/create-session", response_model=dict)
async def create_session(
    session_name: str,
    description: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """创建新的对话会话"""
    try:
        session_id = await tdpilot_service.create_session(
            user_id=current_user["id"],
            session_name=session_name,
            description=description
        )

        return {
            "success": True,
            "session_id": session_id,
            "message": "会话创建成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"会话创建失败: {str(e)}")


@router.post("/upload-requirements", response_model=dict)
async def upload_requirements_file(
    file: UploadFile = File(...),
    current_user: dict = Depends(get_current_user)
):
    """上传需求文件进行批量处理"""
    try:
        # 验证文件类型
        if not file.filename.endswith(('.txt', '.md', '.doc', '.docx')):
            raise HTTPException(status_code=400, detail="不支持的文件类型")
        
        # 读取文件内容
        content = await file.read()
        
        # 这里应该处理文件内容并进行批量分析
        return {
            "message": "文件上传成功",
            "filename": file.filename,
            "size": len(content),
            "processing_id": "proc_123456"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


@router.get("/history", response_model=List[dict])
async def get_generation_history(
    current_user: dict = Depends(get_current_user),
    limit: int = 20,
    offset: int = 0,
    type_filter: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取生成历史记录"""
    try:
        user_id = current_user["id"]
        history_items = []

        # 根据类型筛选决定要获取哪些数据
        should_get_analyses = type_filter is None or type_filter == "requirement_analysis"
        should_get_test_cases = type_filter is None or type_filter == "test_case_generation"
        should_get_sql_generations = type_filter is None or type_filter == "sql_generation"

        # 获取需求分析历史
        if should_get_analyses:
            analyses = db.query(RequirementAnalysis).filter(
                RequirementAnalysis.user_id == user_id
            ).order_by(RequirementAnalysis.created_at.desc()).all()

            for analysis in analyses:
                history_items.append({
                    "id": analysis.id,
                    "type": "requirement_analysis",
                    "title": analysis.title,
                    "status": analysis.status,
                    "requirement_text": analysis.requirement_text[:200] + "..." if len(analysis.requirement_text) > 200 else analysis.requirement_text,
                    "database_type": analysis.database_type,
                    "test_scenarios_count": len(analysis.test_scenarios) if analysis.test_scenarios else 0,
                    "created_at": analysis.created_at.isoformat() if analysis.created_at else None,
                    "completed_at": analysis.completed_at.isoformat() if analysis.completed_at else None
                })

        # 获取测试用例生成历史
        if should_get_test_cases:
            test_cases = db.query(TestCase).filter(
                TestCase.user_id == user_id
            ).order_by(TestCase.created_at.desc()).all()

            for test_case in test_cases:
                # 获取关联的需求分析
                analysis = db.query(RequirementAnalysis).filter(
                    RequirementAnalysis.id == test_case.analysis_id
                ).first()

                history_items.append({
                    "id": test_case.id,
                    "type": "test_case_generation",
                    "title": test_case.title,
                    "status": test_case.status,
                    "analysis_title": analysis.title if analysis else "未知需求",
                    "test_scenarios_count": len(test_case.test_scenarios) if test_case.test_scenarios else 0,
                    "created_at": test_case.created_at.isoformat() if test_case.created_at else None,
                    "completed_at": test_case.completed_at.isoformat() if test_case.completed_at else None
                })

        # 获取SQL生成历史
        if should_get_sql_generations:
            sql_generations = db.query(SQLGeneration).filter(
                SQLGeneration.user_id == user_id
            ).order_by(SQLGeneration.created_at.desc()).all()

            for sql_gen in sql_generations:
                # 获取关联的测试用例和需求分析
                test_case = db.query(TestCase).filter(
                    TestCase.id == sql_gen.test_case_id
                ).first()

                history_items.append({
                    "id": sql_gen.id,
                    "type": "sql_generation",
                    "title": sql_gen.title,
                    "status": sql_gen.status,
                    "database_type": sql_gen.database_type,
                    "test_case_title": test_case.title if test_case else "未知测试用例",
                    "sql_length": len(sql_gen.sql_content) if sql_gen.sql_content else 0,
                    "created_at": sql_gen.created_at.isoformat() if sql_gen.created_at else None,
                    "completed_at": sql_gen.completed_at.isoformat() if sql_gen.completed_at else None
                })

        # 按创建时间排序
        history_items.sort(key=lambda x: x["created_at"] or "", reverse=True)

        # 应用分页
        start_index = offset
        end_index = offset + limit
        paginated_items = history_items[start_index:end_index]

        return paginated_items

    except Exception as e:
        logger.error(f"获取历史记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取历史记录失败: {str(e)}")


@router.get("/history/{item_type}/{item_id}")
async def get_history_detail(
    item_type: str,
    item_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取历史记录详情"""
    try:
        user_id = current_user["id"]

        if item_type == "requirement_analysis":
            item = db.query(RequirementAnalysis).filter(
                RequirementAnalysis.id == item_id,
                RequirementAnalysis.user_id == user_id
            ).first()

            if not item:
                raise HTTPException(status_code=404, detail="需求分析记录不存在")

            return {
                "id": item.id,
                "type": "requirement_analysis",
                "title": item.title,
                "requirement_text": item.requirement_text,
                "background_knowledge": item.background_knowledge,
                "design_document_url": item.design_document_url,
                "database_type": item.database_type,
                "status": item.status,
                "test_scenarios": item.test_scenarios or [],
                "test_points": item.test_points or [],
                "recommendations": item.recommendations or [],
                "analysis_result": item.analysis_result,
                "created_at": item.created_at.isoformat() if item.created_at else None,
                "completed_at": item.completed_at.isoformat() if item.completed_at else None
            }

        elif item_type == "test_case_generation":
            item = db.query(TestCase).filter(
                TestCase.id == item_id,
                TestCase.user_id == user_id
            ).first()

            if not item:
                raise HTTPException(status_code=404, detail="测试用例记录不存在")

            # 获取关联的需求分析
            analysis = db.query(RequirementAnalysis).filter(
                RequirementAnalysis.id == item.analysis_id
            ).first()

            return {
                "id": item.id,
                "type": "test_case_generation",
                "title": item.title,
                "analysis_id": item.analysis_id,
                "analysis_title": analysis.title if analysis else "未知需求",
                "custom_requirements": item.custom_requirements,
                "status": item.status,
                "test_scenarios": item.test_scenarios or [],
                "visualization_data": item.visualization_data or {},
                "created_at": item.created_at.isoformat() if item.created_at else None,
                "completed_at": item.completed_at.isoformat() if item.completed_at else None
            }

        elif item_type == "sql_generation":
            item = db.query(SQLGeneration).filter(
                SQLGeneration.id == item_id,
                SQLGeneration.user_id == user_id
            ).first()

            if not item:
                raise HTTPException(status_code=404, detail="SQL生成记录不存在")

            # 获取关联的测试用例
            test_case = db.query(TestCase).filter(
                TestCase.id == item.test_case_id
            ).first()

            return {
                "id": item.id,
                "type": "sql_generation",
                "title": item.title,
                "test_case_id": item.test_case_id,
                "test_case_title": test_case.title if test_case else "未知测试用例",
                "database_type": item.database_type,
                "status": item.status,
                "sql_content": item.sql_content,
                "test_tables": item.test_tables or [],
                "execution_plan": item.execution_plan or {},
                "created_at": item.created_at.isoformat() if item.created_at else None,
                "completed_at": item.completed_at.isoformat() if item.completed_at else None
            }
        else:
            raise HTTPException(status_code=400, detail="不支持的历史记录类型")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取历史记录详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取历史记录详情失败: {str(e)}")


@router.delete("/history/{item_type}/{item_id}")
async def delete_history_item(
    item_type: str,
    item_id: int,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除历史记录"""
    try:
        user_id = current_user["id"]

        # 根据类型删除对应的记录
        if item_type == "requirement_analysis":
            # 查找记录
            item = db.query(RequirementAnalysis).filter(
                RequirementAnalysis.id == item_id,
                RequirementAnalysis.user_id == user_id
            ).first()

            if not item:
                raise HTTPException(status_code=404, detail="需求分析记录不存在")

            # 删除关联的测试用例记录
            db.query(TestCase).filter(
                TestCase.analysis_id == item_id
            ).delete()

            # 删除需求分析记录
            db.delete(item)

        elif item_type == "test_case_generation":
            # 查找记录
            item = db.query(TestCase).filter(
                TestCase.id == item_id,
                TestCase.user_id == user_id
            ).first()

            if not item:
                raise HTTPException(status_code=404, detail="测试用例生成记录不存在")

            # 删除关联的SQL生成记录
            db.query(SQLGeneration).filter(
                SQLGeneration.test_case_id == item_id
            ).delete()

            # 删除测试用例生成记录
            db.delete(item)

        elif item_type == "sql_generation":
            # 查找记录
            item = db.query(SQLGeneration).filter(
                SQLGeneration.id == item_id,
                SQLGeneration.user_id == user_id
            ).first()

            if not item:
                raise HTTPException(status_code=404, detail="SQL生成记录不存在")

            # 删除SQL生成记录
            db.delete(item)

        else:
            raise HTTPException(status_code=400, detail="不支持的历史记录类型")

        # 提交删除操作
        db.commit()

        logger.info(f"用户 {user_id} 删除了 {item_type} 记录 {item_id}")

        return {
            "success": True,
            "message": "历史记录删除成功",
            "deleted_type": item_type,
            "deleted_id": item_id
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除历史记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除历史记录失败: {str(e)}")


@router.delete("/history/batch")
async def delete_history_batch(
    request: dict,
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """批量删除历史记录"""
    try:
        user_id = current_user["id"]
        items = request.get("items", [])

        if not items:
            raise HTTPException(status_code=400, detail="没有指定要删除的记录")

        deleted_count = 0

        for item in items:
            item_type = item.get("type")
            item_id = item.get("id")

            if not item_type or not item_id:
                continue

            try:
                # 根据类型删除对应的记录
                if item_type == "requirement_analysis":
                    record = db.query(RequirementAnalysis).filter(
                        RequirementAnalysis.id == item_id,
                        RequirementAnalysis.user_id == user_id
                    ).first()

                    if record:
                        # 删除关联的测试用例记录
                        db.query(TestCase).filter(
                            TestCase.analysis_id == item_id
                        ).delete()

                        db.delete(record)
                        deleted_count += 1

                elif item_type == "test_case_generation":
                    record = db.query(TestCase).filter(
                        TestCase.id == item_id,
                        TestCase.user_id == user_id
                    ).first()

                    if record:
                        # 删除关联的SQL生成记录
                        db.query(SQLGeneration).filter(
                            SQLGeneration.test_case_id == item_id
                        ).delete()

                        db.delete(record)
                        deleted_count += 1

                elif item_type == "sql_generation":
                    record = db.query(SQLGeneration).filter(
                        SQLGeneration.id == item_id,
                        SQLGeneration.user_id == user_id
                    ).first()

                    if record:
                        db.delete(record)
                        deleted_count += 1

            except Exception as e:
                logger.warning(f"删除记录 {item_type}:{item_id} 失败: {str(e)}")
                continue

        # 提交删除操作
        db.commit()

        logger.info(f"用户 {user_id} 批量删除了 {deleted_count} 条历史记录")

        return {
            "success": True,
            "message": f"成功删除 {deleted_count} 条历史记录",
            "deleted_count": deleted_count
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"批量删除历史记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量删除历史记录失败: {str(e)}")
