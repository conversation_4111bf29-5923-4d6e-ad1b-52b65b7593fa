"""
LLM模型管理API
"""
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from ...api.v1.auth import get_current_user
from ...config.database import get_async_db
from ...services.llm_service import LLMService
from ...models.llm_model import LLMModel
from ...core.exceptions import LLMError
from sqlalchemy import select, update, delete, func
import json
import asyncio

router = APIRouter()
llm_service = LLMService()


# Pydantic模型
class LLMModelConfig(BaseModel):
    name: str
    provider: str  # openai, anthropic, google, deepseek, ollama, custom
    base_url: Optional[str] = None
    api_key: Optional[str] = None
    model_name: str
    max_tokens: Optional[int] = 32768
    temperature: Optional[float] = 0.7
    enable_stream: Optional[bool] = True
    is_active: bool = True


class LLMModelResponse(BaseModel):
    id: int
    name: str
    provider: str
    model_name: str
    is_active: bool
    enable_stream: bool
    created_at: str
    status: str  # available, unavailable, testing


class LLMTestRequest(BaseModel):
    model_id: int
    test_prompt: str = "Hello, this is a test message."
    tools: Optional[List[Dict[str, Any]]] = None
    tool_choice: Optional[str] = None


class LLMGenerateRequest(BaseModel):
    model_id: int
    messages: List[Dict[str, str]]
    tools: Optional[List[Dict[str, Any]]] = None
    tool_choice: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    enable_stream: Optional[bool] = True


class LLMGenerateResponse(BaseModel):
    success: bool
    content: str
    reasoning_content: str = ""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    response_time: float
    model: str
    tool_calls: List[Dict[str, Any]] = []
    error: Optional[str] = None


@router.get("/models", response_model=List[LLMModelResponse])
async def get_llm_models(
    db: AsyncSession = Depends(get_async_db),
    current_user: dict = Depends(get_current_user)
):
    """获取LLM模型列表"""
    try:
        result = await db.execute(select(LLMModel).where(LLMModel.created_by == current_user["id"]))
        models = result.scalars().all()

        return [
            LLMModelResponse(
                id=model.id,
                name=model.name,
                provider=model.provider,
                model_name=model.model_name,
                is_active=model.is_active,
                enable_stream=model.enable_stream,
                created_at=model.created_at.isoformat(),
                status=model.status
            )
            for model in models
        ]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模型列表失败: {str(e)}")


@router.post("/models", response_model=LLMModelResponse)
async def create_llm_model(
    model_config: LLMModelConfig,
    db: AsyncSession = Depends(get_async_db),
    current_user: dict = Depends(get_current_user)
):
    """创建LLM模型配置"""
    try:
        # 添加调试日志
        print(f"DEBUG: Received model_config: {model_config}")
        print(f"DEBUG: Current user: {current_user}")
        # 创建新的模型配置
        new_model = LLMModel(
            created_by=current_user["id"],
            name=model_config.name,
            provider=model_config.provider,
            model_name=model_config.model_name,
            base_url=model_config.base_url,
            api_key=model_config.api_key,  # 在实际应用中应该加密存储
            max_tokens=model_config.max_tokens,
            temperature=model_config.temperature,
            enable_stream=model_config.enable_stream,
            is_active=model_config.is_active,
            status="unknown"
        )

        db.add(new_model)
        await db.commit()
        await db.refresh(new_model)

        return LLMModelResponse(
            id=new_model.id,
            name=new_model.name,
            provider=new_model.provider,
            model_name=new_model.model_name,
            is_active=new_model.is_active,
            enable_stream=new_model.enable_stream,
            created_at=new_model.created_at.isoformat(),
            status=new_model.status
        )
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"创建模型配置失败: {str(e)}")


@router.put("/models/{model_id}", response_model=LLMModelResponse)
async def update_llm_model(
    model_id: int,
    model_config: LLMModelConfig,
    db: AsyncSession = Depends(get_async_db),
    current_user: dict = Depends(get_current_user)
):
    """更新LLM模型配置"""
    try:
        # 查找模型
        result = await db.execute(
            select(LLMModel).where(
                LLMModel.id == model_id,
                LLMModel.created_by == current_user["id"]
            )
        )
        model = result.scalar_one_or_none()

        if not model:
            raise HTTPException(status_code=404, detail="模型配置不存在")

        # 更新模型配置
        await db.execute(
            update(LLMModel)
            .where(LLMModel.id == model_id)
            .values(
                name=model_config.name,
                provider=model_config.provider,
                model_name=model_config.model_name,
                base_url=model_config.base_url,
                api_key=model_config.api_key,
                max_tokens=model_config.max_tokens,
                temperature=model_config.temperature,
                enable_stream=model_config.enable_stream,
                is_active=model_config.is_active
            )
        )

        await db.commit()

        # 重新获取更新后的模型
        result = await db.execute(select(LLMModel).where(LLMModel.id == model_id))
        updated_model = result.scalar_one()

        return LLMModelResponse(
            id=updated_model.id,
            name=updated_model.name,
            provider=updated_model.provider,
            model_name=updated_model.model_name,
            is_active=updated_model.is_active,
            enable_stream=updated_model.enable_stream,
            created_at=updated_model.created_at.isoformat(),
            status=updated_model.status
        )
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"更新模型配置失败: {str(e)}")


@router.delete("/models/{model_id}", response_model=dict)
async def delete_llm_model(
    model_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: dict = Depends(get_current_user)
):
    """删除LLM模型配置"""
    try:
        # 查找模型
        result = await db.execute(
            select(LLMModel).where(
                LLMModel.id == model_id,
                LLMModel.created_by == current_user["id"]
            )
        )
        model = result.scalar_one_or_none()

        if not model:
            raise HTTPException(status_code=404, detail="模型配置不存在")

        # 删除模型
        await db.execute(delete(LLMModel).where(LLMModel.id == model_id))
        await db.commit()

        return {"message": "模型配置删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"删除模型配置失败: {str(e)}")


@router.post("/models/generate", response_model=LLMGenerateResponse)
async def generate_text(
    generate_request: LLMGenerateRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user: dict = Depends(get_current_user)
):
    """使用LLM模型生成文本"""
    try:
        # 获取模型配置
        result = await db.execute(
            select(LLMModel).where(
                LLMModel.id == generate_request.model_id,
                LLMModel.created_by == current_user["id"]
            )
        )
        model = result.scalar_one_or_none()

        if not model:
            raise HTTPException(status_code=404, detail="模型配置不存在")

        # 构建模型配置
        model_config = {
            "provider": model.provider,
            "model_name": model.model_name,
            "base_url": model.base_url,
            "api_key": model.api_key,
            "max_tokens": generate_request.max_tokens or model.max_tokens,
            "temperature": generate_request.temperature or model.temperature,
            "enable_stream": generate_request.enable_stream if generate_request.enable_stream is not None else model.enable_stream
        }

        # 准备生成参数
        generate_kwargs = {}
        if generate_request.tools:
            generate_kwargs["tools"] = generate_request.tools
        if generate_request.tool_choice:
            generate_kwargs["tool_choice"] = generate_request.tool_choice
        if generate_request.max_tokens:
            generate_kwargs["max_tokens"] = generate_request.max_tokens
        if generate_request.temperature is not None:
            generate_kwargs["temperature"] = generate_request.temperature
        if generate_request.enable_stream is not None:
            generate_kwargs["enable_stream"] = generate_request.enable_stream

        # 调用LLM服务
        result = await llm_service.generate_text(
            model_config=model_config,
            messages=generate_request.messages,
            **generate_kwargs
        )

        return LLMGenerateResponse(**result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文本生成失败: {str(e)}")


@router.post("/models/test", response_model=dict)
async def test_llm_model(
    test_request: LLMTestRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user: dict = Depends(get_current_user)
):
    """测试LLM模型连接"""
    try:
        # 获取模型配置
        result = await db.execute(
            select(LLMModel).where(
                LLMModel.id == test_request.model_id,
                LLMModel.created_by == current_user["id"]
            )
        )
        model = result.scalar_one_or_none()

        if not model:
            raise HTTPException(status_code=404, detail="模型配置不存在")

        # 构建模型配置
        model_config = {
            "provider": model.provider,
            "model_name": model.model_name,
            "base_url": model.base_url,
            "api_key": model.api_key,
            "max_tokens": model.max_tokens,
            "temperature": model.temperature,
            "enable_stream": model.enable_stream
        }

        # 准备测试参数
        test_kwargs = {"enable_stream": model.enable_stream}
        if test_request.tools:
            test_kwargs["tools"] = test_request.tools
        if test_request.tool_choice:
            test_kwargs["tool_choice"] = test_request.tool_choice

        # 测试模型连接
        test_result = await llm_service.generate_text(
            model_config,
            test_request.test_prompt,
            **test_kwargs
        )

        # 添加调试日志
        print(f"DEBUG: Test result for model {test_request.model_id}: {test_result}")

        # 更新模型状态
        status = "available" if test_result.get("success") else "unavailable"
        await db.execute(
            update(LLMModel)
            .where(LLMModel.id == test_request.model_id)
            .values(
                status=status,
                last_tested=func.now(),
                test_result=json.dumps(test_result)
            )
        )
        await db.commit()

        return test_result

    except HTTPException:
        raise
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


@router.post("/models/test-stream")
async def test_llm_model_stream(
    test_request: LLMTestRequest,
    db: AsyncSession = Depends(get_async_db),
    current_user: dict = Depends(get_current_user)
):
    """流式测试LLM模型连接"""
    try:
        # 获取模型配置
        result = await db.execute(
            select(LLMModel).where(
                LLMModel.id == test_request.model_id,
                LLMModel.created_by == current_user["id"]
            )
        )
        model = result.scalar_one_or_none()

        if not model:
            raise HTTPException(status_code=404, detail="模型配置不存在")

        # 构建配置
        config = {
            "provider": model.provider,
            "model_name": model.model_name,
            "base_url": model.base_url,
            "api_key": model.api_key,
            "max_tokens": model.max_tokens,
            "temperature": model.temperature,
            "enable_stream": True  # 强制启用流式输出
        }

        # 返回流式响应
        return StreamingResponse(
            stream_llm_test_response(config, test_request.test_prompt, test_request.tools),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )

    except Exception as e:
        print(f"ERROR: Stream test model failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"流式测试失败: {str(e)}")


@router.get("/providers", response_model=List[dict])
async def get_llm_providers():
    """获取支持的LLM提供商列表"""
    from ...config.settings import settings

    return [
        {
            "name": "openai",
            "display_name": "OpenAI",
            "default_model": settings.OPENAI_MODEL_NAME,
            "default_base_url": settings.OPENAI_BASE_URL,
            "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini"],
            "requires_api_key": True,
            "supports_custom_base_url": True,
            "config_fields": ["model_name", "base_url", "api_key"]
        },
        {
            "name": "anthropic",
            "display_name": "Anthropic Claude",
            "default_model": settings.ANTHROPIC_MODEL_NAME,
            "default_base_url": settings.ANTHROPIC_BASE_URL,
            "models": ["claude-3-haiku-20240307", "claude-3-sonnet-20240229", "claude-3-opus-20240229", "claude-3-5-sonnet-20241022"],
            "requires_api_key": True,
            "supports_custom_base_url": True,
            "config_fields": ["model_name", "base_url", "api_key"]
        },
        {
            "name": "google",
            "display_name": "Google Gemini",
            "default_model": settings.GOOGLE_MODEL_NAME,
            "default_base_url": settings.GOOGLE_BASE_URL,
            "models": ["gemini-pro", "gemini-pro-vision", "gemini-1.5-pro", "gemini-1.5-flash"],
            "requires_api_key": True,
            "supports_custom_base_url": True,
            "config_fields": ["model_name", "base_url", "api_key"]
        },
        {
            "name": "deepseek",
            "display_name": "DeepSeek",
            "default_model": settings.DEEPSEEK_MODEL_NAME,
            "default_base_url": settings.DEEPSEEK_BASE_URL,
            "models": ["deepseek-chat", "deepseek-coder", "deepseek-v2-chat", "deepseek-v2-coder"],
            "requires_api_key": True,
            "supports_custom_base_url": True,
            "config_fields": ["model_name", "base_url", "api_key"]
        },
        {
            "name": "ollama",
            "display_name": "Ollama (本地部署)",
            "default_model": settings.OLLAMA_MODEL_NAME,
            "default_base_url": settings.OLLAMA_BASE_URL,
            "models": ["llama2", "llama3", "codellama", "mistral", "qwen", "gemma"],
            "requires_api_key": False,
            "supports_custom_base_url": True,
            "config_fields": ["model_name", "base_url"]
        },
        {
            "name": "custom",
            "display_name": "自定义模型 (OpenAI兼容)",
            "default_model": "custom-model",
            "default_base_url": "https://your-api-endpoint.com/v1",
            "models": ["custom-model"],
            "requires_api_key": True,
            "supports_custom_base_url": True,
            "config_fields": ["model_name", "base_url", "api_key"]
        }
    ]


async def stream_llm_test_response(config: dict, test_prompt: str, tools: Optional[List[Dict[str, Any]]] = None):
    """流式测试LLM响应的生成器"""
    try:
        # 准备测试参数
        test_kwargs = {"enable_stream": True}
        if tools:
            test_kwargs["tools"] = tools

        # 发送开始信号
        yield f"data: {json.dumps({'type': 'start', 'message': '开始测试...'})}\n\n"

        # 调用LLM服务进行流式测试
        async for chunk in llm_service.test_model_stream(config, test_prompt, **test_kwargs):
            if chunk.get("type") == "content":
                yield f"data: {json.dumps({'type': 'content', 'content': chunk.get('content', '')})}\n\n"
            elif chunk.get("type") == "reasoning":
                yield f"data: {json.dumps({'type': 'reasoning', 'content': chunk.get('content', '')})}\n\n"
            elif chunk.get("type") == "tool_call":
                yield f"data: {json.dumps({'type': 'tool_call', 'tool_call': chunk.get('tool_call', {})})}\n\n"
            elif chunk.get("type") == "metrics":
                yield f"data: {json.dumps({'type': 'metrics', 'prompt_tokens': chunk.get('prompt_tokens', 0), 'completion_tokens': chunk.get('completion_tokens', 0), 'total_tokens': chunk.get('total_tokens', 0), 'model': chunk.get('model', '')})}\n\n"
            elif chunk.get("type") == "error":
                yield f"data: {json.dumps({'type': 'error', 'error': chunk.get('error', '')})}\n\n"
                break

        # 发送完成信号
        yield f"data: {json.dumps({'type': 'complete', 'message': '测试完成'})}\n\n"

    except Exception as e:
        # 发送错误信号
        yield f"data: {json.dumps({'type': 'error', 'error': str(e)})}\n\n"
