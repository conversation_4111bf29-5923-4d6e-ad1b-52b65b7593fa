"""
MCP相关数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON, Float
from sqlalchemy.sql import func
from ..config.database import Base


class DatabaseConnection(Base):
    """数据库连接模型"""
    __tablename__ = "database_connections"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    name = Column(String(100), nullable=False)
    database_type = Column(String(50), nullable=False)  # tdsql-pg, tdsql-oracle, tdsql2, tdsql3, cynosdb
    host = Column(String(255), nullable=False)
    port = Column(Integer, nullable=False)
    database = Column(String(100), nullable=False)
    username = Column(String(100), nullable=False)
    password = Column(String(255), nullable=False)  # 应该加密存储
    connection_params = Column(JSON, nullable=True)  # 额外的连接参数
    
    # 状态信息
    status = Column(String(20), default="disconnected")  # connected, disconnected, error
    last_tested = Column(DateTime(timezone=True), nullable=True)
    test_result = Column(Text, nullable=True)
    
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<DatabaseConnection(id={self.id}, name='{self.name}', type='{self.database_type}')>"


class SQLExecutionLog(Base):
    """SQL执行日志模型"""
    __tablename__ = "sql_execution_logs"

    id = Column(String(255), primary_key=True)  # execution_id
    user_id = Column(Integer, nullable=False, index=True)
    connection_id = Column(Integer, nullable=False, index=True)
    sql_generation_id = Column(Integer, nullable=True, index=True)
    
    # SQL信息
    sql_content = Column(Text, nullable=False)
    database_type = Column(String(50), nullable=False)
    
    # 执行结果
    success = Column(Boolean, default=False)
    execution_time = Column(Float, default=0.0)
    rows_affected = Column(Integer, default=0)
    results = Column(JSON, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # 优化信息
    optimization_iterations = Column(Integer, default=0)
    optimization_history = Column(JSON, nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return f"<SQLExecutionLog(id='{self.id}', success={self.success})>"
