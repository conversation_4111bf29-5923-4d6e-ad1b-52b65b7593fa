"""
知识库相关数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON, Float
from sqlalchemy.sql import func
from ..config.database import Base


class KnowledgeDocument(Base):
    """知识文档模型"""
    __tablename__ = "knowledge_documents"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    title = Column(String(200), nullable=False, index=True)
    content = Column(Text, nullable=False)
    category = Column(String(50), nullable=True, index=True)
    tags = Column(JSON, nullable=True)  # 标签列表
    doc_metadata = Column(JSON, nullable=True)  # 元数据
    
    # 文件信息
    filename = Column(String(255), nullable=True)
    file_path = Column(String(500), nullable=True)
    file_size = Column(Integer, default=0)
    file_type = Column(String(50), nullable=True)
    
    # 向量化信息
    vector_id = Column(String(255), nullable=True, index=True)  # Milvus中的向量ID
    embedding_model = Column(String(100), nullable=True)  # 使用的嵌入模型
    
    # 状态信息
    status = Column(String(20), default="processing")  # processing, indexed, failed
    processing_error = Column(Text, nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    indexed_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<KnowledgeDocument(id={self.id}, title='{self.title}', status='{self.status}')>"


class KnowledgeCategory(Base):
    """知识分类模型"""
    __tablename__ = "knowledge_categories"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False, index=True)
    display_name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    parent_id = Column(Integer, nullable=True)  # 父分类ID，支持层级分类
    sort_order = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<KnowledgeCategory(id={self.id}, name='{self.name}')>"


class KnowledgeTag(Base):
    """知识标签模型"""
    __tablename__ = "knowledge_tags"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    color = Column(String(7), nullable=True)  # 标签颜色，如 #1890ff
    usage_count = Column(Integer, default=0)  # 使用次数
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<KnowledgeTag(id={self.id}, name='{self.name}')>"


class KnowledgeSearch(Base):
    """知识搜索记录模型"""
    __tablename__ = "knowledge_searches"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    query = Column(String(500), nullable=False, index=True)
    category = Column(String(50), nullable=True)
    tags = Column(JSON, nullable=True)
    
    # 搜索结果
    results_count = Column(Integer, default=0)
    results = Column(JSON, nullable=True)  # 搜索结果列表
    search_time = Column(Float, default=0.0)  # 搜索耗时
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return f"<KnowledgeSearch(id={self.id}, query='{self.query}')>"


class KnowledgeGraph(Base):
    """知识图谱节点模型"""
    __tablename__ = "knowledge_graph_nodes"

    id = Column(Integer, primary_key=True, index=True)
    node_id = Column(String(255), unique=True, nullable=False, index=True)  # Neo4j节点ID
    node_type = Column(String(50), nullable=False, index=True)  # 节点类型
    name = Column(String(200), nullable=False, index=True)
    properties = Column(JSON, nullable=True)  # 节点属性
    
    # 关联信息
    document_id = Column(Integer, nullable=True)  # 关联的文档ID
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<KnowledgeGraph(id={self.id}, node_id='{self.node_id}', type='{self.node_type}')>"
