"""
LLM模型配置数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float
from sqlalchemy.sql import func
from ..config.database import Base


class LLMModel(Base):
    """LLM模型配置模型"""
    __tablename__ = "llm_models"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    provider = Column(String(50), nullable=False, index=True)  # openai, anthropic, google, deepseek, ollama, custom
    model_name = Column(String(100), nullable=False)  # 实际的模型标识
    base_url = Column(String(255), nullable=True)  # 自定义API端点
    api_key = Column(String(255), nullable=True)  # 加密存储的API密钥
    max_tokens = Column(Integer, default=32768)
    temperature = Column(Float, default=0.7)
    enable_stream = Column(Boolean, default=True)  # 是否启用流式输出
    is_active = Column(Boolean, default=True)
    status = Column(String(20), default="unknown")  # available, unavailable, testing
    last_tested = Column(DateTime(timezone=True), nullable=True)
    test_result = Column(Text, nullable=True)  # JSON格式的测试结果
    created_by = Column(Integer, nullable=False)  # 创建者用户ID
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<LLMModel(id={self.id}, name='{self.name}', provider='{self.provider}')>"


class LLMModelUsage(Base):
    """LLM模型使用统计"""
    __tablename__ = "llm_model_usage"

    id = Column(Integer, primary_key=True, index=True)
    model_id = Column(Integer, nullable=False, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    usage_type = Column(String(50), nullable=False)  # test_generation, sql_generation, requirement_analysis
    tokens_used = Column(Integer, default=0)
    cost = Column(Float, default=0.0)
    response_time = Column(Float, default=0.0)  # 响应时间（秒）
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return f"<LLMModelUsage(id={self.id}, model_id={self.model_id}, usage_type='{self.usage_type}')>"
