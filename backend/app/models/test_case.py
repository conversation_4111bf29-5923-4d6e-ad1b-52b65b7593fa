"""
测试用例相关数据模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON, Float
from sqlalchemy.sql import func
from ..config.database import Base


class RequirementAnalysis(Base):
    """需求分析模型"""
    __tablename__ = "requirement_analyses"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    title = Column(String(200), nullable=False)
    requirement_text = Column(Text, nullable=False)
    background_knowledge = Column(Text, nullable=True)
    design_document_url = Column(String(500), nullable=True)
    database_type = Column(String(50), nullable=False)  # tdsql-pg, tdsql-oracle, tdsql2, tdsql3, cynosdb
    llm_model_id = Column(Integer, nullable=False)
    
    # 分析结果
    status = Column(String(20), default="analyzing")  # analyzing, completed, failed
    test_scenarios = Column(JSON, nullable=True)  # 测试场景列表
    test_points = Column(JSON, nullable=True)  # 测试点列表
    recommendations = Column(JSON, nullable=True)  # 建议列表
    analysis_result = Column(Text, nullable=True)  # 完整的分析结果
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<RequirementAnalysis(id={self.id}, title='{self.title}', status='{self.status}')>"


class TestCase(Base):
    """测试用例模型"""
    __tablename__ = "test_cases"

    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, nullable=False, index=True)  # 关联的需求分析ID
    user_id = Column(Integer, nullable=False, index=True)
    title = Column(String(200), nullable=False)
    llm_model_id = Column(Integer, nullable=False)
    
    # 测试用例内容
    test_scenarios = Column(JSON, nullable=True)  # 测试场景和用例
    visualization_data = Column(JSON, nullable=True)  # 可视化数据
    custom_requirements = Column(Text, nullable=True)  # 用户自定义需求

    # 调试和错误信息
    error_message = Column(Text, nullable=True)  # 错误消息
    debug_info = Column(JSON, nullable=True)  # 调试信息，包含LLM原始输出等

    status = Column(String(20), default="generating")  # generating, completed, failed
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<TestCase(id={self.id}, title='{self.title}', status='{self.status}')>"


class SQLGeneration(Base):
    """SQL生成模型"""
    __tablename__ = "sql_generations"

    id = Column(Integer, primary_key=True, index=True)
    test_case_id = Column(Integer, nullable=False, index=True)  # 关联的测试用例ID
    user_id = Column(Integer, nullable=False, index=True)
    title = Column(String(200), nullable=False)
    database_type = Column(String(50), nullable=False)
    llm_model_id = Column(Integer, nullable=False)

    # 生成配置
    custom_requirements = Column(Text, nullable=True)  # 用户自定义需求
    use_ai_generation = Column(Boolean, default=True)  # 是否使用AI生成
    generation_method = Column(String(20), default="ai")  # ai, template, hybrid

    # SQL内容
    sql_content = Column(Text, nullable=True)  # 生成的SQL脚本
    test_tables = Column(JSON, nullable=True)  # 测试表列表
    execution_plan = Column(JSON, nullable=True)  # 执行计划
    scenario_count = Column(Integer, default=0)  # 测试场景数量

    # 生成统计
    generation_time = Column(Float, default=0.0)  # 生成耗时(秒)
    tokens_used = Column(Integer, default=0)  # 使用的Token数量

    # 状态和错误信息
    status = Column(String(20), default="generating")  # generating, completed, failed, executed
    error_message = Column(Text, nullable=True)  # 错误消息

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    generated_at = Column(DateTime(timezone=True), nullable=True)  # 生成完成时间

    def __repr__(self):
        return f"<SQLGeneration(id={self.id}, title='{self.title}', status='{self.status}')>"


class SQLExecution(Base):
    """SQL执行记录模型"""
    __tablename__ = "sql_executions"

    id = Column(String(255), primary_key=True)  # execution_id
    sql_generation_id = Column(Integer, nullable=False, index=True)
    user_id = Column(Integer, nullable=False, index=True)
    database_connection_id = Column(Integer, nullable=False)
    
    # 执行信息
    sql_content = Column(Text, nullable=False)
    execution_mode = Column(String(20), default="single")  # single, batch, transaction
    
    # 执行结果
    success = Column(Boolean, default=False)
    results = Column(JSON, nullable=True)  # 执行结果列表
    error_message = Column(Text, nullable=True)
    execution_time = Column(Float, default=0.0)
    rows_affected = Column(Integer, default=0)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    def __repr__(self):
        return f"<SQLExecution(id='{self.id}', success={self.success})>"
