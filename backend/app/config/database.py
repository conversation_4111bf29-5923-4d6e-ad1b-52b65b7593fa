"""
数据库配置和连接管理
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from .settings import settings
import redis
from pymilvus import connections
from neo4j import GraphDatabase
from typing import Generator, AsyncGenerator


# SQLAlchemy配置
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=settings.DEBUG
)

async_engine = create_async_engine(
    settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
    pool_pre_ping=True,
    pool_recycle=300,
    echo=settings.DEBUG
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AsyncSessionLocal = sessionmaker(
    async_engine, class_=AsyncSession, expire_on_commit=False
)

Base = declarative_base()


# 数据库依赖注入
def get_db() -> Generator:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话"""
    async with AsyncSessionLocal() as session:
        yield session


# Redis连接
redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)


def get_redis():
    """获取Redis客户端"""
    return redis_client


# Milvus连接
def connect_milvus():
    """连接Milvus向量数据库"""
    try:
        connections.connect(
            alias="default",
            host=settings.MILVUS_HOST,
            port=settings.MILVUS_PORT
        )
        return True
    except Exception as e:
        print(f"Failed to connect to Milvus: {e}")
        return False


def get_milvus_connection():
    """获取Milvus连接"""
    if not connect_milvus():
        raise ConnectionError("Cannot connect to Milvus")
    return True


# Neo4j连接
class Neo4jConnection:
    """Neo4j数据库连接管理"""
    
    def __init__(self):
        self.driver = None
    
    def connect(self):
        """连接Neo4j数据库"""
        try:
            self.driver = GraphDatabase.driver(
                settings.NEO4J_URI,
                auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
            )
            return True
        except Exception as e:
            print(f"Failed to connect to Neo4j: {e}")
            return False
    
    def close(self):
        """关闭连接"""
        if self.driver:
            self.driver.close()
    
    def get_session(self):
        """获取Neo4j会话"""
        if not self.driver:
            if not self.connect():
                raise ConnectionError("Cannot connect to Neo4j")
        return self.driver.session()


# 全局Neo4j连接实例
neo4j_connection = Neo4jConnection()


def get_neo4j_session():
    """获取Neo4j会话"""
    return neo4j_connection.get_session()


# 数据库初始化
async def init_database():
    """初始化数据库"""
    # 创建所有表
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # 初始化Milvus
    connect_milvus()
    
    # 初始化Neo4j
    neo4j_connection.connect()


# 数据库清理
async def close_database():
    """关闭数据库连接"""
    await async_engine.dispose()
    redis_client.close()
    neo4j_connection.close()
