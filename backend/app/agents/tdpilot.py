"""
TDPilot AI智能体 - 核心智能体类
"""
import asyncio
import json
import time
from typing import Dict, List, Optional, Any
from ..services.llm_service import llm_service
from ..services.mcp_service import mcp_service
from ..services.memory_service import memory_service
from ..core.exceptions import LLMError, MCPError
from ..utils.logger import logger


class TDPilotAgent:
    """TDPilot AI智能体"""
    
    def __init__(self, model_config: Dict[str, Any], user_id: int, session_id: Optional[str] = None):
        self.model_config = model_config
        self.user_id = user_id
        self.session_id = session_id
        self.context_memory = []
        self.sql_execution_history = []  # SQL执行历史
        self.system_prompts = {
            "requirement_analysis": self._get_requirement_analysis_prompt(),
            "test_case_generation": self._get_test_case_generation_prompt(),
            "sql_generation": self._get_sql_generation_prompt(),
            "sql_optimization": self._get_sql_optimization_prompt()
        }
    
    def _get_requirement_analysis_prompt(self) -> str:
        """获取需求分析系统提示"""
        return """
你是TDPilot，一个专业的数据库测试需求分析专家。你的任务是深度分析用户的测试需求，提取关键信息并生成全面的测试方案。

## 分析流程：
1. 分析用户提供的需求文本，提取核心功能点
2. 如果有背景知识，深入分析并整合到需求理解中
3. 如果有设计文档，提取关键的技术细节和约束条件
4. 识别测试场景和测试点
5. 提供测试建议和最佳实践

## 输出格式：
以JSON格式输出分析结果(必须输出这个JSON格式，只输出这个)：
{
    "test_scenarios": [
        {
            "scenario": "场景名称",
            "description": "场景描述",
            "test_points": ["测试点1", "测试点2", ...]
        }
    ],
    "recommendations": ["建议1", "建议2", ...],
    "database_considerations": ["数据库相关考虑1", "考虑2", ...],
    "risk_points": ["风险点1", "风险点2", ...]
}

## 注意事项：
- 考虑分布式数据库的特性
- 关注数据一致性和事务处理
- 重视性能和并发测试
- 考虑边界条件和异常情况
"""
    
    def _get_test_case_generation_prompt(self) -> str:
        """获取测试用例生成系统提示"""
        return """
你是TDPilot，一个专业的测试用例生成专家。基于需求分析结果，生成详细的测试用例。

## 核心要求：数量保证
**重要：必须严格按照输入的测试点数量生成测试用例！**
- 如果输入有N个测试点，必须生成至少N×2个测试用例
- 每个测试点必须生成至少2个不同类型的测试用例
- 绝对不能遗漏任何测试点
- 确保输出的summary中的数量统计准确

## 层级结构：
请严格按照以下三级层级结构生成测试用例：
1. **测试场景** - 对应需求分析中的每个测试场景
2. **测试点** - 每个场景下的具体测试点（对应需求分析中的test_points）
3. **测试用例** - 每个测试点下生成至少2个具体的测试用例

## 测试用例类型组合：
为每个测试点生成以下类型的用例组合：
1. **正向功能用例** + **边界测试用例**
2. **功能测试用例** + **异常测试用例**
3. **性能测试用例** + **安全测试用例**
4. **并发测试用例** + **数据一致性用例**

## 输出格式：
**重要：必须输出完整、有效的JSON格式！**
- 只输出JSON，不要添加任何解释文字
- 确保JSON格式正确，所有括号和引号都匹配
- 如果内容较长，确保完整输出，不要截断

请严格按照以下JSON格式输出，确保三级层级结构：
{
    "test_scenarios": [
        {
            "scenario_id": "S001",
            "scenario_name": "场景名称",
            "scenario_description": "场景描述",
            "test_points": [
                {
                    "point_id": "P001",
                    "point_name": "测试点名称",
                    "point_description": "测试点描述",
                    "test_cases": [
                        {
                            "case_id": "TC001",
                            "name": "正向功能测试用例",
                            "description": "详细描述",
                            "preconditions": ["前置条件1", "前置条件2"],
                            "test_steps": ["步骤1", "步骤2", "步骤3"],
                            "expected_result": "预期结果",
                            "test_data": "测试数据要求",
                            "priority": "High",
                            "category": "功能"
                        },
                        {
                            "case_id": "TC002",
                            "name": "边界值测试用例",
                            "description": "详细描述",
                            "preconditions": ["前置条件1"],
                            "test_steps": ["步骤1", "步骤2"],
                            "expected_result": "预期结果",
                            "test_data": "边界值测试数据",
                            "priority": "Medium",
                            "category": "边界"
                        }
                    ]
                }
            ]
        }
    ],
    "test_coverage": {
        "functional": 85,
        "boundary": 70,
        "exception": 60,
        "performance": 50,
        "security": 40
    },
    "summary": {
        "total_scenarios": 0,
        "total_test_points": 0,
        "total_test_cases": 0
    }
}

## 生成要求：
- **数量验证**：生成前先统计输入的测试点总数，确保生成的测试用例数量 >= 测试点数量 × 2
- 基于需求分析结果中的每个测试场景，生成对应的测试场景
- 对于每个场景中的测试点，必须生成对应的测试点结构
- 每个测试点必须包含至少2个不同类型的测试用例
- 确保测试用例的完整性和可执行性
- 考虑分布式数据库的特殊场景
- 提供清晰的测试步骤和预期结果
- 测试用例ID格式：TC001, TC002, TC003...（全局唯一）
- 测试点ID格式：P001, P002, P003...（全局唯一）
- 场景ID格式：S001, S002, S003...（全局唯一）
- **最终验证**：生成完成后，确保summary中的统计数据准确反映实际生成的数量
"""
    
    def _get_sql_generation_prompt(self) -> str:
        """获取SQL生成系统提示"""
        return """
你是TDPilot，一个专业的SQL测试脚本生成专家。基于测试用例生成复杂、全面、针对性强的SQL测试脚本。

## SQL生成规则：
1. 每个测试用例生成至少3个不同的SQL语句
2. SQL必须语法正确，无错误
3. 分布式数据库查询中，有LIMIT的必须包含ORDER BY
4. 使用批量函数进行数据插入
5. 包含表创建、数据插入、查询、更新、删除等完整流程

## 数据库类型支持：
- TDSQL-PG: PostgreSQL语法
- TDSQL-Oracle: Oracle兼容语法
- TDSQL2/TDSQL3: MySQL语法
- CynosDB: MySQL语法

## 输出格式：
生成完整的SQL脚本，格式如下：
```sql
/*
测试方案：{test_plan_name}
数据库类型：{database_type}
生成时间：{timestamp}
*/

-- 创建测试表
CREATE TABLE test_table_name (
    id SERIAL PRIMARY KEY,
    ...
);

-- 批量插入测试数据
INSERT INTO test_table_name (...) VALUES
(...),
(...),
(...);

-- TestPoint 1: {test_case_name}
-- 测试SQL 1
{sql_statement_1};

-- 测试SQL 2
{sql_statement_2};

-- 测试SQL 3
{sql_statement_3};

-- TestPoint 2: {test_case_name_2}
...
```

## 注意事项：
- 确保SQL语法正确
- 考虑分布式数据库特性
- 包含适当的索引和约束
- 提供有意义的测试数据
- 考虑事务和并发场景
"""

    def _get_sql_optimization_prompt(self) -> str:
        """获取SQL优化系统提示"""
        return """
你是TDPilot，一个专业的SQL优化专家。基于SQL执行结果，分析问题并提供优化建议。

## 分析任务：
1. 分析SQL执行结果和错误信息
2. 识别性能问题和语法错误
3. 提供具体的优化建议
4. 生成改进后的SQL脚本

## 输出格式：
请以JSON格式输出分析结果：
{
    "analysis": {
        "success_rate": 85,
        "performance_issues": ["问题1", "问题2"],
        "syntax_errors": ["错误1", "错误2"],
        "optimization_suggestions": ["建议1", "建议2"]
    },
    "optimized_sql": "优化后的SQL脚本",
    "explanation": "优化说明"
}

## 优化重点：
- 修复语法错误
- 优化查询性能
- 改进索引使用
- 优化分布式查询
- 减少数据传输量
"""
    
    async def _generate_with_memory_context(
        self,
        prompt: str,
        query: str,
        task_type: str = "general"
    ) -> Dict[str, Any]:
        """使用记忆上下文生成响应"""
        try:
            # 如果有会话ID，获取上下文记忆
            if self.session_id:
                context = await memory_service.get_context_for_llm(
                    user_id=self.user_id,
                    session_id=self.session_id,
                    current_query=query
                )

                # 构建包含记忆的完整提示
                memory_context = self._build_memory_context(context)
                full_prompt = f"{memory_context}\n\n{prompt}"

                # 记录用户查询
                await memory_service.add_message(
                    session_id=self.session_id,
                    role="user",
                    content=query,
                    metadata={"task_type": task_type}
                )
            else:
                full_prompt = prompt

            # 调用LLM生成响应
            result = await llm_service.generate_text(
                self.model_config,
                full_prompt,
                temperature=0.3
            )

            # 如果有会话ID，记录助手响应
            if self.session_id and result["success"]:
                await memory_service.add_message(
                    session_id=self.session_id,
                    role="assistant",
                    content=result["content"],
                    metadata={
                        "task_type": task_type,
                        "tokens_used": result.get("tokens_used", 0),
                        "response_time": result.get("response_time", 0)
                    }
                )

            return result

        except Exception as e:
            logger.error(f"记忆增强生成失败: {str(e)}")
            # 降级到普通生成
            return await llm_service.generate_text(self.model_config, prompt, temperature=0.3)

    def _build_memory_context(self, context: Dict[str, Any]) -> str:
        """构建记忆上下文字符串"""
        memory_parts = []

        # 添加相关的长期记忆
        if context.get("relevant_memories"):
            memory_parts.append("## 相关历史记忆：")
            for memory in context["relevant_memories"][:3]:  # 只取前3个最相关的
                memory_parts.append(f"- {memory['content'][:200]}...")

        # 添加最近的对话历史
        if context.get("recent_conversation"):
            memory_parts.append("\n## 最近对话历史：")
            for msg in context["recent_conversation"][-5:]:  # 只取最近5条
                role = "用户" if msg.get("role") == "user" else "助手"
                content = msg.get("content", "")[:150]
                memory_parts.append(f"{role}: {content}...")

        return "\n".join(memory_parts) if memory_parts else ""

    async def analyze_requirement(
        self,
        requirement_text: str,
        background_knowledge: Optional[str] = None,
        design_document_url: Optional[str] = None,
        database_type: str = "tdsql-pg"
    ) -> Dict[str, Any]:
        """分析用户需求"""
        try:
            # 构建完整的提示
            prompt = f"""
{self.system_prompts["requirement_analysis"]}

## 用户需求：
{requirement_text}

## 背景知识：
{background_knowledge or "无"}

## 设计文档：
{design_document_url or "无"}

## 目标数据库类型：
{database_type}

请分析以上需求并生成测试方案。
"""
            
            # 使用记忆增强的生成
            result = await self._generate_with_memory_context(
                prompt=prompt,
                query=requirement_text,
                task_type="requirement_analysis"
            )
            
            if not result["success"]:
                raise LLMError(f"需求分析失败-5: {result['error']}")
            
            # 尝试解析JSON结果
            try:
                analysis_result = json.loads(result["content"])
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}")
                # 如果不是有效JSON，返回原始文本
                analysis_result = {
                    "raw_analysis": result["content"],
                    "test_scenarios": [],
                    "recommendations": [],
                    "database_considerations": [],
                    "risk_points": []
                }
            
            # 记录到上下文记忆
            self.context_memory.append({
                "type": "requirement_analysis",
                "input": {
                    "requirement_text": requirement_text,
                    "background_knowledge": background_knowledge,
                    "design_document_url": design_document_url,
                    "database_type": database_type
                },
                "output": analysis_result,
                "tokens_used": result.get("tokens_used", 0),
                "response_time": result.get("response_time", 0)
            })
            
            return {
                "success": True,
                "analysis_result": analysis_result,
                "tokens_used": result.get("tokens_used", 0),
                "response_time": result.get("response_time", 0)
            }
            
        except Exception as e:
            logger.error(f"需求分析失败-4: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def generate_test_cases(
        self,
        analysis_result: Dict[str, Any],
        custom_requirements: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成测试用例"""
        try:
            # 构建提示
            prompt = f"""
{self.system_prompts["test_case_generation"]}

## 需求分析结果：
{json.dumps(analysis_result, ensure_ascii=False, indent=2)}

## 用户自定义需求：
{custom_requirements or "无"}

请基于以上分析结果生成详细的测试用例。
"""

            logger.info(f"开始生成测试用例，用户ID: {self.user_id}")
            logger.debug(f"使用的提示词长度: {len(prompt)} 字符")
            logger.debug(f"模型配置: {self.model_config}")

            # 调用LLM生成测试用例
            result = await llm_service.generate_text(
                self.model_config,
                prompt,
                temperature=0.4
            )

            # 详细记录LLM响应
            logger.info(f"LLM调用完成，成功状态: {result.get('success')}")
            logger.info(f"响应时间: {result.get('response_time', 0):.2f}秒")
            logger.info(f"Token使用情况 - 提示: {result.get('prompt_tokens', 0)}, 完成: {result.get('completion_tokens', 0)}, 总计: {result.get('total_tokens', 0)}")

            if not result["success"]:
                error_msg = f"LLM调用失败: {result.get('error', '未知错误')}"
                logger.error(error_msg)
                raise LLMError(error_msg)

            # 记录原始LLM输出
            raw_content = result.get("content", "")
            reasoning_content = result.get("reasoning_content", "")

            logger.info(f"LLM原始输出长度: {len(raw_content)} 字符")
            if reasoning_content:
                logger.info(f"LLM推理内容长度: {len(reasoning_content)} 字符")
                logger.debug(f"LLM推理内容: {reasoning_content[:500]}...")

            # 记录完整的原始输出用于调试
            logger.debug(f"LLM完整原始输出: {raw_content}")

            # 解析结果
            test_cases = None
            json_parse_error = None

            try:
                test_cases = json.loads(raw_content)
                logger.info("JSON解析成功")
                logger.debug(f"解析后的数据结构: {list(test_cases.keys()) if isinstance(test_cases, dict) else type(test_cases)}")
            except json.JSONDecodeError as e:
                json_parse_error = str(e)
                logger.error(f"JSON解析失败: {json_parse_error}")
                logger.error(f"解析失败的内容: {raw_content[:1000]}...")

                # 尝试提取JSON部分
                try:
                    logger.info(f"尝试从内容中提取JSON，内容长度: {len(raw_content)}")

                    # 查找JSON代码块
                    import re
                    json_pattern = r'```json\s*(.*?)\s*```'
                    json_match = re.search(json_pattern, raw_content, re.DOTALL)
                    if json_match:
                        json_content = json_match.group(1)
                        test_cases = json.loads(json_content)
                        logger.info("从代码块中成功提取并解析JSON")
                    else:
                        # 查找大括号包围的JSON
                        brace_pattern = r'\{.*\}'
                        brace_match = re.search(brace_pattern, raw_content, re.DOTALL)
                        if brace_match:
                            json_content = brace_match.group(0)
                            logger.info(f"找到JSON对象，长度: {len(json_content)}")
                            test_cases = json.loads(json_content)
                            logger.info("从大括号内容中成功提取并解析JSON")
                        else:
                            logger.warning("无法找到有效的JSON内容")
                            raise json.JSONDecodeError("无法找到有效的JSON内容", raw_content, 0)
                except (json.JSONDecodeError, AttributeError) as e2:
                    logger.error(f"JSON提取和解析都失败: {str(e2)}")
                    logger.error(f"原始内容前1000字符: {raw_content[:1000]}")

                    # 创建包含原始内容的fallback结构
                    test_cases = {
                        "test_scenarios": [
                            {
                                "scenario": "JSON解析失败",
                                "description": "LLM生成的内容无法解析为有效JSON",
                                "test_cases": [
                                    {
                                        "case_name": "查看原始生成内容",
                                        "description": "JSON解析失败，请查看调试信息中的原始内容",
                                        "steps": ["检查LLM生成的原始内容", "修复JSON格式问题"],
                                        "expected_result": "能够正确解析JSON格式的测试用例"
                                    }
                                ]
                            }
                        ],
                        "test_coverage": {},
                        "parse_error": f"初始解析失败: {json_parse_error}, 提取解析失败: {str(e2)}",
                        "raw_content": raw_content,
                        "reasoning_content": reasoning_content
                    }

            # 验证解析后的数据结构和数量
            if test_cases and isinstance(test_cases, dict):
                if "test_scenarios" in test_cases:
                    scenarios_count = len(test_cases["test_scenarios"])
                    logger.info(f"成功解析到 {scenarios_count} 个测试场景")

                    # 统计实际生成的测试用例数量
                    actual_test_cases = 0
                    actual_test_points = 0
                    for scenario in test_cases["test_scenarios"]:
                        test_points = scenario.get("test_points", [])
                        actual_test_points += len(test_points)
                        for point in test_points:
                            actual_test_cases += len(point.get("test_cases", []))

                    # 从输入数据中获取预期的测试点数量
                    expected_test_points = analysis_result.get("total_test_points", 0)
                    expected_test_cases = expected_test_points * 2

                    logger.info(f"测试用例生成统计: 预期{expected_test_points}个测试点/{expected_test_cases}个用例, 实际{actual_test_points}个测试点/{actual_test_cases}个用例")

                    # 如果生成的用例数量不足，记录警告
                    if actual_test_cases < expected_test_cases:
                        shortage = expected_test_cases - actual_test_cases
                        logger.warning(f"测试用例生成数量不足，缺少 {shortage} 个用例")

                        # 更新summary信息
                        if "summary" not in test_cases:
                            test_cases["summary"] = {}
                        test_cases["summary"].update({
                            "expected_test_points": expected_test_points,
                            "expected_test_cases": expected_test_cases,
                            "actual_test_points": actual_test_points,
                            "actual_test_cases": actual_test_cases,
                            "generation_complete": actual_test_cases >= expected_test_cases,
                            "shortage": shortage if actual_test_cases < expected_test_cases else 0
                        })
                    else:
                        logger.info("测试用例生成数量符合预期")
                        if "summary" not in test_cases:
                            test_cases["summary"] = {}
                        test_cases["summary"].update({
                            "expected_test_points": expected_test_points,
                            "expected_test_cases": expected_test_cases,
                            "actual_test_points": actual_test_points,
                            "actual_test_cases": actual_test_cases,
                            "generation_complete": True,
                            "shortage": 0
                        })
                else:
                    logger.warning("解析的JSON中缺少 'test_scenarios' 字段")
                    test_cases["test_scenarios"] = []

                if "test_coverage" not in test_cases:
                    logger.warning("解析的JSON中缺少 'test_coverage' 字段")
                    test_cases["test_coverage"] = {}
            else:
                logger.error(f"解析结果不是有效的字典类型: {type(test_cases)}")
                test_cases = {
                    "raw_content": raw_content,
                    "reasoning_content": reasoning_content,
                    "test_scenarios": [],
                    "test_coverage": {},
                    "parse_error": "解析结果不是字典类型"
                }

            # 记录到上下文记忆
            self.context_memory.append({
                "type": "test_case_generation",
                "input": {
                    "analysis_result": analysis_result,
                    "custom_requirements": custom_requirements
                },
                "output": test_cases,
                "tokens_used": result.get("total_tokens", 0),
                "response_time": result.get("response_time", 0),
                "llm_raw_output": raw_content,
                "llm_reasoning": reasoning_content,
                "json_parse_success": json_parse_error is None
            })

            return {
                "success": True,
                "test_cases": test_cases,
                "tokens_used": result.get("total_tokens", 0),
                "response_time": result.get("response_time", 0),
                "debug_info": {
                    "llm_raw_output": raw_content,
                    "llm_reasoning": reasoning_content,
                    "json_parse_error": json_parse_error,
                    "prompt_length": len(prompt)
                }
            }

        except Exception as e:
            error_msg = f"测试用例生成失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                "success": False,
                "error": error_msg,
                "debug_info": {
                    "exception_type": type(e).__name__,
                    "exception_details": str(e)
                }
            }
    
    async def generate_sql_scripts(
        self,
        test_cases: Dict[str, Any],
        database_type: str = "tdsql-pg"
    ) -> Dict[str, Any]:
        """生成SQL测试脚本"""
        try:
            # 构建提示
            prompt = f"""
{self.system_prompts["sql_generation"]}

## 测试用例：
{json.dumps(test_cases, ensure_ascii=False, indent=2)}

## 目标数据库类型：
{database_type}

请基于以上测试用例生成完整的SQL测试脚本。
"""

            # 调用LLM生成SQL脚本
            result = await llm_service.generate_text(
                self.model_config,
                prompt,
                temperature=0.2  # 使用更低的温度确保SQL语法正确
            )

            if not result["success"]:
                raise LLMError(f"SQL脚本生成失败: {result['error']}")

            sql_content = result["content"]

            # 提取测试表信息（简单的正则匹配）
            import re
            table_matches = re.findall(r'CREATE TABLE\s+(\w+)', sql_content, re.IGNORECASE)
            test_tables = list(set(table_matches))

            # 记录到上下文记忆
            self.context_memory.append({
                "type": "sql_generation",
                "input": {
                    "test_cases": test_cases,
                    "database_type": database_type
                },
                "output": {
                    "sql_content": sql_content,
                    "test_tables": test_tables
                },
                "tokens_used": result.get("tokens_used", 0),
                "response_time": result.get("response_time", 0)
            })

            return {
                "success": True,
                "sql_content": sql_content,
                "test_tables": test_tables,
                "tokens_used": result.get("tokens_used", 0),
                "response_time": result.get("response_time", 0)
            }

        except Exception as e:
            logger.error(f"SQL脚本生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def generate_sql_scripts_stream(
        self,
        test_cases: Dict[str, Any],
        database_type: str = "tdsql-pg"
    ):
        """流式生成SQL测试脚本"""
        try:
            # 构建提示
            prompt = f"""
{self.system_prompts["sql_generation"]}

## 测试用例：
{json.dumps(test_cases, ensure_ascii=False, indent=2)}

## 目标数据库类型：
{database_type}

请基于以上测试用例生成完整的SQL测试脚本。
"""

            # 构建消息格式
            messages = [{"role": "user", "content": prompt}]

            # 创建LLM提供商实例
            provider = llm_service.create_provider(self.model_config)

            sql_content = ""
            reasoning_content = ""

            # 流式调用LLM
            async for chunk in provider.generate_stream(
                messages,
                temperature=0.2,
                enable_stream=True
            ):
                if chunk.get("type") == "content":
                    content = chunk.get("content", "")
                    sql_content += content
                    # 发送内容块
                    yield {
                        "type": "llm_output",
                        "content": content
                    }
                elif chunk.get("type") == "reasoning":
                    # 累积思考过程内容
                    reasoning_chunk = chunk.get("content", "")
                    reasoning_content += reasoning_chunk
                    # 发送思考过程块（不加标记）
                    yield {
                        "type": "llm_reasoning",
                        "content": reasoning_chunk
                    }
                elif chunk.get("type") == "error":
                    yield {
                        "type": "error",
                        "message": chunk.get("error", "生成失败")
                    }
                    return

            # 提取测试表信息
            import re
            table_matches = re.findall(r'CREATE TABLE\s+(\w+)', sql_content, re.IGNORECASE)
            test_tables = list(set(table_matches))

            # 记录到上下文记忆
            self.context_memory.append({
                "type": "sql_generation",
                "input": {
                    "test_cases": test_cases,
                    "database_type": database_type
                },
                "output": {
                    "sql_content": sql_content,
                    "test_tables": test_tables
                }
            })

            # 发送完成信号
            yield {
                "type": "complete",
                "data": {
                    "success": True,
                    "sql_content": sql_content,
                    "test_tables": test_tables
                }
            }

        except Exception as e:
            logger.error(f"SQL脚本流式生成失败: {str(e)}")
            yield {
                "type": "error",
                "message": str(e)
            }

    async def generate_test_cases_stream(
        self,
        analysis_result: Dict[str, Any],
        custom_requirements: Optional[str] = None
    ):
        """流式生成测试用例"""
        try:
            # 构建提示
            prompt = f"""
{self.system_prompts["test_case_generation"]}

## 需求分析结果：
{json.dumps(analysis_result, ensure_ascii=False, indent=2)}

## 自定义需求：
{custom_requirements or "无"}

请基于以上分析结果生成详细的测试用例。
"""

            # 构建消息格式
            messages = [{"role": "user", "content": prompt}]

            # 创建LLM提供商实例
            provider = llm_service.create_provider(self.model_config)

            test_cases_content = ""
            reasoning_content = ""

            # 流式调用LLM
            async for chunk in provider.generate_stream(
                messages,
                temperature=0.3,
                enable_stream=True
            ):
                if chunk.get("type") == "content":
                    content = chunk.get("content", "")
                    test_cases_content += content
                    # 发送内容块
                    yield {
                        "type": "llm_output",
                        "content": content.strip()
                    }
                elif chunk.get("type") == "reasoning":
                    # 累积思考过程内容
                    reasoning_chunk = chunk.get("content", "")
                    reasoning_content += reasoning_chunk
                    # 发送思考过程块（不加标记）
                    yield {
                        "type": "llm_reasoning",
                        "content": reasoning_chunk.strip()
                    }
                elif chunk.get("type") == "error":
                    yield {
                        "type": "error",
                        "message": chunk.get("error", "生成失败")
                    }
                    return

            # 解析生成的测试用例
            logger.info(f"开始解析测试用例，内容长度: {len(test_cases_content)}")
            logger.debug(f"测试用例内容前500字符: {test_cases_content[:500]}")

            try:
                json_content = ""

                # 尝试从JSON格式解析
                if "```json" in test_cases_content:
                    start = test_cases_content.find("```json") + 7
                    end = test_cases_content.find("```", start)
                    if end == -1:
                        # 如果没有找到结束标记，可能JSON被截断了
                        logger.warning("JSON代码块没有结束标记，可能被截断")
                        json_content = test_cases_content[start:].strip()
                    else:
                        json_content = test_cases_content[start:end].strip()
                    logger.info(f"从代码块中提取JSON，长度: {len(json_content)}")
                else:
                    # 尝试查找JSON对象
                    import re
                    # 查找以{开始的JSON对象
                    json_match = re.search(r'\{.*\}', test_cases_content, re.DOTALL)
                    if json_match:
                        json_content = json_match.group(0)
                        logger.info(f"通过正则表达式提取JSON，长度: {len(json_content)}")
                    else:
                        # 如果都没找到，尝试直接解析整个内容
                        json_content = test_cases_content.strip()
                        logger.info("使用整个内容作为JSON")

                # 尝试解析JSON
                test_cases = json.loads(json_content)
                logger.info("JSON解析成功")

                # 验证生成的测试用例数量
                if test_cases and isinstance(test_cases, dict) and "test_scenarios" in test_cases:
                    actual_test_cases = 0
                    actual_test_points = 0
                    for scenario in test_cases["test_scenarios"]:
                        test_points = scenario.get("test_points", [])
                        actual_test_points += len(test_points)
                        for point in test_points:
                            actual_test_cases += len(point.get("test_cases", []))

                    # 从输入数据中获取预期的测试点数量
                    expected_test_points = analysis_result.get("total_test_points", 0)
                    expected_test_cases = expected_test_points * 2

                    logger.info(f"流式生成统计: 预期{expected_test_points}个测试点/{expected_test_cases}个用例, 实际{actual_test_points}个测试点/{actual_test_cases}个用例")

                    # 更新summary信息
                    if "summary" not in test_cases:
                        test_cases["summary"] = {}
                    test_cases["summary"].update({
                        "expected_test_points": expected_test_points,
                        "expected_test_cases": expected_test_cases,
                        "actual_test_points": actual_test_points,
                        "actual_test_cases": actual_test_cases,
                        "generation_complete": actual_test_cases >= expected_test_cases,
                        "shortage": max(0, expected_test_cases - actual_test_cases)
                    })

            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {str(e)}")
                logger.error(f"尝试解析的JSON内容: {json_content[:1000]}...")

                # 如果解析失败，创建一个基本的测试用例结构
                test_cases = {
                    "test_scenarios": [
                        {
                            "scenario": "JSON解析失败",
                            "description": "LLM生成的内容无法解析为有效JSON",
                            "test_cases": [
                                {
                                    "case_name": "查看原始生成内容",
                                    "description": "JSON解析失败，请查看调试信息中的原始内容",
                                    "steps": ["检查LLM生成的原始内容", "修复JSON格式问题"],
                                    "expected_result": "能够正确解析JSON格式的测试用例"
                                }
                            ]
                        }
                    ],
                    "parse_error": f"JSON解析失败: {str(e)}",
                    "raw_content": test_cases_content,
                    "attempted_json": json_content
                }

            # 记录到上下文记忆
            self.context_memory.append({
                "type": "test_case_generation",
                "input": {
                    "analysis_result": analysis_result,
                    "custom_requirements": custom_requirements
                },
                "output": {
                    "test_cases": test_cases
                }
            })

            # 发送完成信号
            yield {
                "type": "complete",
                "data": {
                    "success": True,
                    "test_cases": test_cases
                }
            }

        except Exception as e:
            logger.error(f"测试用例流式生成失败: {str(e)}")
            yield {
                "type": "error",
                "message": str(e)
            }
    
    def get_context_memory(self) -> List[Dict[str, Any]]:
        """获取上下文记忆"""
        return self.context_memory
    
    async def execute_and_optimize_sql(
        self,
        sql_content: str,
        database_config: Dict[str, Any],
        database_type: str,
        max_iterations: int = 3
    ) -> Dict[str, Any]:
        """执行SQL并基于结果进行优化"""
        try:
            optimization_history = []
            current_sql = sql_content

            for iteration in range(max_iterations):
                logger.info(f"SQL执行迭代 {iteration + 1}/{max_iterations}")

                # 执行SQL
                execution_result = await mcp_service.execute_sql(
                    connection_id=database_config.get("connection_id", 1),
                    database_type=database_type,
                    config=database_config,
                    sql=current_sql
                )

                # 记录执行历史
                execution_record = {
                    "iteration": iteration + 1,
                    "sql": current_sql,
                    "result": execution_result,
                    "timestamp": time.time()
                }
                self.sql_execution_history.append(execution_record)
                optimization_history.append(execution_record)

                # 如果执行成功且没有明显问题，结束优化
                if execution_result["success"] and self._is_execution_satisfactory(execution_result):
                    logger.info("SQL执行成功，无需进一步优化")
                    break

                # 如果是最后一次迭代，不再优化
                if iteration == max_iterations - 1:
                    logger.warning("达到最大优化迭代次数")
                    break

                # 基于执行结果进行SQL优化
                optimization_result = await self._optimize_sql_based_on_result(
                    current_sql, execution_result, database_type
                )

                if optimization_result["success"]:
                    current_sql = optimization_result["optimized_sql"]
                    logger.info(f"SQL已优化，准备第{iteration + 2}次执行")
                else:
                    logger.error("SQL优化失败，停止迭代")
                    break

            # 返回最终结果
            final_result = optimization_history[-1]["result"] if optimization_history else execution_result

            return {
                "success": True,
                "final_sql": current_sql,
                "execution_result": final_result,
                "optimization_history": optimization_history,
                "iterations": len(optimization_history)
            }

        except Exception as e:
            logger.error(f"SQL执行和优化失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "sql": sql_content
            }

    def _is_execution_satisfactory(self, execution_result: Dict[str, Any]) -> bool:
        """判断SQL执行结果是否满意"""
        if not execution_result["success"]:
            return False

        # 检查执行时间是否合理（小于10秒）
        if execution_result.get("execution_time", 0) > 10:
            return False

        # 检查是否有错误的语句
        results = execution_result.get("results", [])
        error_count = sum(1 for r in results if r.get("status") == "error")
        success_rate = (len(results) - error_count) / len(results) if results else 0

        return success_rate >= 0.8  # 成功率至少80%

    async def _optimize_sql_based_on_result(
        self,
        sql: str,
        execution_result: Dict[str, Any],
        database_type: str
    ) -> Dict[str, Any]:
        """基于执行结果优化SQL"""
        try:
            # 构建优化提示
            prompt = f"""
{self.system_prompts["sql_optimization"]}

## 原始SQL：
```sql
{sql}
```

## 执行结果：
{json.dumps(execution_result, ensure_ascii=False, indent=2)}

## 数据库类型：
{database_type}

请分析执行结果中的问题，并提供优化后的SQL脚本。
"""

            # 调用LLM进行优化
            result = await llm_service.generate_text(
                self.model_config,
                prompt,
                temperature=0.2  # 使用较低温度确保优化的准确性
            )

            if not result["success"]:
                raise LLMError(f"SQL优化失败: {result['error']}")

            # 解析优化结果
            try:
                optimization_result = json.loads(result["content"])
                optimized_sql = optimization_result.get("optimized_sql", sql)
            except json.JSONDecodeError:
                # 如果不是JSON格式，尝试提取SQL代码块
                content = result["content"]
                if "```sql" in content:
                    start = content.find("```sql") + 6
                    end = content.find("```", start)
                    optimized_sql = content[start:end].strip() if end != -1 else sql
                else:
                    optimized_sql = sql

                optimization_result = {
                    "analysis": {"optimization_suggestions": ["基于执行结果进行了优化"]},
                    "optimized_sql": optimized_sql,
                    "explanation": "自动优化"
                }

            # 记录优化过程到上下文记忆
            self.context_memory.append({
                "type": "sql_optimization",
                "input": {
                    "original_sql": sql,
                    "execution_result": execution_result,
                    "database_type": database_type
                },
                "output": optimization_result,
                "tokens_used": result.get("tokens_used", 0),
                "response_time": result.get("response_time", 0)
            })

            return {
                "success": True,
                "optimized_sql": optimized_sql,
                "analysis": optimization_result.get("analysis", {}),
                "explanation": optimization_result.get("explanation", ""),
                "tokens_used": result.get("tokens_used", 0)
            }

        except Exception as e:
            logger.error(f"SQL优化失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "optimized_sql": sql
            }

    def get_sql_execution_history(self) -> List[Dict[str, Any]]:
        """获取SQL执行历史"""
        return self.sql_execution_history

    def clear_context_memory(self):
        """清空上下文记忆"""
        self.context_memory = []
        self.sql_execution_history = []

    async def analyze_requirement_stream(
        self,
        requirement_text: str,
        background_knowledge: Optional[str] = None,
        design_document_url: Optional[str] = None,
        database_type: str = "tdsql-pg"
    ):
        """流式分析需求"""
        try:
            # 构建提示
            prompt = f"""
{self.system_prompts["requirement_analysis"]}

## 需求描述：
{requirement_text}

## 背景知识：
{background_knowledge or "无"}

## 设计文档：
{design_document_url or "无"}

## 目标数据库类型：
{database_type}

请分析以上需求，生成详细的测试场景、测试点和建议。
"""

            # 构建消息格式
            messages = [{"role": "user", "content": prompt}]

            # 创建LLM提供商实例
            provider = llm_service.create_provider(self.model_config)

            analysis_content = ""
            reasoning_content = ""

            # 流式调用LLM
            async for chunk in provider.generate_stream(
                messages,
                temperature=0.3,
                enable_stream=True
            ):
                if chunk.get("type") == "content":
                    content = chunk.get("content", "")
                    analysis_content += content
                    # 发送内容块
                    yield {
                        "type": "llm_output",
                        "content": content
                    }
                elif chunk.get("type") == "reasoning":
                    # 累积思考过程内容
                    reasoning_chunk = chunk.get("content", "")
                    reasoning_content += reasoning_chunk
                    # 发送思考过程块（不加标记）
                    yield {
                        "type": "llm_reasoning",
                        "content": reasoning_chunk
                    }
                elif chunk.get("type") == "error":
                    yield {
                        "type": "error",
                        "message": chunk.get("error", "分析失败")
                    }
                    return

            # 解析分析结果
            try:
                # 尝试从JSON格式解析
                if "```json" in analysis_content:
                    start = analysis_content.find("```json") + 7
                    end = analysis_content.find("```", start)
                    json_content = analysis_content[start:end].strip()
                    analysis_result = json.loads(json_content)
                else:
                    # 如果不是JSON格式，尝试直接解析
                    analysis_result = json.loads(analysis_content)
            except json.JSONDecodeError:
                # 如果解析失败，创建一个基本的分析结构
                analysis_result = {
                    "test_scenarios": [
                        {
                            "scenario": "基于AI分析的测试场景",
                            "description": "AI分析的需求内容",
                            "priority": "high"
                        }
                    ],
                    "test_points": [
                        "基于AI分析的测试点",
                        "功能性测试",
                        "性能测试",
                        "安全性测试"
                    ],
                    "recommendations": [
                        "建议查看AI生成的完整分析内容",
                        "根据具体需求调整测试策略"
                    ]
                }

            # 记录到上下文记忆
            self.context_memory.append({
                "type": "requirement_analysis",
                "input": {
                    "requirement_text": requirement_text,
                    "background_knowledge": background_knowledge,
                    "database_type": database_type
                },
                "output": {
                    "analysis_result": analysis_result
                }
            })

            # 发送完成信号
            yield {
                "type": "complete",
                "data": {
                    "success": True,
                    "analysis_result": analysis_result
                }
            }

        except Exception as e:
            logger.error(f"需求分析流式生成失败: {str(e)}")
            yield {
                "type": "error",
                "message": str(e)
            }
