"""
记忆管理服务 - 处理对话历史、短期记忆和长期记忆
"""
import json
import time
import hashlib
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import redis
from sentence_transformers import SentenceTransformer
from pymilvus import Collection, connections, FieldSchema, CollectionSchema, DataType, utility
from ..config.database import get_redis, get_milvus_connection
from ..config.settings import settings
from ..core.exceptions import DatabaseError
from ..utils.logger import logger


class MemoryService:
    """记忆管理服务"""
    
    def __init__(self):
        self.redis_client = None
        self.embedding_model = None
        self.milvus_collection = None
        self._initialize_services()
    
    def _initialize_services(self):
        """初始化服务"""
        try:
            # 初始化Redis
            self.redis_client = get_redis()
            
            # 初始化嵌入模型
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            
            # 初始化Milvus
            self._initialize_milvus()
            
        except Exception as e:
            logger.error(f"记忆服务初始化失败: {str(e)}")
    
    def _initialize_milvus(self):
        """初始化Milvus向量数据库"""
        try:
            get_milvus_connection()
            
            collection_name = "memory_vectors"
            
            # 检查集合是否存在
            if utility.has_collection(collection_name):
                self.milvus_collection = Collection(collection_name)
            else:
                # 创建集合
                fields = [
                    FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                    FieldSchema(name="memory_id", dtype=DataType.VARCHAR, max_length=255),
                    FieldSchema(name="user_id", dtype=DataType.INT64),
                    FieldSchema(name="session_id", dtype=DataType.VARCHAR, max_length=255),
                    FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=10000),
                    FieldSchema(name="memory_type", dtype=DataType.VARCHAR, max_length=50),
                    FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=384),
                    FieldSchema(name="timestamp", dtype=DataType.INT64),
                ]
                
                schema = CollectionSchema(fields, "Memory vectors collection")
                self.milvus_collection = Collection(collection_name, schema)
                
                # 创建索引
                index_params = {
                    "metric_type": "COSINE",
                    "index_type": "IVF_FLAT",
                    "params": {"nlist": 128}
                }
                self.milvus_collection.create_index("embedding", index_params)
            
            # 加载集合
            self.milvus_collection.load()
            
        except Exception as e:
            logger.error(f"Milvus初始化失败: {str(e)}")
            self.milvus_collection = None
    
    async def create_session(
        self, 
        user_id: int, 
        session_name: str, 
        description: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """创建新的记忆会话"""
        try:
            session_id = f"session_{user_id}_{int(time.time())}"
            
            session_data = {
                "session_id": session_id,
                "user_id": user_id,
                "session_name": session_name,
                "description": description or "",
                "metadata": metadata or {},
                "created_at": time.time(),
                "updated_at": time.time(),
                "message_count": 0,
                "is_active": True
            }
            
            # 存储到Redis
            session_key = f"session:{session_id}"
            self.redis_client.hset(session_key, mapping={
                k: json.dumps(v) if isinstance(v, (dict, list)) else str(v)
                for k, v in session_data.items()
            })
            
            # 设置过期时间（30天）
            self.redis_client.expire(session_key, 30 * 24 * 3600)
            
            # 添加到用户会话列表
            user_sessions_key = f"user_sessions:{user_id}"
            self.redis_client.zadd(user_sessions_key, {session_id: time.time()})
            
            logger.info(f"创建会话成功: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"创建会话失败: {str(e)}")
            raise DatabaseError(f"创建会话失败: {str(e)}")
    
    async def add_message(
        self,
        session_id: str,
        role: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """添加消息到会话"""
        try:
            message_id = f"msg_{session_id}_{int(time.time() * 1000)}"
            
            message_data = {
                "message_id": message_id,
                "session_id": session_id,
                "role": role,  # user, assistant, system
                "content": content,
                "metadata": metadata or {},
                "timestamp": time.time()
            }
            
            # 存储消息到Redis
            message_key = f"message:{message_id}"
            self.redis_client.hset(message_key, mapping={
                k: json.dumps(v) if isinstance(v, (dict, list)) else str(v)
                for k, v in message_data.items()
            })
            
            # 设置过期时间（30天）
            self.redis_client.expire(message_key, 30 * 24 * 3600)
            
            # 添加到会话消息列表
            session_messages_key = f"session_messages:{session_id}"
            self.redis_client.zadd(session_messages_key, {message_id: time.time()})
            
            # 更新会话信息
            session_key = f"session:{session_id}"
            self.redis_client.hset(session_key, mapping={
                "updated_at": str(time.time()),
                "message_count": str(self.redis_client.zcard(session_messages_key))
            })
            
            # 如果是重要消息，存储到长期记忆
            if self._is_important_message(content, role):
                await self._store_long_term_memory(session_id, message_data)
            
            return message_id
            
        except Exception as e:
            logger.error(f"添加消息失败: {str(e)}")
            raise DatabaseError(f"添加消息失败: {str(e)}")
    
    def _is_important_message(self, content: str, role: str) -> bool:
        """判断消息是否重要，需要存储到长期记忆"""
        # 简单的重要性判断逻辑
        important_keywords = [
            "需求分析", "测试用例", "SQL生成", "数据库", "性能", "优化",
            "错误", "问题", "解决方案", "建议", "最佳实践"
        ]
        
        # 用户消息或包含重要关键词的助手消息
        if role == "user" or (role == "assistant" and any(keyword in content for keyword in important_keywords)):
            return len(content) > 50  # 内容长度大于50字符
        
        return False
    
    async def _store_long_term_memory(self, session_id: str, message_data: Dict[str, Any]):
        """存储长期记忆到向量数据库"""
        try:
            if not self.milvus_collection:
                return
            
            content = message_data["content"]
            user_id = await self._get_user_id_from_session(session_id)
            
            # 生成嵌入向量
            embedding = self.embedding_model.encode([content])[0].tolist()
            
            # 准备数据
            memory_id = f"memory_{session_id}_{int(time.time() * 1000)}"
            data = [
                [memory_id],  # memory_id
                [user_id],    # user_id
                [session_id], # session_id
                [content],    # content
                [message_data["role"]], # memory_type
                [embedding],  # embedding
                [int(message_data["timestamp"])]  # timestamp
            ]
            
            # 插入到Milvus
            self.milvus_collection.insert(data)
            self.milvus_collection.flush()
            
            logger.info(f"长期记忆存储成功: {memory_id}")
            
        except Exception as e:
            logger.error(f"长期记忆存储失败: {str(e)}")
    
    async def _get_user_id_from_session(self, session_id: str) -> int:
        """从会话ID获取用户ID"""
        try:
            session_key = f"session:{session_id}"
            user_id = self.redis_client.hget(session_key, "user_id")
            return int(user_id) if user_id else 0
        except:
            return 0
    
    async def search_memory(
        self,
        user_id: int,
        query: str,
        session_id: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """搜索相关记忆"""
        try:
            if not self.milvus_collection:
                return []
            
            # 生成查询向量
            query_embedding = self.embedding_model.encode([query])[0].tolist()
            
            # 构建搜索表达式
            expr = f"user_id == {user_id}"
            if session_id:
                expr += f" and session_id == '{session_id}'"
            
            # 搜索相似记忆
            search_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
            results = self.milvus_collection.search(
                data=[query_embedding],
                anns_field="embedding",
                param=search_params,
                limit=limit,
                expr=expr,
                output_fields=["memory_id", "session_id", "content", "memory_type", "timestamp"]
            )
            
            # 格式化结果
            memories = []
            for hits in results:
                for hit in hits:
                    memories.append({
                        "memory_id": hit.entity.get("memory_id"),
                        "session_id": hit.entity.get("session_id"),
                        "content": hit.entity.get("content"),
                        "memory_type": hit.entity.get("memory_type"),
                        "timestamp": hit.entity.get("timestamp"),
                        "relevance_score": float(hit.score)
                    })
            
            return memories
            
        except Exception as e:
            logger.error(f"记忆搜索失败: {str(e)}")
            return []
    
    async def get_session_messages(
        self,
        session_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """获取会话消息"""
        try:
            session_messages_key = f"session_messages:{session_id}"
            
            # 获取消息ID列表（按时间倒序）
            message_ids = self.redis_client.zrevrange(
                session_messages_key, offset, offset + limit - 1
            )
            
            messages = []
            for message_id in message_ids:
                message_key = f"message:{message_id}"
                message_data = self.redis_client.hgetall(message_key)
                
                if message_data:
                    # 解析JSON字段
                    for key in ["metadata"]:
                        if key in message_data:
                            try:
                                message_data[key] = json.loads(message_data[key])
                            except:
                                pass
                    
                    messages.append(message_data)
            
            return messages
            
        except Exception as e:
            logger.error(f"获取会话消息失败: {str(e)}")
            return []
    
    async def get_context_for_llm(
        self,
        user_id: int,
        session_id: str,
        current_query: str,
        max_history: int = 10,
        max_memory: int = 5
    ) -> Dict[str, Any]:
        """为LLM获取上下文信息"""
        try:
            # 获取最近的对话历史
            recent_messages = await self.get_session_messages(session_id, limit=max_history)
            
            # 搜索相关的长期记忆
            relevant_memories = await self.search_memory(
                user_id=user_id,
                query=current_query,
                limit=max_memory
            )
            
            # 构建上下文
            context = {
                "recent_conversation": recent_messages,
                "relevant_memories": relevant_memories,
                "session_id": session_id,
                "user_id": user_id
            }
            
            return context
            
        except Exception as e:
            logger.error(f"获取LLM上下文失败: {str(e)}")
            return {
                "recent_conversation": [],
                "relevant_memories": [],
                "session_id": session_id,
                "user_id": user_id
            }


# 全局记忆服务实例
memory_service = MemoryService()
