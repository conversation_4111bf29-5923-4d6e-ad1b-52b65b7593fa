"""
TDPilot服务 - 管理TDPilot AI智能体的业务逻辑
"""
import asyncio
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from ..agents.tdpilot import TDPilotAgent
from ..models.llm_model import LLMModel
from ..models.test_case import RequirementAnalysis, TestCase, SQLGeneration
from ..services.memory_service import memory_service
from ..services.sql_generator import sql_generator
from ..core.exceptions import LLMError, ValidationError
from ..utils.logger import logger


class TDPilotService:
    """TDPilot服务管理器"""
    
    def __init__(self):
        self.active_agents = {}  # 存储活跃的智能体实例
    
    def _get_model_config(self, db: Session, model_id: int) -> Dict[str, Any]:
        """获取LLM模型配置"""
        model = db.query(LLMModel).filter(LLMModel.id == model_id).first()
        if not model:
            raise ValidationError(f"LLM模型不存在: {model_id}")
        
        if not model.is_active:
            raise ValidationError(f"LLM模型未激活: {model.name}")
        
        return {
            "provider": model.provider,
            "model_name": model.model_name,
            "base_url": model.base_url,
            "api_key": model.api_key,
            "max_tokens": model.max_tokens,
            "temperature": model.temperature
        }
    
    def _get_or_create_agent(
        self,
        user_id: int,
        model_config: Dict[str, Any],
        session_id: Optional[str] = None
    ) -> TDPilotAgent:
        """获取或创建TDPilot智能体实例"""
        agent_key = f"{user_id}_{model_config['provider']}_{model_config['model_name']}"

        if agent_key not in self.active_agents:
            self.active_agents[agent_key] = TDPilotAgent(model_config, user_id, session_id)
        else:
            # 更新会话ID
            self.active_agents[agent_key].session_id = session_id

        return self.active_agents[agent_key]
    
    async def create_session(
        self,
        user_id: int,
        session_name: str,
        description: Optional[str] = None
    ) -> str:
        """创建新的对话会话"""
        return await memory_service.create_session(
            user_id=user_id,
            session_name=session_name,
            description=description
        )

    async def analyze_requirement(
        self,
        db: Session,
        user_id: int,
        title: str,
        requirement_text: str,
        background_knowledge: Optional[str] = None,
        design_document_url: Optional[str] = None,
        database_type: str = "tdsql-pg",
        llm_model_id: int = 1,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """分析用户需求"""
        try:
            # 获取模型配置
            model_config = self._get_model_config(db, llm_model_id)
            
            # 获取智能体实例
            agent = self._get_or_create_agent(user_id, model_config, session_id)
            
            # 创建需求分析记录
            analysis = RequirementAnalysis(
                user_id=user_id,
                title=title,
                requirement_text=requirement_text,
                background_knowledge=background_knowledge,
                design_document_url=design_document_url,
                database_type=database_type,
                llm_model_id=llm_model_id,
                status="analyzing"
            )
            db.add(analysis)
            db.commit()
            db.refresh(analysis)
            
            # 调用智能体分析需求
            result = await agent.analyze_requirement(
                requirement_text=requirement_text,
                background_knowledge=background_knowledge,
                design_document_url=design_document_url,
                database_type=database_type
            )
            
            if result["success"]:
                # 更新分析结果
                analysis.status = "completed"
                analysis.test_scenarios = result["analysis_result"].get("test_scenarios", [])
                analysis.test_points = result["analysis_result"].get("test_points", [])
                analysis.recommendations = result["analysis_result"].get("recommendations", [])
                analysis.analysis_result = str(result["analysis_result"])
                
                # 记录使用统计
                from ..models.llm_model import LLMModelUsage
                usage = LLMModelUsage(
                    model_id=llm_model_id,
                    user_id=user_id,
                    usage_type="requirement_analysis",
                    tokens_used=result.get("tokens_used", 0),
                    response_time=result.get("response_time", 0),
                    success=True
                )
                db.add(usage)
            else:
                # 更新失败状态
                analysis.status = "failed"
                analysis.analysis_result = result.get("error", "分析失败")
                
                # 记录失败统计
                from ..models.llm_model import LLMModelUsage
                usage = LLMModelUsage(
                    model_id=llm_model_id,
                    user_id=user_id,
                    usage_type="requirement_analysis",
                    success=False,
                    error_message=result.get("error", "分析失败")
                )
                db.add(usage)
            
            db.commit()
            
            return {
                "analysis_id": analysis.id,
                "status": analysis.status,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"需求分析失败-1: {str(e)}")
            db.rollback()
            raise LLMError(f"需求分析失败-2: {str(e)}")
    
    async def generate_test_cases(
        self,
        db: Session,
        user_id: int,
        analysis_id: int,
        title: str,
        llm_model_id: int,
        custom_requirements: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成测试用例"""
        try:
            # 获取需求分析结果
            analysis = db.query(RequirementAnalysis).filter(
                RequirementAnalysis.id == analysis_id,
                RequirementAnalysis.user_id == user_id
            ).first()
            
            if not analysis:
                raise ValidationError("需求分析不存在")
            
            if analysis.status != "completed":
                raise ValidationError("需求分析未完成")
            
            # 获取模型配置
            model_config = self._get_model_config(db, llm_model_id)
            
            # 获取智能体实例
            agent = self._get_or_create_agent(user_id, model_config)
            
            # 创建测试用例记录
            test_case = TestCase(
                analysis_id=analysis_id,
                user_id=user_id,
                title=title,
                llm_model_id=llm_model_id,
                custom_requirements=custom_requirements,
                status="generating"
            )
            db.add(test_case)
            db.commit()
            db.refresh(test_case)
            
            # 准备分析结果 - 重构数据结构以确保正确的层级关系
            test_scenarios = analysis.test_scenarios or []
            test_points = analysis.test_points or []

            # 计算预期的测试用例数量
            total_test_points = 0

            # 检查场景中是否已经有测试点
            scenarios_have_test_points = any(scenario.get("test_points") for scenario in test_scenarios)

            if scenarios_have_test_points:
                # 如果场景中已经有测试点，直接统计
                for scenario in test_scenarios:
                    scenario_test_points = scenario.get("test_points", [])
                    total_test_points += len(scenario_test_points)
            elif test_points:
                # 如果test_points是独立的数组，需要将其分配到场景中
                points_per_scenario = len(test_points) // len(test_scenarios) if test_scenarios else 0
                remaining_points = len(test_points) % len(test_scenarios) if test_scenarios else 0

                point_index = 0
                for i, scenario in enumerate(test_scenarios):
                    # 每个场景分配基础数量的测试点
                    scenario_points = test_points[point_index:point_index + points_per_scenario]
                    point_index += points_per_scenario

                    # 前几个场景多分配一个测试点（处理余数）
                    if i < remaining_points:
                        if point_index < len(test_points):
                            scenario_points.append(test_points[point_index])
                            point_index += 1

                    scenario["test_points"] = scenario_points

                total_test_points = len(test_points)  # 总测试点数就是原始数组的长度

            analysis_result = {
                "test_scenarios": test_scenarios,
                "test_points": test_points,  # 保留原始测试点数组作为参考
                "recommendations": analysis.recommendations or [],
                "expected_test_cases": total_test_points * 2,  # 每个测试点至少生成2个用例
                "total_test_points": total_test_points
            }

            logger.info(f"准备测试用例生成数据: {len(test_scenarios)}个场景, {total_test_points}个测试点, 预期生成{total_test_points * 2}个测试用例")
            
            # 调用智能体生成测试用例
            logger.info(f"开始调用智能体生成测试用例，用户ID: {user_id}, 分析ID: {analysis_id}")
            result = await agent.generate_test_cases(
                analysis_result=analysis_result,
                custom_requirements=custom_requirements
            )

            # 详细记录生成结果
            logger.info(f"智能体调用完成，成功状态: {result.get('success')}")
            if result.get("debug_info"):
                debug_info = result["debug_info"]
                logger.info(f"调试信息 - 提示词长度: {debug_info.get('prompt_length', 0)}")
                logger.info(f"调试信息 - LLM原始输出长度: {len(debug_info.get('llm_raw_output', ''))}")
                if debug_info.get("json_parse_error"):
                    logger.error(f"JSON解析错误: {debug_info['json_parse_error']}")
                if debug_info.get("llm_reasoning"):
                    logger.debug(f"LLM推理过程: {debug_info['llm_reasoning'][:200]}...")

            if result["success"]:
                test_cases_data = result["test_cases"]
                test_scenarios = test_cases_data.get("test_scenarios", [])
                test_coverage = test_cases_data.get("test_coverage", {})

                logger.info(f"成功生成 {len(test_scenarios)} 个测试场景")

                # 检查是否有原始内容（表示JSON解析失败但有fallback）
                if "raw_content" in test_cases_data:
                    logger.warning("测试用例生成包含原始内容，可能存在JSON解析问题")
                    logger.debug(f"原始内容: {test_cases_data['raw_content'][:500]}...")

                # 更新测试用例结果
                test_case.status = "completed"
                test_case.test_scenarios = test_scenarios
                test_case.visualization_data = test_coverage

                # 如果有调试信息，也保存到数据库
                if result.get("debug_info"):
                    test_case.debug_info = result["debug_info"]

                # 记录使用统计
                from ..models.llm_model import LLMModelUsage
                usage = LLMModelUsage(
                    model_id=llm_model_id,
                    user_id=user_id,
                    usage_type="test_generation",
                    tokens_used=result.get("tokens_used", 0),
                    response_time=result.get("response_time", 0),
                    success=True
                )
                db.add(usage)

                logger.info(f"测试用例生成成功，ID: {test_case.id}")
            else:
                error_msg = result.get("error", "生成失败")
                logger.error(f"测试用例生成失败: {error_msg}")

                # 更新失败状态
                test_case.status = "failed"
                test_case.error_message = error_msg

                # 保存调试信息
                if result.get("debug_info"):
                    test_case.debug_info = result["debug_info"]

                # 记录失败统计
                from ..models.llm_model import LLMModelUsage
                usage = LLMModelUsage(
                    model_id=llm_model_id,
                    user_id=user_id,
                    usage_type="test_generation",
                    success=False,
                    error_message=error_msg
                )
                db.add(usage)

            db.commit()

            return {
                "test_case_id": test_case.id,
                "status": test_case.status,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"测试用例生成失败: {str(e)}")
            db.rollback()
            raise LLMError(f"测试用例生成失败: {str(e)}")
    
    async def generate_sql_scripts(
        self,
        db: Session,
        user_id: int,
        test_case_id: int,
        title: str,
        database_type: str,
        llm_model_id: int,
        custom_requirements: Optional[str] = None,
        use_ai_generation: bool = True
    ) -> Dict[str, Any]:
        """生成SQL脚本 - 支持AI生成和模板生成两种模式"""
        try:
            # 获取测试用例
            test_case = db.query(TestCase).filter(
                TestCase.id == test_case_id,
                TestCase.user_id == user_id
            ).first()

            if not test_case:
                raise ValidationError("测试用例不存在")

            if test_case.status != "completed":
                raise ValidationError("测试用例未完成")

            # 创建SQL生成记录
            sql_generation = SQLGeneration(
                test_case_id=test_case_id,
                user_id=user_id,
                title=title,
                database_type=database_type,
                llm_model_id=llm_model_id,
                custom_requirements=custom_requirements,
                use_ai_generation=use_ai_generation,
                generation_method="ai" if use_ai_generation else "template",
                status="generating"
            )
            db.add(sql_generation)
            db.commit()
            db.refresh(sql_generation)

            # 准备测试用例数据
            test_cases_data = {
                "test_scenarios": test_case.test_scenarios or []
            }

            start_time = time.time()

            if use_ai_generation:
                # 使用AI生成SQL脚本
                result = await self._generate_sql_with_ai(
                    db, user_id, llm_model_id, test_cases_data,
                    database_type, custom_requirements
                )
            else:
                # 使用模板生成SQL脚本
                result = self._generate_sql_with_template(
                    test_cases_data, database_type, custom_requirements
                )

            generation_time = time.time() - start_time

            if result["success"]:
                # 更新SQL生成结果
                sql_generation.status = "completed"
                sql_generation.sql_content = result["sql_content"]
                sql_generation.test_tables = result.get("test_tables", [])
                sql_generation.scenario_count = result.get("scenario_count", 0)
                sql_generation.generation_time = generation_time
                sql_generation.tokens_used = result.get("tokens_used", 0)
                sql_generation.generated_at = datetime.now()

                # 记录使用统计
                from ..models.llm_model import LLMModelUsage
                usage = LLMModelUsage(
                    model_id=llm_model_id,
                    user_id=user_id,
                    usage_type="sql_generation",
                    tokens_used=result.get("tokens_used", 0),
                    response_time=generation_time,
                    success=True
                )
                db.add(usage)

                logger.info(f"SQL脚本生成成功，ID: {sql_generation.id}, 耗时: {generation_time:.2f}秒, 场景数: {result.get('scenario_count', 0)}")
            else:
                # 更新失败状态
                sql_generation.status = "failed"
                sql_generation.error_message = result.get("error", "生成失败")
                sql_generation.generation_time = generation_time

                # 记录失败统计
                from ..models.llm_model import LLMModelUsage
                usage = LLMModelUsage(
                    model_id=llm_model_id,
                    user_id=user_id,
                    usage_type="sql_generation",
                    success=False,
                    error_message=result.get("error", "生成失败")
                )
                db.add(usage)

                logger.error(f"SQL脚本生成失败: {result.get('error')}")

            db.commit()

            return {
                "sql_generation_id": sql_generation.id,
                "status": sql_generation.status,
                "result": result
            }

        except Exception as e:
            logger.error(f"SQL脚本生成失败: {str(e)}")
            db.rollback()
            raise LLMError(f"SQL脚本生成失败: {str(e)}")

    async def _generate_sql_with_ai(
        self,
        db: Session,
        user_id: int,
        llm_model_id: int,
        test_cases_data: Dict[str, Any],
        database_type: str,
        custom_requirements: Optional[str] = None
    ) -> Dict[str, Any]:
        """使用AI生成SQL脚本"""
        try:
            # 获取模型配置
            model_config = self._get_model_config(db, llm_model_id)

            # 获取智能体实例
            agent = self._get_or_create_agent(user_id, model_config)

            # 调用智能体生成SQL脚本
            result = await agent.generate_sql_scripts(
                test_cases=test_cases_data,
                database_type=database_type
            )

            return result

        except Exception as e:
            logger.error(f"TDPilot SQL生成失败: {str(e)}")
            return {
                "success": False,
                "error": f"TDPilot SQL生成失败: {str(e)}"
            }

    async def _generate_sql_with_ai_stream(
        self,
        db: Session,
        user_id: int,
        llm_model_id: int,
        test_cases_data: Dict[str, Any],
        database_type: str,
        custom_requirements: Optional[str] = None
    ):
        """使用AI流式生成SQL脚本"""
        try:
            # 获取模型配置
            model_config = self._get_model_config(db, llm_model_id)

            # 获取智能体实例
            agent = self._get_or_create_agent(user_id, model_config)

            # 调用智能体流式生成SQL脚本
            async for chunk in agent.generate_sql_scripts_stream(
                test_cases=test_cases_data,
                database_type=database_type
            ):
                yield chunk

        except Exception as e:
            logger.error(f"TDPilot SQL流式生成失败: {str(e)}")
            yield {
                "type": "error",
                "message": f"TDPilot生成失败: {str(e)}"
            }

    async def _generate_test_cases_with_ai_stream(
        self,
        db: Session,
        user_id: int,
        llm_model_id: int,
        analysis_result: Dict[str, Any],
        custom_requirements: Optional[str] = None
    ):
        """使用AI流式生成测试用例"""
        try:
            # 获取模型配置
            model_config = self._get_model_config(db, llm_model_id)

            # 获取智能体实例
            agent = self._get_or_create_agent(user_id, model_config)

            # 调用智能体流式生成测试用例
            async for chunk in agent.generate_test_cases_stream(
                analysis_result=analysis_result,
                custom_requirements=custom_requirements
            ):
                yield chunk

        except Exception as e:
            logger.error(f"AI测试用例流式生成失败: {str(e)}")
            yield {
                "type": "error",
                "message": f"AI生成失败: {str(e)}"
            }

    async def _analyze_requirement_with_ai_stream(
        self,
        db: Session,
        user_id: int,
        llm_model_id: int,
        requirement_text: str,
        background_knowledge: Optional[str] = None,
        design_document_url: Optional[str] = None,
        database_type: str = "tdsql-pg"
    ):
        """使用AI流式分析需求"""
        try:
            # 获取模型配置
            model_config = self._get_model_config(db, llm_model_id)

            # 获取智能体实例
            agent = self._get_or_create_agent(user_id, model_config)

            # 调用智能体流式分析需求
            async for chunk in agent.analyze_requirement_stream(
                requirement_text=requirement_text,
                background_knowledge=background_knowledge,
                design_document_url=design_document_url,
                database_type=database_type
            ):
                yield chunk

        except Exception as e:
            logger.error(f"AI需求分析流式生成失败: {str(e)}")
            yield {
                "type": "error",
                "message": f"AI分析失败: {str(e)}"
            }

    def _generate_sql_with_template(
        self,
        test_cases_data: Dict[str, Any],
        database_type: str,
        custom_requirements: Optional[str] = None
    ) -> Dict[str, Any]:
        """使用模板生成SQL脚本"""
        try:
            # 使用新的SQL生成器
            result = sql_generator.generate_comprehensive_sql(
                test_cases=test_cases_data,
                database_type=database_type,
                custom_requirements=custom_requirements
            )

            return result

        except Exception as e:
            logger.error(f"模板SQL生成失败: {str(e)}")
            return {
                "success": False,
                "error": f"模板生成失败: {str(e)}"
            }
    
    def get_agent_memory(self, user_id: int, model_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取智能体的上下文记忆"""
        agent = self._get_or_create_agent(user_id, model_config)
        return agent.get_context_memory()
    
    def clear_agent_memory(self, user_id: int, model_config: Dict[str, Any]):
        """清空智能体的上下文记忆"""
        agent = self._get_or_create_agent(user_id, model_config)
        agent.clear_context_memory()


# 全局TDPilot服务实例
tdpilot_service = TDPilotService()
