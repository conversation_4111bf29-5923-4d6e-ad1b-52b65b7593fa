"""
SQL脚本生成服务 - 专门负责SQL脚本的智能生成
"""
import json
import re
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.orm import Session
from ..core.exceptions import ValidationError, LLMError
from ..utils.logger import logger


class SQLTemplateEngine:
    """SQL模板引擎"""
    
    def __init__(self):
        self.templates = {
            "tdsql-pg": self._get_postgresql_templates(),
            "tdsql-oracle": self._get_oracle_templates(),
            "tdsql2": self._get_mysql_templates(),
            "tdsql3": self._get_mysql_templates(),
            "cynosdb": self._get_mysql_templates()
        }
    
    def _get_postgresql_templates(self) -> Dict[str, str]:
        """PostgreSQL模板"""
        return {
            "table_creation": """
-- 创建测试表: {table_name}
CREATE TABLE {table_name} (
    id SERIAL PRIMARY KEY,
    {columns}
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
{indexes}
""",
            "batch_insert": """
-- 批量插入测试数据
INSERT INTO {table_name} ({column_list}) VALUES
{value_rows};
""",
            "crud_operations": """
-- CRUD操作测试
-- 查询操作 (必须包含ORDER BY)
SELECT * FROM {table_name} ORDER BY id LIMIT 10;

-- 更新操作
UPDATE {table_name} SET {update_fields} WHERE {condition};

-- 删除操作
DELETE FROM {table_name} WHERE {condition};
""",
            "performance_test": """
-- 性能测试SQL
-- 大数据量查询
SELECT COUNT(*) FROM {table_name};

-- 复杂连接查询
SELECT t1.*, t2.* FROM {table_name} t1 
JOIN {related_table} t2 ON t1.id = t2.{foreign_key}
ORDER BY t1.id LIMIT 100;

-- 聚合查询
SELECT {group_column}, COUNT(*), AVG({numeric_column})
FROM {table_name} 
GROUP BY {group_column}
ORDER BY COUNT(*) DESC;
"""
        }
    
    def _get_oracle_templates(self) -> Dict[str, str]:
        """Oracle模板"""
        return {
            "table_creation": """
-- 创建测试表: {table_name}
CREATE TABLE {table_name} (
    id NUMBER GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    {columns}
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
{indexes}
""",
            "batch_insert": """
-- 批量插入测试数据
INSERT ALL
{insert_statements}
SELECT * FROM dual;
""",
            "crud_operations": """
-- CRUD操作测试
-- 查询操作 (使用ROWNUM限制)
SELECT * FROM (
    SELECT * FROM {table_name} ORDER BY id
) WHERE ROWNUM <= 10;

-- 更新操作
UPDATE {table_name} SET {update_fields} WHERE {condition};

-- 删除操作
DELETE FROM {table_name} WHERE {condition};
"""
        }
    
    def _get_mysql_templates(self) -> Dict[str, str]:
        """MySQL模板"""
        return {
            "table_creation": """
-- 创建测试表: {table_name}
CREATE TABLE {table_name} (
    id INT AUTO_INCREMENT PRIMARY KEY,
    {columns}
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建索引
{indexes}
""",
            "batch_insert": """
-- 批量插入测试数据
INSERT INTO {table_name} ({column_list}) VALUES
{value_rows};
""",
            "crud_operations": """
-- CRUD操作测试
-- 查询操作
SELECT * FROM {table_name} ORDER BY id LIMIT 10;

-- 更新操作
UPDATE {table_name} SET {update_fields} WHERE {condition};

-- 删除操作
DELETE FROM {table_name} WHERE {condition};
"""
        }
    
    def generate_sql_from_template(
        self,
        database_type: str,
        template_type: str,
        parameters: Dict[str, Any]
    ) -> str:
        """根据模板生成SQL"""
        if database_type not in self.templates:
            raise ValidationError(f"不支持的数据库类型: {database_type}")
        
        if template_type not in self.templates[database_type]:
            raise ValidationError(f"不支持的模板类型: {template_type}")
        
        template = self.templates[database_type][template_type]
        return template.format(**parameters)


class DatabaseAdapter:
    """数据库适配器"""
    
    @staticmethod
    def get_data_type_mapping(database_type: str) -> Dict[str, str]:
        """获取数据类型映射"""
        mappings = {
            "tdsql-pg": {
                "integer": "INTEGER",
                "string": "VARCHAR(255)",
                "text": "TEXT",
                "decimal": "DECIMAL(10,2)",
                "boolean": "BOOLEAN",
                "datetime": "TIMESTAMP",
                "date": "DATE"
            },
            "tdsql-oracle": {
                "integer": "NUMBER",
                "string": "VARCHAR2(255)",
                "text": "CLOB",
                "decimal": "NUMBER(10,2)",
                "boolean": "NUMBER(1)",
                "datetime": "TIMESTAMP",
                "date": "DATE"
            },
            "tdsql2": {
                "integer": "INT",
                "string": "VARCHAR(255)",
                "text": "TEXT",
                "decimal": "DECIMAL(10,2)",
                "boolean": "BOOLEAN",
                "datetime": "DATETIME",
                "date": "DATE"
            },
            "tdsql3": {
                "integer": "INT",
                "string": "VARCHAR(255)",
                "text": "TEXT",
                "decimal": "DECIMAL(10,2)",
                "boolean": "BOOLEAN",
                "datetime": "DATETIME",
                "date": "DATE"
            },
            "cynosdb": {
                "integer": "INT",
                "string": "VARCHAR(255)",
                "text": "TEXT",
                "decimal": "DECIMAL(10,2)",
                "boolean": "BOOLEAN",
                "datetime": "DATETIME",
                "date": "DATE"
            }
        }
        return mappings.get(database_type, mappings["tdsql-pg"])
    
    @staticmethod
    def adapt_sql_syntax(sql: str, database_type: str) -> str:
        """适配SQL语法"""
        if database_type == "tdsql-oracle":
            # Oracle特殊处理
            sql = re.sub(r'LIMIT\s+(\d+)', r'AND ROWNUM <= \1', sql)
            sql = re.sub(r'AUTO_INCREMENT', 'GENERATED BY DEFAULT AS IDENTITY', sql)
        elif database_type in ["tdsql2", "tdsql3", "cynosdb"]:
            # MySQL特殊处理
            sql = re.sub(r'SERIAL', 'INT AUTO_INCREMENT', sql)
            sql = re.sub(r'CURRENT_TIMESTAMP', 'NOW()', sql)
        
        return sql


class SQLGenerator:
    """SQL脚本生成器"""
    
    def __init__(self):
        self.template_engine = SQLTemplateEngine()
        self.database_adapter = DatabaseAdapter()
    
    def generate_comprehensive_sql(
        self,
        test_cases: Dict[str, Any],
        database_type: str,
        custom_requirements: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成综合SQL脚本"""
        try:
            logger.info(f"开始生成SQL脚本，数据库类型: {database_type}")
            
            # 解析测试用例
            test_scenarios = test_cases.get("test_scenarios", [])
            if not test_scenarios:
                raise ValidationError("测试用例为空")
            
            # 生成SQL脚本
            sql_parts = []
            test_tables = []
            
            # 添加脚本头部注释
            header = self._generate_script_header(database_type, test_scenarios)
            sql_parts.append(header)
            
            # 为每个测试场景生成SQL
            for i, scenario in enumerate(test_scenarios):
                scenario_sql, tables = self._generate_scenario_sql(
                    scenario, database_type, i + 1
                )
                sql_parts.append(scenario_sql)
                test_tables.extend(tables)
            
            # 生成清理脚本
            cleanup_sql = self._generate_cleanup_sql(test_tables)
            sql_parts.append(cleanup_sql)
            
            # 合并所有SQL
            complete_sql = "\n\n".join(sql_parts)
            
            # 适配数据库语法
            complete_sql = self.database_adapter.adapt_sql_syntax(
                complete_sql, database_type
            )
            
            return {
                "success": True,
                "sql_content": complete_sql,
                "test_tables": list(set(test_tables)),
                "scenario_count": len(test_scenarios),
                "generated_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"SQL脚本生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    def _generate_script_header(self, database_type: str, test_scenarios: List[Dict]) -> str:
        """生成脚本头部注释"""
        scenario_names = [scenario.get("scenario", f"场景{i+1}") for i, scenario in enumerate(test_scenarios)]

        return f"""/*
=============================================================================
TDSQL AI测试平台 - 自动生成的SQL测试脚本
=============================================================================
数据库类型: {database_type}
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试场景数量: {len(test_scenarios)}
测试场景列表:
{chr(10).join([f"  - {name}" for name in scenario_names])}

注意事项:
1. 请在测试环境中执行此脚本
2. 执行前请备份重要数据
3. 分布式数据库查询中，有LIMIT的必须包含ORDER BY
4. 建议分段执行，观察执行结果
=============================================================================
*/"""

    def _generate_scenario_sql(
        self,
        scenario: Dict[str, Any],
        database_type: str,
        scenario_index: int
    ) -> Tuple[str, List[str]]:
        """为单个测试场景生成SQL"""
        scenario_name = scenario.get("scenario", f"测试场景{scenario_index}")
        test_cases = scenario.get("test_cases", [])

        sql_parts = [f"""
-- =============================================================================
-- 测试场景 {scenario_index}: {scenario_name}
-- ============================================================================="""]

        tables_created = []

        # 为每个测试用例生成SQL
        for i, test_case in enumerate(test_cases):
            case_sql, case_tables = self._generate_test_case_sql(
                test_case, database_type, scenario_index, i + 1
            )
            sql_parts.append(case_sql)
            tables_created.extend(case_tables)

        return "\n\n".join(sql_parts), tables_created

    def _generate_test_case_sql(
        self,
        test_case: Dict[str, Any],
        database_type: str,
        scenario_index: int,
        case_index: int
    ) -> Tuple[str, List[str]]:
        """为单个测试用例生成SQL"""
        case_name = test_case.get("name", f"测试用例{case_index}")
        case_id = test_case.get("case_id", f"TC{scenario_index:02d}{case_index:02d}")
        description = test_case.get("description", "")
        category = test_case.get("category", "功能")

        # 根据测试用例类型生成不同的SQL
        table_name = f"test_{scenario_index}_{case_index}_{category.lower()}"
        tables_created = [table_name]

        sql_parts = [f"""
-- -----------------------------------------------------------------------------
-- {case_id}: {case_name}
-- 描述: {description}
-- 类别: {category}
-- -----------------------------------------------------------------------------"""]

        # 生成表结构
        table_sql = self._generate_table_structure(table_name, database_type, category)
        sql_parts.append(table_sql)

        # 生成测试数据
        data_sql = self._generate_test_data(table_name, database_type, category)
        sql_parts.append(data_sql)

        # 生成测试SQL
        test_sql = self._generate_test_operations(table_name, database_type, category)
        sql_parts.append(test_sql)

        return "\n".join(sql_parts), tables_created

    def _generate_table_structure(self, table_name: str, database_type: str, category: str) -> str:
        """生成表结构"""
        data_types = self.database_adapter.get_data_type_mapping(database_type)

        # 根据测试类别定义不同的表结构
        if category in ["功能", "边界"]:
            columns = f"""
    name {data_types['string']} NOT NULL,
    email {data_types['string']} UNIQUE,
    age {data_types['integer']},
    salary {data_types['decimal']},
    is_active {data_types['boolean']} DEFAULT TRUE,
    description {data_types['text']},"""

            indexes = f"""
CREATE INDEX idx_{table_name}_name ON {table_name}(name);
CREATE INDEX idx_{table_name}_email ON {table_name}(email);"""

        elif category == "性能":
            columns = f"""
    user_id {data_types['integer']} NOT NULL,
    product_id {data_types['integer']} NOT NULL,
    quantity {data_types['integer']} DEFAULT 1,
    price {data_types['decimal']} NOT NULL,
    order_date {data_types['datetime']},
    status {data_types['string']} DEFAULT 'pending',"""

            indexes = f"""
CREATE INDEX idx_{table_name}_user_id ON {table_name}(user_id);
CREATE INDEX idx_{table_name}_product_id ON {table_name}(product_id);
CREATE INDEX idx_{table_name}_order_date ON {table_name}(order_date);
CREATE INDEX idx_{table_name}_status ON {table_name}(status);"""

        elif category == "安全":
            columns = f"""
    username {data_types['string']} NOT NULL UNIQUE,
    password_hash {data_types['string']} NOT NULL,
    role {data_types['string']} DEFAULT 'user',
    last_login {data_types['datetime']},
    failed_attempts {data_types['integer']} DEFAULT 0,
    is_locked {data_types['boolean']} DEFAULT FALSE,"""

            indexes = f"""
CREATE UNIQUE INDEX idx_{table_name}_username ON {table_name}(username);
CREATE INDEX idx_{table_name}_role ON {table_name}(role);"""

        else:  # 异常测试
            columns = f"""
    test_field {data_types['string']},
    nullable_field {data_types['string']},
    numeric_field {data_types['integer']},
    date_field {data_types['date']},"""

            indexes = f"-- 异常测试表不创建索引"

        return self.template_engine.generate_sql_from_template(
            database_type,
            "table_creation",
            {
                "table_name": table_name,
                "columns": columns,
                "indexes": indexes
            }
        )

    def _generate_test_data(self, table_name: str, database_type: str, category: str) -> str:
        """生成测试数据"""
        if category in ["功能", "边界"]:
            column_list = "name, email, age, salary, is_active, description"
            value_rows = """
    ('张三', '<EMAIL>', 25, 5000.00, TRUE, '测试用户1'),
    ('李四', '<EMAIL>', 30, 6000.00, TRUE, '测试用户2'),
    ('王五', '<EMAIL>', 35, 7000.00, FALSE, '测试用户3'),
    ('赵六', '<EMAIL>', 28, 5500.00, TRUE, '测试用户4'),
    ('钱七', '<EMAIL>', 32, 6500.00, TRUE, '测试用户5')"""

        elif category == "性能":
            column_list = "user_id, product_id, quantity, price, order_date, status"
            value_rows = """
    (1, 101, 2, 99.99, '2024-01-01 10:00:00', 'completed'),
    (2, 102, 1, 149.99, '2024-01-01 11:00:00', 'pending'),
    (3, 103, 3, 79.99, '2024-01-01 12:00:00', 'completed'),
    (1, 104, 1, 199.99, '2024-01-02 09:00:00', 'shipped'),
    (4, 105, 2, 129.99, '2024-01-02 10:00:00', 'pending')"""

        elif category == "安全":
            column_list = "username, password_hash, role, last_login, failed_attempts, is_locked"
            value_rows = """
    ('admin', 'hashed_password_1', 'admin', '2024-01-01 08:00:00', 0, FALSE),
    ('user1', 'hashed_password_2', 'user', '2024-01-01 09:00:00', 0, FALSE),
    ('user2', 'hashed_password_3', 'user', '2024-01-01 10:00:00', 1, FALSE),
    ('locked_user', 'hashed_password_4', 'user', '2024-01-01 07:00:00', 5, TRUE),
    ('guest', 'hashed_password_5', 'guest', '2024-01-01 11:00:00', 0, FALSE)"""

        else:  # 异常测试
            column_list = "test_field, nullable_field, numeric_field, date_field"
            value_rows = """
    ('正常数据', '可空字段', 100, '2024-01-01'),
    ('边界测试', NULL, 0, '1900-01-01'),
    ('', '空字符串测试', -1, '2099-12-31'),
    ('特殊字符!@#$%', '中文测试', 999999, '2024-02-29'),
    ('长文本测试' || REPEAT('x', 100), '重复数据', 123, '2024-01-15')"""

        return self.template_engine.generate_sql_from_template(
            database_type,
            "batch_insert",
            {
                "table_name": table_name,
                "column_list": column_list,
                "value_rows": value_rows
            }
        )

    def _generate_test_operations(self, table_name: str, database_type: str, category: str) -> str:
        """生成测试操作SQL"""
        if category in ["功能", "边界"]:
            update_fields = "salary = salary * 1.1, updated_at = CURRENT_TIMESTAMP"
            condition = "age > 30"

        elif category == "性能":
            update_fields = "status = 'shipped', updated_at = CURRENT_TIMESTAMP"
            condition = "status = 'pending'"

        elif category == "安全":
            update_fields = "failed_attempts = 0, is_locked = FALSE, updated_at = CURRENT_TIMESTAMP"
            condition = "is_locked = TRUE"

        else:  # 异常测试
            update_fields = "test_field = 'updated', updated_at = CURRENT_TIMESTAMP"
            condition = "numeric_field < 0"

        crud_sql = self.template_engine.generate_sql_from_template(
            database_type,
            "crud_operations",
            {
                "table_name": table_name,
                "update_fields": update_fields,
                "condition": condition
            }
        )

        # 添加特定类别的额外测试
        additional_tests = []

        if category == "性能":
            additional_tests.append(f"""
-- 性能测试: 聚合查询
SELECT status, COUNT(*) as order_count, SUM(price * quantity) as total_amount
FROM {table_name}
GROUP BY status
ORDER BY total_amount DESC;

-- 性能测试: 复杂查询
SELECT user_id, AVG(price) as avg_price, COUNT(*) as order_count
FROM {table_name}
WHERE order_date >= '2024-01-01'
GROUP BY user_id
HAVING COUNT(*) > 1
ORDER BY avg_price DESC;""")

        elif category == "安全":
            additional_tests.append(f"""
-- 安全测试: 权限查询
SELECT role, COUNT(*) as user_count
FROM {table_name}
GROUP BY role
ORDER BY user_count DESC;

-- 安全测试: 登录失败统计
SELECT username, failed_attempts, is_locked
FROM {table_name}
WHERE failed_attempts > 0 OR is_locked = TRUE
ORDER BY failed_attempts DESC;""")

        elif category == "边界":
            additional_tests.append(f"""
-- 边界测试: 极值查询
SELECT MIN(age) as min_age, MAX(age) as max_age, AVG(age) as avg_age
FROM {table_name};

-- 边界测试: 空值处理
SELECT COUNT(*) as total_count,
       COUNT(email) as non_null_email_count,
       COUNT(*) - COUNT(email) as null_email_count
FROM {table_name};""")

        return crud_sql + "\n".join(additional_tests)

    def _generate_cleanup_sql(self, test_tables: List[str]) -> str:
        """生成清理脚本"""
        if not test_tables:
            return ""

        cleanup_statements = []
        for table in reversed(test_tables):  # 反向删除，避免外键约束问题
            cleanup_statements.append(f"DROP TABLE IF EXISTS {table};")

        return f"""
-- =============================================================================
-- 清理脚本 - 删除测试表
-- =============================================================================
-- 注意: 请谨慎执行清理脚本，确保不会影响生产数据

{chr(10).join(cleanup_statements)}

-- 清理完成
SELECT '测试表清理完成' as cleanup_status;"""


# 全局SQL生成器实例
sql_generator = SQLGenerator()
