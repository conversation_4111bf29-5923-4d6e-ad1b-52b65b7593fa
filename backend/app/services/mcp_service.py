"""
MCP服务 - 多数据库连接和SQL执行
"""
import asyncio
import uuid
import time
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod
import asyncpg
import aiomysql
import cx_Oracle
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from ..core.exceptions import MCPError, DatabaseError


class BaseDatabaseConnector(ABC):
    """数据库连接器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.connection = None
    
    @abstractmethod
    async def connect(self) -> bool:
        """建立连接"""
        pass
    
    @abstractmethod
    async def disconnect(self):
        """断开连接"""
        pass
    
    @abstractmethod
    async def execute_sql(self, sql: str) -> Dict[str, Any]:
        """执行SQL"""
        pass
    
    @abstractmethod
    async def test_connection(self) -> Dict[str, Any]:
        """测试连接"""
        pass


class PostgreSQLConnector(BaseDatabaseConnector):
    """PostgreSQL连接器 - 用于TDSQL-PG和TDSQL-Oracle"""
    
    async def connect(self) -> bool:
        try:
            self.connection = await asyncpg.connect(
                host=self.config["host"],
                port=self.config["port"],
                database=self.config["database"],
                user=self.config["username"],
                password=self.config["password"],
                **self.config.get("connection_params", {})
            )
            return True
        except Exception as e:
            raise DatabaseError(f"Failed to connect to PostgreSQL: {str(e)}")
    
    async def disconnect(self):
        if self.connection:
            await self.connection.close()
            self.connection = None
    
    async def execute_sql(self, sql: str) -> Dict[str, Any]:
        if not self.connection:
            await self.connect()
        
        try:
            start_time = time.time()
            
            # 分割SQL语句
            statements = [stmt.strip() for stmt in sql.split(';') if stmt.strip()]
            results = []
            total_rows_affected = 0
            
            for statement in statements:
                stmt_start = time.time()
                
                try:
                    if statement.upper().startswith(('SELECT', 'WITH')):
                        # 查询语句
                        rows = await self.connection.fetch(statement)
                        result_set = [dict(row) for row in rows]
                        results.append({
                            "statement": statement,
                            "status": "success",
                            "rows_affected": len(result_set),
                            "execution_time": time.time() - stmt_start,
                            "result_set": result_set
                        })
                        total_rows_affected += len(result_set)
                    else:
                        # 非查询语句
                        result = await self.connection.execute(statement)
                        # 解析影响的行数
                        rows_affected = 0
                        if result.startswith(('INSERT', 'UPDATE', 'DELETE')):
                            parts = result.split()
                            if len(parts) > 1 and parts[1].isdigit():
                                rows_affected = int(parts[1])
                        
                        results.append({
                            "statement": statement,
                            "status": "success",
                            "rows_affected": rows_affected,
                            "execution_time": time.time() - stmt_start,
                            "result_set": []
                        })
                        total_rows_affected += rows_affected
                        
                except Exception as e:
                    results.append({
                        "statement": statement,
                        "status": "error",
                        "error": str(e),
                        "execution_time": time.time() - stmt_start,
                        "result_set": []
                    })
            
            return {
                "success": True,
                "results": results,
                "total_execution_time": time.time() - start_time,
                "rows_affected": total_rows_affected
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "execution_time": time.time() - start_time,
                "rows_affected": 0
            }
    
    async def test_connection(self) -> Dict[str, Any]:
        try:
            if not self.connection:
                await self.connect()
            
            start_time = time.time()
            result = await self.connection.fetchval("SELECT version()")
            response_time = time.time() - start_time
            
            return {
                "success": True,
                "message": "Connection successful",
                "response_time": response_time,
                "server_version": result
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"Connection failed: {str(e)}"
            }


class MySQLConnector(BaseDatabaseConnector):
    """MySQL连接器 - 用于TDSQL2、TDSQL3和CynosDB"""
    
    async def connect(self) -> bool:
        try:
            self.connection = await aiomysql.connect(
                host=self.config["host"],
                port=self.config["port"],
                db=self.config["database"],
                user=self.config["username"],
                password=self.config["password"],
                **self.config.get("connection_params", {})
            )
            return True
        except Exception as e:
            raise DatabaseError(f"Failed to connect to MySQL: {str(e)}")
    
    async def disconnect(self):
        if self.connection:
            self.connection.close()
            self.connection = None
    
    async def execute_sql(self, sql: str) -> Dict[str, Any]:
        if not self.connection:
            await self.connect()
        
        try:
            start_time = time.time()
            cursor = await self.connection.cursor()
            
            # 分割SQL语句
            statements = [stmt.strip() for stmt in sql.split(';') if stmt.strip()]
            results = []
            total_rows_affected = 0
            
            for statement in statements:
                stmt_start = time.time()
                
                try:
                    await cursor.execute(statement)
                    
                    if statement.upper().startswith(('SELECT', 'SHOW', 'DESCRIBE', 'EXPLAIN')):
                        # 查询语句
                        rows = await cursor.fetchall()
                        # 获取列名
                        columns = [desc[0] for desc in cursor.description] if cursor.description else []
                        result_set = [dict(zip(columns, row)) for row in rows]
                        
                        results.append({
                            "statement": statement,
                            "status": "success",
                            "rows_affected": len(result_set),
                            "execution_time": time.time() - stmt_start,
                            "result_set": result_set
                        })
                        total_rows_affected += len(result_set)
                    else:
                        # 非查询语句
                        rows_affected = cursor.rowcount
                        results.append({
                            "statement": statement,
                            "status": "success",
                            "rows_affected": rows_affected,
                            "execution_time": time.time() - stmt_start,
                            "result_set": []
                        })
                        total_rows_affected += rows_affected
                        
                except Exception as e:
                    results.append({
                        "statement": statement,
                        "status": "error",
                        "error": str(e),
                        "execution_time": time.time() - stmt_start,
                        "result_set": []
                    })
            
            await cursor.close()
            
            return {
                "success": True,
                "results": results,
                "total_execution_time": time.time() - start_time,
                "rows_affected": total_rows_affected
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "execution_time": time.time() - start_time,
                "rows_affected": 0
            }
    
    async def test_connection(self) -> Dict[str, Any]:
        try:
            if not self.connection:
                await self.connect()
            
            start_time = time.time()
            cursor = await self.connection.cursor()
            await cursor.execute("SELECT VERSION()")
            result = await cursor.fetchone()
            await cursor.close()
            response_time = time.time() - start_time
            
            return {
                "success": True,
                "message": "Connection successful",
                "response_time": response_time,
                "server_version": result[0] if result else "Unknown"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"Connection failed: {str(e)}"
            }


class MCPService:
    """MCP服务管理器"""
    
    def __init__(self):
        self.connectors = {
            "tdsql-pg": PostgreSQLConnector,
            "tdsql-oracle": PostgreSQLConnector,
            "tdsql2": MySQLConnector,
            "tdsql3": MySQLConnector,
            "cynosdb": MySQLConnector
        }
        self.active_connections = {}
    
    def create_connector(self, database_type: str, config: Dict[str, Any]) -> BaseDatabaseConnector:
        """创建数据库连接器"""
        if database_type not in self.connectors:
            raise MCPError(f"Unsupported database type: {database_type}")
        
        connector_class = self.connectors[database_type]
        return connector_class(config)
    
    async def test_connection(self, database_type: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """测试数据库连接"""
        connector = self.create_connector(database_type, config)
        try:
            result = await connector.test_connection()
            await connector.disconnect()
            return result
        except Exception as e:
            return {
                "success": False,
                "message": f"Connection test failed: {str(e)}"
            }
    
    async def execute_sql(
        self, 
        connection_id: int, 
        database_type: str, 
        config: Dict[str, Any], 
        sql: str
    ) -> Dict[str, Any]:
        """执行SQL"""
        execution_id = str(uuid.uuid4())
        
        connector = self.create_connector(database_type, config)
        try:
            result = await connector.execute_sql(sql)
            await connector.disconnect()
            
            return {
                "execution_id": execution_id,
                "success": result["success"],
                "results": result.get("results", []),
                "error_message": result.get("error"),
                "execution_time": result.get("total_execution_time", 0),
                "rows_affected": result.get("rows_affected", 0),
                "executed_at": time.time()
            }
        except Exception as e:
            return {
                "execution_id": execution_id,
                "success": False,
                "results": [],
                "error_message": str(e),
                "execution_time": 0,
                "rows_affected": 0,
                "executed_at": time.time()
            }


# 全局MCP服务实例
mcp_service = MCPService()
