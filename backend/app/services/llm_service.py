"""
LLM服务 - 管理多种LLM模型提供商
重构版本：统一使用OpenAI格式API，支持工具调用和思考模型
"""
import time
import json
from typing import Dict, Any, List, Union
from abc import ABC, abstractmethod
import openai
import httpx
from ..config.settings import settings
from ..core.exceptions import LLMError



class BaseLLMProvider(ABC):
    """LLM提供商基类"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

    @abstractmethod
    async def generate(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        """生成文本
        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "..."}]
            **kwargs: 其他参数，包括 tools, temperature, max_tokens 等
        Returns:
            统一格式的响应：
            {
                "success": bool,
                "content": str,  # 主要内容
                "reasoning_content": str,  # 思考内容（如果有）
                "prompt_tokens": int,
                "completion_tokens": int,
                "total_tokens": int,
                "response_time": float,
                "model": str,
                "tool_calls": List[Dict],  # 工具调用（如果有）
                "error": str  # 错误信息（如果失败）
            }
        """
        pass

    async def generate_stream(self, messages: List[Dict[str, str]], **kwargs):
        """流式生成文本（默认实现：调用 generate 并模拟流式输出）
        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "..."}]
            **kwargs: 其他参数，包括 tools, temperature, max_tokens 等
        Yields:
            Dict[str, Any]: 流式数据块
        """
        # 默认实现：调用非流式方法并模拟流式输出
        result = await self.generate(messages, **kwargs)

        if result.get("success"):
            # 发送思考内容（如果有）
            if result.get("reasoning_content"):
                reasoning = result.get("reasoning_content", "")
                for i in range(0, len(reasoning), 10):  # 每次发送10个字符
                    yield {"type": "reasoning", "content": reasoning[i:i+10]}

            # 发送主要内容
            if result.get("content"):
                content = result.get("content", "")
                for i in range(0, len(content), 10):  # 每次发送10个字符
                    yield {"type": "content", "content": content[i:i+10]}

            # 发送工具调用
            if result.get("tool_calls"):
                for tool_call in result.get("tool_calls", []):
                    yield {"type": "tool_call", "tool_call": tool_call}

            # 发送指标
            yield {
                "type": "metrics",
                "prompt_tokens": result.get("prompt_tokens", 0),
                "completion_tokens": result.get("completion_tokens", 0),
                "total_tokens": result.get("total_tokens", 0),
                "model": result.get("model", "")
            }
        else:
            yield {"type": "error", "error": result.get("error", "Unknown error")}

    @abstractmethod
    async def test_connection(self) -> Dict[str, Any]:
        """测试连接"""
        pass

    def _extract_reasoning_content(self, content: str) -> tuple[str, str]:
        """提取思考内容和主要内容"""
        # 检查是否包含思考标记
        if "<thinking>" in content and "</thinking>" in content:
            start = content.find("<thinking>") + len("<thinking>")
            end = content.find("</thinking>")
            reasoning = content[start:end].strip()
            main_content = content[end + len("</thinking>"):].strip()
            return reasoning, main_content
        return "", content


class OpenAICompatibleProvider(BaseLLMProvider):
    """OpenAI兼容格式提供商 - 统一实现"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.client = openai.AsyncOpenAI(
            api_key=config.get("api_key"),
            base_url=config.get("base_url")
        )

    async def generate(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        try:
            start_time = time.time()

            # 默认启用流式输出以避免超时
            enable_stream = kwargs.get("enable_stream", True)

            # 准备请求参数
            request_params = {
                "model": self.config["model_name"],
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.config.get("max_tokens", 32768)),
                "temperature": kwargs.get("temperature", self.config.get("temperature", 0.7)),
                "stream": enable_stream
            }

            # 添加工具调用支持
            if "tools" in kwargs and kwargs["tools"]:
                request_params["tools"] = kwargs["tools"]
                if "tool_choice" in kwargs:
                    request_params["tool_choice"] = kwargs["tool_choice"]

            response = await self.client.chat.completions.create(**request_params)

            if enable_stream:
                # 处理流式响应
                content = ""
                prompt_tokens = 0
                completion_tokens = 0
                total_tokens = 0
                model_name = self.config["model_name"]
                tool_calls = []

                reasoning_content = ""

                async for chunk in response:
                    # 处理内容增量
                    if (hasattr(chunk, 'choices') and
                        chunk.choices and
                        len(chunk.choices) > 0 and
                        hasattr(chunk.choices[0], 'delta') and
                        chunk.choices[0].delta):

                        delta = chunk.choices[0].delta

                        # 处理文本内容
                        if hasattr(delta, 'content') and delta.content:
                            content += delta.content

                        # 直接处理思考内容
                        if hasattr(delta, 'reasoning_content') and delta.reasoning_content:
                            reasoning_content += delta.reasoning_content

                        # 处理工具调用
                        if hasattr(delta, 'tool_calls') and delta.tool_calls:
                            for tool_call in delta.tool_calls:
                                # 确保工具调用列表足够长
                                if hasattr(tool_call, 'index'):
                                    while len(tool_calls) <= tool_call.index:
                                        tool_calls.append({
                                            "id": "",
                                            "type": "function",
                                            "function": {"name": "", "arguments": ""}
                                        })

                                    # 更新工具调用信息
                                    if hasattr(tool_call, 'id') and tool_call.id:
                                        tool_calls[tool_call.index]["id"] = tool_call.id
                                    if hasattr(tool_call, 'function') and tool_call.function:
                                        if hasattr(tool_call.function, 'name') and tool_call.function.name:
                                            tool_calls[tool_call.index]["function"]["name"] = tool_call.function.name
                                        if hasattr(tool_call.function, 'arguments') and tool_call.function.arguments:
                                            tool_calls[tool_call.index]["function"]["arguments"] += tool_call.function.arguments

                        # 获取使用统计
                        if hasattr(chunk, 'usage') and chunk.usage and chunk.choices[0].finish_reason and chunk.choices[0].finish_reason == "stop":
                            if hasattr(chunk.usage, 'prompt_tokens'):
                                prompt_tokens = chunk.usage.prompt_tokens or 0
                            if hasattr(chunk.usage, 'completion_tokens'):
                                completion_tokens = chunk.usage.completion_tokens or 0
                            if hasattr(chunk.usage, 'total_tokens'):
                                total_tokens = chunk.usage.total_tokens or 0

                    # 获取实际的模型名称
                    if hasattr(chunk, 'model') and chunk.model:
                        model_name = chunk.model

                end_time = time.time()

                return {
                    "success": True,
                    "content": content,
                    "reasoning_content": reasoning_content,
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": total_tokens,
                    "response_time": end_time - start_time,
                    "model": model_name,
                    "tool_calls": tool_calls
                }
            else:
                # 非流式响应
                end_time = time.time()

                # 获取token统计
                prompt_tokens = 0
                completion_tokens = 0
                total_tokens = 0

                if hasattr(response, 'usage') and response.usage:
                    if hasattr(response.usage, 'prompt_tokens'):
                        prompt_tokens = response.usage.prompt_tokens or 0
                    if hasattr(response.usage, 'completion_tokens'):
                        completion_tokens = response.usage.completion_tokens or 0
                    if hasattr(response.usage, 'total_tokens'):
                        total_tokens = response.usage.total_tokens or 0

                # 获取内容和工具调用
                message = response.choices[0].message
                content = getattr(message, 'content', '') or ""
                reasoning_content = getattr(message, 'reasoning_content', '') or ""
                tool_calls = []

                if hasattr(message, 'tool_calls') and message.tool_calls:
                    tool_calls = []
                    for tc in message.tool_calls:
                        tool_call = {
                            "id": getattr(tc, 'id', ''),
                            "type": getattr(tc, 'type', 'function'),
                            "function": {
                                "name": getattr(tc.function, 'name', '') if hasattr(tc, 'function') else '',
                                "arguments": getattr(tc.function, 'arguments', '') if hasattr(tc, 'function') else ''
                            }
                        }
                        tool_calls.append(tool_call)

                return {
                    "success": True,
                    "content": content,
                    "reasoning_content": reasoning_content,
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": total_tokens,
                    "response_time": end_time - start_time,
                    "model": response.model,
                    "tool_calls": tool_calls
                }

        except Exception as e:
            error_msg = str(e)

            # 检查是否是API密钥相关的错误
            if "api_key" in error_msg.lower() or "unauthorized" in error_msg.lower():
                error_msg = "无效的API密钥或未授权访问"
            elif "rate_limit" in error_msg.lower():
                error_msg = "API调用频率限制"
            elif "quota" in error_msg.lower():
                error_msg = "API配额不足"

            return {
                "success": False,
                "error": error_msg,
                "content": "",
                "reasoning_content": "",
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0,
                "response_time": time.time() - start_time if 'start_time' in locals() else 0,
                "model": self.config.get("model_name", "unknown"),
                "tool_calls": []
            }

    async def generate_stream(self, messages: List[Dict[str, str]], **kwargs):
        """真正的流式生成文本"""
        try:
            start_time = time.time()

            # 准备请求参数
            request_params = {
                "model": self.config["model_name"],
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.config.get("max_tokens", 32768)),
                "temperature": kwargs.get("temperature", self.config.get("temperature", 0.7)),
                "stream": True  # 强制启用流式输出
            }



            # 添加工具调用支持
            if "tools" in kwargs and kwargs["tools"]:
                request_params["tools"] = kwargs["tools"]
                if "tool_choice" in kwargs:
                    request_params["tool_choice"] = kwargs["tool_choice"]

            response = await self.client.chat.completions.create(**request_params)

            # 处理流式响应
            content = ""
            prompt_tokens = 0
            completion_tokens = 0
            total_tokens = 0
            model_name = self.config["model_name"]
            tool_calls = []

            async for chunk in response:
                # 处理 OpenAI 兼容格式的流式响应
                if (hasattr(chunk, 'choices') and
                    chunk.choices and
                    len(chunk.choices) > 0 and
                    hasattr(chunk.choices[0], 'delta') and
                    chunk.choices[0].delta):

                    delta = chunk.choices[0].delta

                    # 处理思考内容（reasoning_content）
                    if hasattr(delta, 'reasoning_content') and delta.reasoning_content:
                        reasoning_chunk = delta.reasoning_content
                        yield {"type": "reasoning", "content": reasoning_chunk}

                    # 处理主要文本内容 - 直接发送所有内容
                    if hasattr(delta, 'content') and delta.content:
                        content_chunk = delta.content
                        content += content_chunk
                        
                        # 直接发送所有内容块，保持原始格式
                        yield {"type": "content", "content": content_chunk}

                    # 处理工具调用
                    if hasattr(delta, 'tool_calls') and delta.tool_calls:
                        for tool_call in delta.tool_calls:
                            if hasattr(tool_call, 'index'):
                                index = tool_call.index
                                # 确保工具调用列表足够长
                                while len(tool_calls) <= index:
                                    tool_calls.append({"index": len(tool_calls), "function": {"name": "", "arguments": ""}})

                                if hasattr(tool_call, 'function') and tool_call.function:
                                    if hasattr(tool_call.function, 'name') and tool_call.function.name:
                                        tool_calls[index]["function"]["name"] = tool_call.function.name
                                    if hasattr(tool_call.function, 'arguments') and tool_call.function.arguments:
                                        tool_calls[index]["function"]["arguments"] += tool_call.function.arguments

                                        # 发送工具调用更新
                                        yield {"type": "tool_call", "tool_call": tool_calls[index]}

                    # 获取使用统计
                    if hasattr(chunk, 'usage') and chunk.usage and chunk.choices[0].finish_reason:
                        if hasattr(chunk.usage, 'prompt_tokens'):
                            prompt_tokens = chunk.usage.prompt_tokens or 0
                        if hasattr(chunk.usage, 'completion_tokens'):
                            completion_tokens = chunk.usage.completion_tokens or 0
                        if hasattr(chunk.usage, 'total_tokens'):
                            total_tokens = chunk.usage.total_tokens or 0

                # 获取实际的模型名称
                if hasattr(chunk, 'model') and chunk.model:
                    model_name = chunk.model

            end_time = time.time()

            # 发送最终指标
            yield {
                "type": "metrics",
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens,
                "total_tokens": total_tokens,
                "model": model_name,
                "response_time": end_time - start_time
            }

        except Exception as e:
            yield {"type": "error", "error": str(e)}

    async def test_connection(self) -> Dict[str, Any]:
        return await self.generate([{"role": "user", "content": "Hello, this is a test message."}])


# Anthropic 现在使用 OpenAI 兼容格式
class AnthropicProvider(OpenAICompatibleProvider):
    """Anthropic提供商 - 使用OpenAI兼容格式"""
    pass


# DeepSeek 现在使用 OpenAI 兼容格式
class DeepSeekProvider(OpenAICompatibleProvider):
    """DeepSeek提供商 - 使用OpenAI兼容格式"""
    pass


# Google 现在使用 OpenAI 兼容格式
class GoogleProvider(OpenAICompatibleProvider):
    """Google Gemini提供商 - 使用OpenAI兼容格式"""
    pass


class OllamaProvider(BaseLLMProvider):
    """Ollama提供商"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.base_url = config.get("base_url", settings.OLLAMA_BASE_URL)

    async def generate(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        # 将消息转换为单个 prompt（Ollama 使用简单的 prompt 格式）
        if isinstance(messages, list) and len(messages) > 0:
            prompt = messages[-1].get("content", "")
        else:
            prompt = str(messages)
        try:
            start_time = time.time()

            # 默认启用流式输出以避免超时
            enable_stream = kwargs.get("enable_stream", True)

            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/api/generate",
                    json={
                        "model": self.config["model_name"],
                        "prompt": prompt,
                        "stream": enable_stream,
                        "options": {
                            "temperature": kwargs.get("temperature", self.config.get("temperature", 0.7))
                        }
                    },
                    timeout=120.0 if enable_stream else 60.0
                )

                if response.status_code == 200:
                    if enable_stream:
                        # 处理流式响应
                        content = ""
                        tokens_used = 0
                        model_name = self.config["model_name"]

                        for line in response.text.split('\n'):
                            if line.strip():
                                try:
                                    data = json.loads(line)
                                    if 'response' in data:
                                        content += data['response']
                                    if 'eval_count' in data:
                                        tokens_used += data.get('eval_count', 0) + data.get('prompt_eval_count', 0)
                                    if 'model' in data:
                                        model_name = data['model']
                                except json.JSONDecodeError:
                                    continue

                        end_time = time.time()

                        # Ollama 可能不直接支持 reasoning_content，使用传统方法提取
                        reasoning_content, main_content = self._extract_reasoning_content(content)

                        return {
                            "success": True,
                            "content": main_content,
                            "reasoning_content": reasoning_content,
                            "prompt_tokens": 0,  # Ollama 不提供详细的 token 分类
                            "completion_tokens": tokens_used,
                            "total_tokens": tokens_used,
                            "response_time": end_time - start_time,
                            "model": model_name,
                            "tool_calls": []
                        }
                    else:
                        # 非流式响应
                        data = response.json()
                        end_time = time.time()

                        content = data["response"]
                        tokens_used = data.get("eval_count", 0) + data.get("prompt_eval_count", 0)

                        # 提取思考内容
                        reasoning_content, main_content = self._extract_reasoning_content(content)

                        return {
                            "success": True,
                            "content": main_content,
                            "reasoning_content": reasoning_content,
                            "prompt_tokens": 0,  # Ollama 不提供详细的 token 分类
                            "completion_tokens": tokens_used,
                            "total_tokens": tokens_used,
                            "response_time": end_time - start_time,
                            "model": data["model"],
                            "tool_calls": []
                        }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "content": "",
                        "reasoning_content": "",
                        "prompt_tokens": 0,
                        "completion_tokens": 0,
                        "total_tokens": 0,
                        "response_time": time.time() - start_time,
                        "model": self.config.get("model_name", "unknown"),
                        "tool_calls": []
                    }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "reasoning_content": "",
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0,
                "response_time": time.time() - start_time if 'start_time' in locals() else 0,
                "model": self.config.get("model_name", "unknown"),
                "tool_calls": []
            }

    async def generate_stream(self, messages: List[Dict[str, str]], **kwargs):
        """Ollama 流式生成文本"""
        try:
            start_time = time.time()

            # 将消息转换为单个 prompt（Ollama 使用简单的 prompt 格式）
            if isinstance(messages, list) and len(messages) > 0:
                prompt = messages[-1].get("content", "")
            else:
                prompt = str(messages)

            # 准备请求数据
            data = {
                "model": self.config["model_name"],
                "prompt": prompt,
                "stream": True,  # 启用流式输出
                "options": {
                    "temperature": kwargs.get("temperature", self.config.get("temperature", 0.7)),
                    "num_predict": kwargs.get("max_tokens", self.config.get("max_tokens", 32768))
                }
            }

            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/api/generate", json=data) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        yield {"type": "error", "error": f"Ollama API error: {error_text}"}
                        return

                    content = ""
                    in_thinking = False

                    async for line in response.content:
                        if line:
                            try:
                                import json
                                chunk_data = json.loads(line.decode('utf-8'))

                                if "response" in chunk_data:
                                    response_text = chunk_data["response"]
                                    content += response_text

                                    # 检查思考标记
                                    if "<think>" in response_text or "<thinking>" in response_text:
                                        in_thinking = True
                                        # 发送思考开始前的内容
                                        before_thinking = response_text.split("<thinking>")[0]
                                        if before_thinking:
                                            yield {"type": "content", "content": before_thinking}
                                        continue
                                    elif "</think>" in response_text or "</thinking>" in response_text:
                                        in_thinking = False
                                        # 发送思考结束后的内容
                                        after_thinking = response_text.split("</thinking>")
                                        if len(after_thinking) > 1:
                                            yield {"type": "content", "content": after_thinking[1]}
                                            continue
                                        else:
                                            after_thinking = response_text.split("</think>")[1]
                                            if after_thinking:
                                                yield {"type": "content", "content": after_thinking}
                                                continue

                                    # 根据当前状态发送内容
                                    if in_thinking:
                                        yield {"type": "reasoning", "content": response_text}
                                    else:
                                        yield {"type": "content", "content": response_text}

                                # 检查是否完成
                                if chunk_data.get("done", False):
                                    # 发送最终指标
                                    yield {
                                        "type": "metrics",
                                        "prompt_tokens": chunk_data.get("prompt_eval_count", 0),
                                        "completion_tokens": chunk_data.get("eval_count", 0),
                                        "total_tokens": chunk_data.get("prompt_eval_count", 0) + chunk_data.get("eval_count", 0),
                                        "model": self.config["model_name"],
                                        "response_time": time.time() - start_time
                                    }
                                    break

                            except json.JSONDecodeError:
                                continue

        except Exception as e:
            yield {"type": "error", "error": str(e)}

    async def test_connection(self) -> Dict[str, Any]:
        return await self.generate([{"role": "user", "content": "Hello, this is a test message."}])


# 自定义提供商现在使用 OpenAI 兼容格式
class CustomProvider(OpenAICompatibleProvider):
    """自定义提供商 - 使用OpenAI兼容格式"""
    pass




class LLMService:
    """LLM服务管理器"""
    
    def __init__(self):
        self.providers = {
            "openai": OpenAICompatibleProvider,
            "anthropic": AnthropicProvider,
            "google": GoogleProvider,
            "deepseek": DeepSeekProvider,
            "ollama": OllamaProvider,
            "custom": CustomProvider
        }
    
    def create_provider(self, config: Dict[str, Any]) -> BaseLLMProvider:
        """创建LLM提供商实例"""
        provider_type = config.get("provider")
        if provider_type not in self.providers:
            raise LLMError(f"Unsupported provider: {provider_type}")
        
        provider_class = self.providers[provider_type]
        return provider_class(config)
    
    async def generate_text(
        self,
        model_config: Dict[str, Any],
        messages: Union[str, List[Dict[str, str]]],
        **kwargs
    ) -> Dict[str, Any]:
        """生成文本
        Args:
            model_config: 模型配置
            messages: 消息列表或单个字符串（兼容旧接口）
            **kwargs: 其他参数，包括 tools, temperature, max_tokens 等
        """
        try:
            provider = self.create_provider(model_config)

            # 兼容旧接口：如果传入的是字符串，转换为消息格式
            if isinstance(messages, str):
                messages = [{"role": "user", "content": messages}]

            return await provider.generate(messages, **kwargs)
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "reasoning_content": "",
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0,
                "response_time": 0,
                "model": model_config.get("model_name", "unknown"),
                "tool_calls": []
            }
    
    async def test_model(self, model_config: Dict[str, Any]) -> Dict[str, Any]:
        """测试模型连接"""
        provider = self.create_provider(model_config)
        return await provider.test_connection()

    async def test_model_stream(self, model_config: Dict[str, Any], test_prompt: str, **kwargs):
        """流式测试模型连接"""
        try:
            # 强制启用流式输出
            config = model_config.copy()
            config["enable_stream"] = True

            # 构建消息
            messages = [{"role": "user", "content": test_prompt}]

            # 创建提供商实例
            provider = self.create_provider(config)

            # 调用流式生成
            async for chunk in provider.generate_stream(messages, **kwargs):
                yield chunk

        except Exception as e:
            yield {
                "type": "error",
                "error": str(e)
            }


# 全局LLM服务实例
llm_service = LLMService()
