"""
自定义异常类
"""
from typing import Optional


class CustomException(Exception):
    """自定义异常基类"""
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        detail: Optional[str] = None,
        error_code: Optional[str] = None
    ):
        self.message = message
        self.status_code = status_code
        self.detail = detail
        self.error_code = error_code
        super().__init__(self.message)


class AuthenticationError(CustomException):
    """认证错误"""
    
    def __init__(self, message: str = "Authentication failed", detail: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=401,
            detail=detail,
            error_code="AUTH_ERROR"
        )


class AuthorizationError(CustomException):
    """授权错误"""
    
    def __init__(self, message: str = "Access denied", detail: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=403,
            detail=detail,
            error_code="AUTHORIZATION_ERROR"
        )


class ValidationError(CustomException):
    """验证错误"""
    
    def __init__(self, message: str = "Validation failed", detail: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=400,
            detail=detail,
            error_code="VALIDATION_ERROR"
        )


class NotFoundError(CustomException):
    """资源未找到错误"""
    
    def __init__(self, message: str = "Resource not found", detail: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=404,
            detail=detail,
            error_code="NOT_FOUND"
        )


class ConflictError(CustomException):
    """冲突错误"""
    
    def __init__(self, message: str = "Resource conflict", detail: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=409,
            detail=detail,
            error_code="CONFLICT_ERROR"
        )


class LLMError(CustomException):
    """LLM相关错误"""
    
    def __init__(self, message: str = "LLM service error", detail: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=500,
            detail=detail,
            error_code="LLM_ERROR"
        )


class DatabaseError(CustomException):
    """数据库错误"""
    
    def __init__(self, message: str = "Database error", detail: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=500,
            detail=detail,
            error_code="DATABASE_ERROR"
        )


class KnowledgeBaseError(CustomException):
    """知识库错误"""
    
    def __init__(self, message: str = "Knowledge base error", detail: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=500,
            detail=detail,
            error_code="KNOWLEDGE_BASE_ERROR"
        )


class MCPError(CustomException):
    """MCP服务错误"""
    
    def __init__(self, message: str = "MCP service error", detail: Optional[str] = None):
        super().__init__(
            message=message,
            status_code=500,
            detail=detail,
            error_code="MCP_ERROR"
        )
