"""
认证相关工具函数和依赖
"""
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional
from .security import verify_token
from ..config.database import get_db
from ..core.exceptions import AuthenticationError

security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """获取当前认证用户"""
    try:
        # 验证token
        payload = verify_token(credentials.credentials)
        username = payload.get("sub")
        
        if username is None:
            raise AuthenticationError("Invalid token payload")
        
        # 这里应该从数据库查询用户信息
        # user = db.query(User).filter(User.username == username).first()
        # if user is None:
        #     raise AuthenticationError("User not found")
        # if not user.is_active:
        #     raise AuthenticationError("User is inactive")
        
        # 临时返回用户信息
        return {
            "id": 1,
            "username": username,
            "email": "<EMAIL>",
            "is_active": True
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: dict = Depends(get_current_user)
):
    """获取当前活跃用户"""
    if not current_user.get("is_active"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


def require_permissions(*permissions):
    """权限装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 这里可以实现权限检查逻辑
            return await func(*args, **kwargs)
        return wrapper
    return decorator
