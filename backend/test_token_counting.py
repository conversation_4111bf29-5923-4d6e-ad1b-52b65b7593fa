#!/usr/bin/env python3
"""
测试LLM提供商的token统计功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.llm_service import LLMService

async def test_provider_directly():
    """直接测试提供商实现"""
    from app.services.llm_service import OpenAIProvider, GoogleProvider

    # 测试用的简单prompt
    test_prompt = "Hello, this is a test message. Please respond with exactly 'Test response'."

    print("=== 直接测试OpenAI提供商 ===")
    try:
        openai_config = {
            "provider": "openai",
            "model_name": "gpt-3.5-turbo",
            "api_key": "sk-test-key",  # 需要真实的API key
            "max_tokens": 50,
            "temperature": 0.0
        }

        provider = OpenAIProvider(openai_config)
        result = await provider.generate(test_prompt, enable_stream=False)

        print(f"结果: {result}")
        print(f"成功: {result.get('success')}")
        print(f"内容: {result.get('content', '')[:100]}...")
        print(f"Token使用: {result.get('tokens_used', 0)}")
        print(f"响应时间: {result.get('response_time', 0):.2f}s")

    except Exception as e:
        print(f"OpenAI测试异常: {str(e)}")

    print("\n=== 直接测试Google提供商 ===")
    try:
        google_config = {
            "provider": "google",
            "model_name": "gemini-pro",
            "api_key": "test-key",  # 需要真实的API key
            "max_tokens": 50,
            "temperature": 0.0
        }

        provider = GoogleProvider(google_config)
        result = await provider.generate(test_prompt, enable_stream=False)

        print(f"结果: {result}")
        print(f"成功: {result.get('success')}")
        print(f"内容: {result.get('content', '')[:100]}...")
        print(f"Token使用: {result.get('tokens_used', 0)}")
        print(f"响应时间: {result.get('response_time', 0):.2f}s")

    except Exception as e:
        print(f"Google测试异常: {str(e)}")

async def test_token_counting():
    """测试各个提供商的token统计"""
    llm_service = LLMService()

    # 测试用的简单prompt
    test_prompt = "Hello, this is a test message. Please respond briefly."

    # 测试配置
    test_configs = [
        {
            "name": "OpenAI GPT-3.5",
            "provider": "openai",
            "model_name": "gpt-3.5-turbo",
            "api_key": "sk-test-key",  # 这里需要真实的API key
            "max_tokens": 100,
            "temperature": 0.7
        },
        {
            "name": "Google Gemini",
            "provider": "google",
            "model_name": "gemini-pro",
            "api_key": "test-key",  # 这里需要真实的API key
            "max_tokens": 100,
            "temperature": 0.7
        }
    ]

    for config in test_configs:
        print(f"\n=== 测试 {config['name']} ===")

        try:
            # 测试非流式响应
            print("测试非流式响应...")
            result = await llm_service.generate_text(
                config,
                test_prompt,
                enable_stream=False
            )

            print(f"成功: {result.get('success')}")
            if result.get('success'):
                print(f"内容长度: {len(result.get('content', ''))}")
                print(f"Token使用: {result.get('tokens_used', 0)}")
                print(f"响应时间: {result.get('response_time', 0):.2f}s")
                print(f"模型: {result.get('model', 'unknown')}")
            else:
                print(f"错误: {result.get('error')}")

        except Exception as e:
            print(f"异常: {str(e)}")

if __name__ == "__main__":
    print("开始测试token统计功能...")
    asyncio.run(test_provider_directly())
    print("\n" + "="*50)
    asyncio.run(test_token_counting())
