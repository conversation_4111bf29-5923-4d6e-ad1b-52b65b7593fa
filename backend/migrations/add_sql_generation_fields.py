"""
数据库迁移脚本 - 为SQL生成表添加新字段
"""
import asyncio
import asyncpg
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_database_url():
    """获取数据库URL"""
    return os.getenv("DATABASE_URL", "*****************************************/tdsql_ai_testing")


async def upgrade():
    """升级数据库结构 - 添加SQL生成相关字段"""
    database_url = get_database_url()
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(database_url)
        
        logger.info("开始升级数据库结构...")
        
        # 为sql_generations表添加新字段
        sql_commands = [
            # 添加生成配置字段
            """
            ALTER TABLE sql_generations 
            ADD COLUMN IF NOT EXISTS custom_requirements TEXT,
            ADD COLUMN IF NOT EXISTS use_ai_generation BOOLEAN DEFAULT TRUE,
            ADD COLUMN IF NOT EXISTS generation_method VARCHAR(20) DEFAULT 'ai'
            """,
            
            # 添加统计字段
            """
            ALTER TABLE sql_generations 
            ADD COLUMN IF NOT EXISTS scenario_count INTEGER DEFAULT 0,
            ADD COLUMN IF NOT EXISTS generation_time FLOAT DEFAULT 0.0,
            ADD COLUMN IF NOT EXISTS tokens_used INTEGER DEFAULT 0
            """,
            
            # 添加错误信息字段
            """
            ALTER TABLE sql_generations 
            ADD COLUMN IF NOT EXISTS error_message TEXT,
            ADD COLUMN IF NOT EXISTS generated_at TIMESTAMP WITH TIME ZONE
            """,
            
            # 创建索引
            """
            CREATE INDEX IF NOT EXISTS idx_sql_generations_generation_method 
            ON sql_generations(generation_method)
            """,
            
            """
            CREATE INDEX IF NOT EXISTS idx_sql_generations_use_ai_generation 
            ON sql_generations(use_ai_generation)
            """,
            
            """
            CREATE INDEX IF NOT EXISTS idx_sql_generations_generated_at 
            ON sql_generations(generated_at)
            """
        ]
        
        # 执行SQL命令
        for i, sql_command in enumerate(sql_commands):
            try:
                await conn.execute(sql_command)
                logger.info(f"执行SQL命令 {i+1}/{len(sql_commands)} 成功")
            except Exception as e:
                logger.error(f"执行SQL命令 {i+1} 失败: {str(e)}")
                logger.error(f"失败的SQL: {sql_command}")
                raise
        
        logger.info("数据库结构升级完成")
        
    except Exception as e:
        logger.error(f"数据库升级失败: {str(e)}")
        raise
    finally:
        if 'conn' in locals():
            await conn.close()


async def downgrade():
    """降级数据库结构 - 移除SQL生成相关字段"""
    database_url = get_database_url()
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(database_url)
        
        logger.info("开始降级数据库结构...")
        
        # 移除索引
        drop_index_commands = [
            "DROP INDEX IF EXISTS idx_sql_generations_generation_method",
            "DROP INDEX IF EXISTS idx_sql_generations_use_ai_generation", 
            "DROP INDEX IF EXISTS idx_sql_generations_generated_at"
        ]
        
        # 移除字段
        drop_column_commands = [
            """
            ALTER TABLE sql_generations 
            DROP COLUMN IF EXISTS custom_requirements,
            DROP COLUMN IF EXISTS use_ai_generation,
            DROP COLUMN IF EXISTS generation_method,
            DROP COLUMN IF EXISTS scenario_count,
            DROP COLUMN IF EXISTS generation_time,
            DROP COLUMN IF EXISTS tokens_used,
            DROP COLUMN IF EXISTS error_message,
            DROP COLUMN IF EXISTS generated_at
            """
        ]
        
        all_commands = drop_index_commands + drop_column_commands
        
        # 执行SQL命令
        for i, sql_command in enumerate(all_commands):
            try:
                await conn.execute(sql_command)
                logger.info(f"执行降级命令 {i+1}/{len(all_commands)} 成功")
            except Exception as e:
                logger.error(f"执行降级命令 {i+1} 失败: {str(e)}")
                logger.error(f"失败的SQL: {sql_command}")
                raise
        
        logger.info("数据库结构降级完成")
        
    except Exception as e:
        logger.error(f"数据库降级失败: {str(e)}")
        raise
    finally:
        if 'conn' in locals():
            await conn.close()


async def main():
    """主函数 - 执行数据库迁移"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "downgrade":
        await downgrade()
    else:
        await upgrade()


if __name__ == "__main__":
    asyncio.run(main())
