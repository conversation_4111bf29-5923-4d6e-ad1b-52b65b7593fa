"""
添加调试字段到测试用例表

Revision ID: add_debug_fields_to_test_case
Revises: 
Create Date: 2025-01-25 10:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = 'add_debug_fields_to_test_case'
down_revision = None
depends_on = None


def upgrade():
    """添加调试字段到test_cases表"""
    # 添加错误消息字段
    op.add_column('test_cases', sa.Column('error_message', sa.Text(), nullable=True))
    
    # 添加调试信息字段
    op.add_column('test_cases', sa.Column('debug_info', sa.JSON(), nullable=True))


def downgrade():
    """移除调试字段"""
    op.drop_column('test_cases', 'debug_info')
    op.drop_column('test_cases', 'error_message')
