#!/usr/bin/env python3
"""
测试 LLM 服务中的 ChatCompletionChunk 错误
"""
import asyncio
import sys
import os
import json
import requests

# 添加后端路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend', 'app'))

async def test_with_real_api():
    """使用真实的 API 测试"""
    print("=== 测试真实 API 调用 ===")
    
    # 首先登录获取 token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # 登录
        login_response = requests.post("http://localhost:8000/api/v1/auth/login", json=login_data)
        if login_response.status_code == 200:
            token = login_response.json().get("access_token")
            print(f"✓ 登录成功，获取到 token")
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # 获取模型列表
            models_response = requests.get("http://localhost:8000/api/v1/llm/models", headers=headers)
            if models_response.status_code == 200:
                models = models_response.json()
                print(f"✓ 获取到 {len(models)} 个模型")
                
                if models:
                    # 使用第一个模型进行测试
                    model_id = models[0]["id"]
                    print(f"✓ 使用模型 ID: {model_id}")
                    
                    # 测试模型
                    test_data = {
                        "model_id": model_id,
                        "test_prompt": "Hello, this is a test message with thinking. <thinking>I should respond politely</thinking>Hello! Nice to meet you."
                    }
                    
                    test_response = requests.post("http://localhost:8000/api/v1/llm/models/test", 
                                                json=test_data, headers=headers)
                    
                    print(f"测试响应状态码: {test_response.status_code}")
                    print(f"测试响应内容: {test_response.text}")
                    
                    if test_response.status_code == 200:
                        result = test_response.json()
                        print(f"✓ 测试成功")
                        print(f"  成功: {result.get('success')}")
                        print(f"  内容: {result.get('content', '')[:100]}...")
                        print(f"  思考内容: {result.get('reasoning_content', '')[:100]}...")
                        print(f"  Token统计: {result.get('prompt_tokens')}/{result.get('completion_tokens')}/{result.get('total_tokens')}")
                    else:
                        print(f"✗ 测试失败: {test_response.text}")
                        
                        # 如果有错误，查看后端日志
                        print("\n查看后端日志...")
                        import subprocess
                        try:
                            logs = subprocess.check_output(
                                ["docker-compose", "logs", "backend", "--tail=20"], 
                                cwd="/Users/<USER>/Documents/myporject/tdsql-ai-testing-platform",
                                text=True
                            )
                            print("后端日志:")
                            print(logs)
                        except Exception as e:
                            print(f"无法获取日志: {e}")
                else:
                    print("✗ 没有可用的模型")
            else:
                print(f"✗ 获取模型列表失败: {models_response.text}")
        else:
            print(f"✗ 登录失败: {login_response.text}")
            
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

async def test_direct_llm_service():
    """直接测试 LLM 服务"""
    print("\n=== 直接测试 LLM 服务 ===")
    
    try:
        from services.llm_service import LLMService, OpenAICompatibleProvider
        
        # 创建一个模拟的配置
        config = {
            "provider": "openai",
            "model_name": "gpt-3.5-turbo",
            "api_key": "sk-test-key",  # 使用无效的 key 来触发错误
            "base_url": "https://api.openai.com/v1"
        }
        
        service = LLMService()
        
        # 测试生成文本
        messages = [{"role": "user", "content": "Hello, test message"}]
        result = await service.generate_text(config, messages)
        
        print(f"直接测试结果:")
        print(f"  成功: {result.get('success')}")
        print(f"  错误: {result.get('error', 'None')}")
        print(f"  响应格式: {list(result.keys())}")
        
    except Exception as e:
        print(f"✗ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主测试函数"""
    print("开始测试 LLM 服务错误...")
    
    await test_with_real_api()
    await test_direct_llm_service()
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    asyncio.run(main())
