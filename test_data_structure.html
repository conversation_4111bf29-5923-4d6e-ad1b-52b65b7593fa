<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试数据结构显示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #fafafa;
        }
        .scenario {
            border: 1px solid #d9f7be;
            border-radius: 6px;
            padding: 16px;
            margin: 12px 0;
            background: #f6ffed;
        }
        .scenario-title {
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 8px;
        }
        .scenario-desc {
            color: #666;
            margin-bottom: 12px;
        }
        .test-points {
            background: #e6f7ff;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 4px 0;
        }
        .test-point {
            display: inline-block;
            background: #1890ff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin: 2px 4px;
        }
        .recommendation {
            background: #fff7e6;
            border-left: 4px solid #fa8c16;
            padding: 12px;
            margin: 8px 0;
            border-radius: 4px;
        }
        .tag {
            background: #52c41a;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            margin-right: 8px;
        }
        .debug-info {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 需求分析结果数据结构测试</h1>
    
    <div class="test-section">
        <h2>测试数据 1：完整标准格式</h2>
        <div id="test1"></div>
        <div class="debug-info" id="debug1"></div>
    </div>
    
    <div class="test-section">
        <h2>测试数据 2：缺少部分字段</h2>
        <div id="test2"></div>
        <div class="debug-info" id="debug2"></div>
    </div>
    
    <div class="test-section">
        <h2>测试数据 3：空数据处理</h2>
        <div id="test3"></div>
        <div class="debug-info" id="debug3"></div>
    </div>

    <script>
        // 模拟不同的数据结构
        const testData1 = {
            id: 1,
            status: "completed",
            test_scenarios: [
                {
                    scenario: "用户登录场景",
                    description: "测试用户登录相关功能，包括正常登录、密码错误、用户不存在等情况",
                    test_points: ["用户名密码登录", "邮箱密码登录", "手机号登录", "记住登录状态", "登录失败处理"]
                },
                {
                    scenario: "数据管理场景",
                    description: "测试数据的增删改查操作",
                    test_points: ["数据创建", "数据查询", "数据更新", "数据删除", "批量操作"]
                }
            ],
            test_points: ["用户名密码登录", "邮箱密码登录", "数据创建", "数据查询", "性能测试", "安全测试"],
            recommendations: ["建议进行边界值测试", "建议进行并发测试", "建议进行安全测试", "建议添加日志记录"]
        };

        const testData2 = {
            test_scenarios: [
                {
                    scenario: "基本功能测试",
                    // 缺少description
                    test_points: ["功能点1", "功能点2"]
                },
                {
                    // 缺少scenario
                    description: "这是一个没有标题的场景",
                    test_points: []
                }
            ],
            test_points: ["独立测试点1", "独立测试点2"],
            // 缺少recommendations
        };

        const testData3 = {
            test_scenarios: [],
            test_points: [],
            recommendations: []
        };

        function renderAnalysisResult(data, containerId, debugId) {
            const container = document.getElementById(containerId);
            const debugContainer = document.getElementById(debugId);
            let html = '';

            // 调试信息
            debugContainer.innerHTML = `
                <strong>数据结构调试:</strong><br>
                - test_scenarios: ${Array.isArray(data.test_scenarios) ? data.test_scenarios.length : 'N/A'} 个<br>
                - test_points: ${Array.isArray(data.test_points) ? data.test_points.length : 'N/A'} 个<br>
                - recommendations: ${Array.isArray(data.recommendations) ? data.recommendations.length : 'N/A'} 个<br>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;

            // 渲染测试场景
            html += '<h3>📋 测试场景</h3>';
            if (data.test_scenarios && data.test_scenarios.length > 0) {
                data.test_scenarios.forEach((scenario, index) => {
                    const scenarioTitle = scenario.scenario || scenario.name || `场景 ${index + 1}`;
                    const scenarioDesc = scenario.description || scenario.desc || '暂无描述';
                    const testPoints = scenario.test_points || [];

                    html += `
                        <div class="scenario">
                            <div class="scenario-title">
                                <span class="tag">${index + 1}</span>${scenarioTitle}
                            </div>
                            <div class="scenario-desc">${scenarioDesc}</div>
                            ${testPoints.length > 0 ? `
                                <div class="test-points">
                                    <strong>测试点：</strong><br>
                                    ${testPoints.map((point, idx) => {
                                        const pointText = typeof point === 'string' ? point : point.name || JSON.stringify(point);
                                        return `<span class="test-point">${idx + 1}. ${pointText}</span>`;
                                    }).join('')}
                                </div>
                            ` : '<div style="color: #999; font-style: italic;">暂无测试点</div>'}
                        </div>
                    `;
                });
            } else {
                html += '<p style="color: #999; font-style: italic;">暂无测试场景</p>';
            }

            // 渲染测试点
            html += '<h3>🎯 测试点</h3>';
            if (data.test_points && data.test_points.length > 0) {
                html += '<div>';
                data.test_points.forEach((point, index) => {
                    const pointText = typeof point === 'string' ? point : point.name || JSON.stringify(point);
                    html += `<span class="test-point">${index + 1}. ${pointText}</span>`;
                });
                html += '</div>';
            } else {
                html += '<p style="color: #999; font-style: italic;">暂无测试点</p>';
            }

            // 渲染建议
            html += '<h3>💡 建议</h3>';
            if (data.recommendations && data.recommendations.length > 0) {
                data.recommendations.forEach((rec, index) => {
                    const recText = typeof rec === 'string' ? rec : rec.text || JSON.stringify(rec);
                    html += `<div class="recommendation"><span class="tag">${index + 1}</span>${recText}</div>`;
                });
            } else {
                html += '<p style="color: #999; font-style: italic;">暂无建议</p>';
            }

            container.innerHTML = html;
        }

        // 渲染测试数据
        renderAnalysisResult(testData1, 'test1', 'debug1');
        renderAnalysisResult(testData2, 'test2', 'debug2');
        renderAnalysisResult(testData3, 'test3', 'debug3');

        console.log('🔍 测试数据结构:');
        console.log('完整格式:', testData1);
        console.log('缺少字段:', testData2);
        console.log('空数据:', testData3);
    </script>
</body>
</html>
