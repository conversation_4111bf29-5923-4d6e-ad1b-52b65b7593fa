<!DOCTYPE html>
<html>
<head>
    <title>测试前端超时设置</title>
</head>
<body>
    <h1>测试前端API超时设置</h1>
    <button onclick="testTimeout()">测试API超时</button>
    <div id="result"></div>

    <script>
        async function testTimeout() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const startTime = Date.now();
                
                // 测试一个需要很长时间的请求
                const response = await fetch('http://localhost:8000/api/v1/tdpilot/analyze-requirement', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-token'
                    },
                    body: JSON.stringify({
                        title: "测试超时",
                        requirement_text: "这是一个测试超时的请求",
                        background_knowledge: "测试",
                        database_type: "tdsql-pg",
                        llm_model_id: 27
                    }),
                    signal: AbortSignal.timeout(120000) // 2分钟超时
                });
                
                const endTime = Date.now();
                const duration = (endTime - startTime) / 1000;
                
                if (response.ok) {
                    resultDiv.innerHTML = `✅ 请求成功！耗时: ${duration.toFixed(1)}秒`;
                } else {
                    resultDiv.innerHTML = `❌ 请求失败: ${response.status} ${response.statusText}，耗时: ${duration.toFixed(1)}秒`;
                }
                
            } catch (error) {
                const endTime = Date.now();
                const duration = (endTime - startTime) / 1000;
                
                if (error.name === 'TimeoutError') {
                    resultDiv.innerHTML = `⏰ 请求超时！耗时: ${duration.toFixed(1)}秒`;
                } else {
                    resultDiv.innerHTML = `❌ 请求错误: ${error.message}，耗时: ${duration.toFixed(1)}秒`;
                }
            }
        }
    </script>
</body>
</html>
