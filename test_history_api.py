#!/usr/bin/env python3
"""
测试历史记录API
"""
import requests
import json

def test_history_api():
    print("🔍 测试历史记录API...")
    
    # 1. 登录获取token
    print("\n1. 用户登录...")
    login_response = requests.post(
        'http://localhost:8000/api/v1/auth/login',
        json={
            'username': 'admin',
            'password': 'admin123'
        }
    )
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        return
    
    token = login_response.json()['access_token']
    print(f"✅ 登录成功")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # 2. 先创建一些历史记录
    print("\n2. 创建测试历史记录...")
    
    # 创建需求分析记录
    analysis_data = {
        'requirement_text': '测试历史记录功能，验证需求分析的历史记录展示',
        'background_knowledge': '这是一个历史记录功能测试',
        'database_type': 'tdsql-pg',
        'llm_model_id': 7
    }
    
    analysis_response = requests.post(
        'http://localhost:8000/api/v1/tdpilot/analyze-requirement',
        json=analysis_data,
        headers=headers,
        timeout=60
    )
    
    if analysis_response.status_code == 200:
        analysis_result = analysis_response.json()
        print(f"✅ 创建需求分析记录成功，ID: {analysis_result['id']}")
        
        # 如果需求分析成功，尝试创建测试用例记录
        if analysis_result.get('test_scenarios'):
            testcase_data = {
                'analysis_result_id': analysis_result['id'],
                'llm_model_id': 7,
                'custom_requirements': '生成历史记录测试用例'
            }
            
            testcase_response = requests.post(
                'http://localhost:8000/api/v1/tdpilot/generate-testcases',
                json=testcase_data,
                headers=headers,
                timeout=60
            )
            
            if testcase_response.status_code == 200:
                testcase_result = testcase_response.json()
                print(f"✅ 创建测试用例记录成功，ID: {testcase_result['id']}")
            else:
                print(f"⚠️ 创建测试用例记录失败: {testcase_response.status_code}")
    else:
        print(f"⚠️ 创建需求分析记录失败: {analysis_response.status_code}")
    
    # 3. 测试获取历史记录
    print("\n3. 获取历史记录...")
    history_response = requests.get(
        'http://localhost:8000/api/v1/tdpilot/history',
        headers=headers
    )
    
    if history_response.status_code != 200:
        print(f"❌ 获取历史记录失败: {history_response.status_code}")
        print(f"响应内容: {history_response.text}")
        return
    
    history_data = history_response.json()
    print(f"✅ 获取历史记录成功，共 {len(history_data)} 条记录")
    
    # 显示历史记录
    for i, record in enumerate(history_data[:5]):  # 只显示前5条
        print(f"\n📋 记录 {i+1}:")
        print(f"   - ID: {record['id']}")
        print(f"   - 类型: {record['type']}")
        print(f"   - 标题: {record['title']}")
        print(f"   - 状态: {record['status']}")
        print(f"   - 创建时间: {record['created_at']}")
        
        # 4. 测试获取历史记录详情
        print(f"\n4. 获取记录详情 (ID: {record['id']}, 类型: {record['type']})...")
        detail_response = requests.get(
            f"http://localhost:8000/api/v1/tdpilot/history/{record['type']}/{record['id']}",
            headers=headers
        )
        
        if detail_response.status_code == 200:
            detail_data = detail_response.json()
            print(f"✅ 获取详情成功")
            print(f"   - 详情字段: {list(detail_data.keys())}")
            
            if record['type'] == 'requirement_analysis':
                print(f"   - 需求描述长度: {len(detail_data.get('requirement_text', ''))}")
                print(f"   - 测试场景数量: {len(detail_data.get('test_scenarios', []))}")
            elif record['type'] == 'test_case_generation':
                print(f"   - 关联需求: {detail_data.get('analysis_title', 'N/A')}")
                print(f"   - 测试场景数量: {len(detail_data.get('test_scenarios', []))}")
        else:
            print(f"❌ 获取详情失败: {detail_response.status_code}")
        
        # 只测试第一条记录的详情
        break
    
    print(f"\n🎉 历史记录API测试完成！")

if __name__ == '__main__':
    test_history_api()
