#!/usr/bin/env python3
"""
临时修复脚本：在容器内直接修复JSON解析问题
"""

import json
import re
import sys
import os

def extract_json_from_llm_output(content):
    """
    从LLM输出中提取JSON内容
    支持多种格式：纯JSON、markdown代码块、混合内容等
    """
    if not content or not content.strip():
        return None, "内容为空"
    
    content = content.strip()
    
    # 方法1: 尝试直接解析（如果是纯JSON）
    try:
        return json.loads(content), None
    except json.JSONDecodeError:
        pass
    
    # 方法2: 提取JSON代码块
    json_patterns = [
        r'```json\s*(.*?)\s*```',  # ```json ... ```
        r'```\s*(.*?)\s*```',      # ``` ... ```
        r'`json\s*(.*?)\s*`',      # `json ... `
    ]
    
    for pattern in json_patterns:
        match = re.search(pattern, content, re.DOTALL)
        if match:
            json_content = match.group(1).strip()
            try:
                return json.loads(json_content), None
            except json.JSONDecodeError:
                continue
    
    # 方法3: 查找大括号内容
    if '{' in content and '}' in content:
        start = content.find('{')
        end = content.rfind('}') + 1
        json_part = content[start:end]
        try:
            return json.loads(json_part), None
        except json.JSONDecodeError:
            pass
    
    # 方法4: 查找方括号内容（数组）
    if '[' in content and ']' in content:
        start = content.find('[')
        end = content.rfind(']') + 1
        json_part = content[start:end]
        try:
            return json.loads(json_part), None
        except json.JSONDecodeError:
            pass
    
    return None, f"无法从内容中提取有效JSON: {content[:200]}..."

def create_fallback_test_cases(raw_content, error_msg):
    """
    创建fallback测试用例结构
    """
    return {
        "test_scenarios": [],
        "test_coverage": {},
        "raw_content": raw_content,
        "parse_error": error_msg,
        "extraction_attempted": True
    }

# 测试函数
if __name__ == "__main__":
    # 测试用例1: 带有thinking标签的内容
    test_content1 = """<think>
我需要生成测试用例...
</think>

```json
{
    "test_scenarios": [
        {
            "scenario": "用户登录测试",
            "test_cases": [
                {
                    "case_id": "TC001",
                    "name": "正常登录",
                    "description": "使用有效用户名和密码登录"
                }
            ]
        }
    ],
    "test_coverage": {
        "functional": 85,
        "security": 70
    }
}
```"""
    
    print("测试JSON提取功能...")
    result, error = extract_json_from_llm_output(test_content1)
    
    if result:
        print("✅ 提取成功!")
        print(f"测试场景数量: {len(result.get('test_scenarios', []))}")
        print(f"覆盖率信息: {result.get('test_coverage', {})}")
    else:
        print(f"❌ 提取失败: {error}")
        fallback = create_fallback_test_cases(test_content1, error)
        print(f"创建fallback结构，原始内容长度: {len(fallback['raw_content'])}")
    
    # 测试用例2: 空内容
    print("\n测试空内容...")
    result2, error2 = extract_json_from_llm_output("")
    print(f"空内容结果: {result2}, 错误: {error2}")
    
    # 测试用例3: 纯JSON
    print("\n测试纯JSON...")
    pure_json = '{"test_scenarios": [], "test_coverage": {}}'
    result3, error3 = extract_json_from_llm_output(pure_json)
    print(f"纯JSON结果: {result3 is not None}, 错误: {error3}")
