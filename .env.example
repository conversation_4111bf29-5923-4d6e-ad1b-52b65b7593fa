# 应用配置
APP_NAME=TDSQL AI Testing Platform
APP_VERSION=1.0.0
DEBUG=false
SECRET_KEY=your-secret-key-change-in-production

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=postgresql://tdsql:tdsql123@localhost:5432/tdsql_ai_testing

# Redis配置
REDIS_URL=redis://localhost:6379/0

# Milvus配置
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_COLLECTION_NAME=knowledge_base

# Neo4j配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=neo4j@123

# LLM模型配置
DEFAULT_LLM_PROVIDER=openai

# OpenAI配置
OPENAI_MODEL_NAME=gpt-4o
OPENAI_BASE_URL=https://globalai.vip/v1
OPENAI_API_KEY=sk-xb2aPaolrU0b4Upp5WrrTtr4gjvNMucR9DRQ1mLqwW1CQbJq

# Anthropic配置
ANTHROPIC_MODEL_NAME=claude-sonnet-4-20250514-thinking
ANTHROPIC_BASE_URL=https://globalai.vip/v1
ANTHROPIC_API_KEY=sk-MYSG4qlikUN8qtuiCqI0nHuPAHsEtFBlXYs5ZNuR4yls4Qyl

# Google配置
GOOGLE_MODEL_NAME=gemini-2.5-flash-thinking
GOOGLE_BASE_URL=https://globalai.vip/v1
GOOGLE_API_KEY=sk-MYSG4qlikUN8qtuiCqI0nHuPAHsEtFBlXYs5ZNuR4yls4Qyl

# DeepSeek配置
DEEPSEEK_MODEL_NAME=deepseek-reasoner
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_API_KEY=***********************************

# Ollama配置
OLLAMA_MODEL_NAME=deepseek-r1:latest
OLLAMA_BASE_URL=http://**********:11434

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_EXTENSIONS=.txt,.md,.pdf,.doc,.docx

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/1

# CORS配置
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000,http://127.0.0.1:5173
