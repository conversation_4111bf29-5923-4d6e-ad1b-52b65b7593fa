#!/usr/bin/env python3
"""
最终验证脚本：测试改进后的测试用例生成功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api/v1"

def get_auth_token():
    """获取认证token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    else:
        print(f"❌ 登录失败: {response.text}")
        return None

def test_complete_workflow():
    """测试完整的工作流程"""
    print("🧪 开始最终验证测试...")
    
    # 1. 获取认证
    print("\n1️⃣ 获取认证token...")
    headers = get_auth_token()
    if not headers:
        return False
    print("✅ 认证成功")
    
    # 2. 获取LLM模型
    print("\n2️⃣ 获取LLM模型...")
    models_response = requests.get(f"{BASE_URL}/llm/models", headers=headers)
    if models_response.status_code != 200:
        print(f"❌ 获取模型失败: {models_response.text}")
        return False
    
    models = models_response.json()
    if not models:
        print("❌ 没有可用的模型")
        return False
    
    model_id = models[0]["id"]
    print(f"✅ 使用模型: {models[0]['name']} (ID: {model_id})")
    
    # 3. 创建需求分析
    print("\n3️⃣ 创建需求分析...")
    analysis_data = {
        "title": "电商系统用户管理模块测试",
        "requirement_text": "需要测试电商系统的用户管理模块，包括用户注册、登录、个人信息管理、密码修改、账户安全等功能。系统需要支持高并发访问，确保数据安全。",
        "background_knowledge": "这是一个大型电商平台的用户管理系统，日活跃用户超过100万，需要确保系统的稳定性和安全性。",
        "database_type": "tdsql-pg",
        "llm_model_id": model_id
    }
    
    analysis_response = requests.post(f"{BASE_URL}/tdpilot/analyze-requirement", 
                                    json=analysis_data, headers=headers)
    
    if analysis_response.status_code != 200:
        print(f"❌ 需求分析失败-11: {analysis_response.text}")
        return False
    
    analysis_result = analysis_response.json()
    analysis_id = analysis_result["id"]
    print(f"✅ 需求分析完成，ID: {analysis_id}")
    
    # 4. 生成测试用例
    print("\n4️⃣ 生成测试用例...")
    testcase_data = {
        "analysis_result_id": analysis_id,
        "llm_model_id": model_id,
        "custom_requirements": "请特别关注数据安全、并发访问、性能测试和边界条件测试。确保覆盖SQL注入、XSS攻击等安全测试场景。"
    }
    
    testcase_response = requests.post(f"{BASE_URL}/tdpilot/generate-testcases", 
                                    json=testcase_data, headers=headers)
    
    if testcase_response.status_code != 200:
        print(f"❌ 测试用例生成失败: {testcase_response.text}")
        return False
    
    testcase_result = testcase_response.json()
    testcase_id = testcase_result["id"]
    print(f"✅ 测试用例生成完成，ID: {testcase_id}")
    
    # 5. 检查生成结果
    print("\n5️⃣ 检查生成结果...")
    test_scenarios = testcase_result.get("test_scenarios", [])
    print(f"📊 生成的测试场景数量: {len(test_scenarios)}")
    
    if test_scenarios:
        print("✅ 成功生成测试场景!")
        for i, scenario in enumerate(test_scenarios[:3], 1):  # 显示前3个场景
            scenario_name = scenario.get("scenario", "未知场景")
            test_cases_count = len(scenario.get("test_cases", []))
            print(f"   {i}. {scenario_name} ({test_cases_count}个测试用例)")
        
        if len(test_scenarios) > 3:
            print(f"   ... 还有 {len(test_scenarios) - 3} 个场景")
    else:
        print("⚠️ 没有生成测试场景，检查调试信息...")
        
        # 尝试获取调试信息
        debug_response = requests.get(f"{BASE_URL}/tdpilot/testcase/{testcase_id}/debug", 
                                    headers=headers)
        
        if debug_response.status_code == 200:
            debug_info = debug_response.json()
            print(f"🔍 调试信息:")
            print(f"   - 状态: {debug_info.get('status')}")
            print(f"   - 错误消息: {debug_info.get('error_message', '无')}")
            print(f"   - JSON解析错误: {debug_info.get('json_parse_error', '无')}")
            
            # 获取原始输出
            raw_response = requests.get(f"{BASE_URL}/tdpilot/testcase/{testcase_id}/raw-output", 
                                      headers=headers)
            if raw_response.status_code == 200:
                raw_data = raw_response.json()
                raw_output = raw_data.get('llm_raw_output', '')
                print(f"   - LLM原始输出长度: {len(raw_output)} 字符")
                print(f"   - 原始输出预览: {raw_output[:200]}...")
        else:
            print(f"❌ 无法获取调试信息: {debug_response.text}")
    
    # 6. 总结
    print(f"\n📋 测试总结:")
    print(f"   - 需求分析ID: {analysis_id}")
    print(f"   - 测试用例ID: {testcase_id}")
    print(f"   - 生成的测试场景: {len(test_scenarios)}")
    print(f"   - 使用的模型: {models[0]['name']}")
    
    return len(test_scenarios) > 0

if __name__ == "__main__":
    success = test_complete_workflow()
    if success:
        print("\n🎉 最终验证测试通过！测试用例生成功能工作正常。")
    else:
        print("\n❌ 最终验证测试失败，需要进一步调试。")
