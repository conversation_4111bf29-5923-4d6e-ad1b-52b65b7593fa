# 数据库AI测试智能化平台

一个基于LLM的数据库测试平台解决方案，通过TDPilot AI智能体提供智能化的测试用例生成、SQL脚本生成和执行验证服务。

## 🚀 快速开始

### 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- 8GB+ 内存
- 20GB+ 磁盘空间

### 一键启动
```bash
# 克隆项目
git clone <repository-url>
cd tdsql-ai-testing-platform

# 启动服务
./start.sh
```

### 访问地址
- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/api/v1/docs
- **Neo4j管理**: http://localhost:7474 (neo4j/password)

### 默认账号
- 用户名: `admin`
- 密码: `admin123`

### LLM模型配置
系统支持多种LLM提供商，需要在使用前进行配置：

1. **登录系统**后，进入"LLM模型配置"页面
2. **点击"配置向导"**，按步骤配置您的LLM模型
3. **支持的提供商**:
   - **OpenAI**: GPT-4, GPT-3.5-turbo等
   - **Anthropic**: Claude-3系列
   - **Google**: Gemini Pro系列
   - **DeepSeek**: DeepSeek Chat/Coder
   - **Ollama**: 本地部署模型
   - **自定义**: OpenAI兼容API

4. **配置参数**: 每个提供商需要配置模型名称、Base URL、API Key
5. **详细指南**: 查看 [LLM配置指南](docs/llm-configuration-guide.md)

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   AI智能体      │
│   React + Vite  │◄──►│   FastAPI       │◄──►│   TDPilot       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼─────┐
        │ PostgreSQL   │ │   Redis     │ │  Milvus   │
        │ (主数据库)   │ │  (缓存)     │ │ (向量库)  │
        └──────────────┘ └─────────────┘ └───────────┘
                                │
                        ┌───────▼──────┐
                        │    Neo4j     │
                        │  (知识图谱)  │
                        └──────────────┘
```

## 📋 核心功能
### 🤖 TDPilot AI智能体
一个强大的测试用例和SQL生成AI智能体，在数据库测试领域的权威专家：
- **智能需求分析** - 深度分析用户需求，提取关键测试点和场景
- **自动测试用例生成** - 生成全面的功能、性能、边界、异常测试用例
- **智能SQL脚本生成** - 生成复杂、针对性强的SQL测试脚本
- **MCP执行与优化** - 自动执行SQL，基于结果进行多轮迭代优化
- **三层记忆系统** - 短期记忆(Redis) + 长期记忆(Milvus) + 工作记忆(内存)
- **上下文感知对话** - 基于历史记忆提供连续性智能服务
- **多数据库支持** - 支持5种TDSQL数据库产品的测试生成

### 🔧 LLM模型管理
- **多提供商支持** - OpenAI、Anthropic、Google、DeepSeek、Ollama、自定义模型
- **配置向导** - 3步完成模型配置，自动填充默认参数
- **连接测试** - 配置前测试模型可用性和性能
- **智能切换** - 支持多模型备选和故障转移
- **使用监控** - 实时监控响应时间、Token使用量、成本统计

### 🗄️ 多数据库支持 (MCP服务)
- **TDSQL-PG** - 基于PostgreSQL的分布式数据库，支持PostgreSQL语法
- **TDSQL-Oracle** - PostgreSQL架构，Oracle语法兼容，用于Oracle替换
- **TDSQL2** - 基于MySQL的分布式数据库产品
- **TDSQL3** - MySQL架构全新调整的分布式数据库
- **CynosDB** - 云原生MySQL分布式数据库产品
- **智能连接管理** - 自动连接池管理、故障检测、性能监控
- **SQL执行优化** - 基于执行结果的多轮SQL优化机制

## 核心功能
- 基于LLM的数据库测试平台解决方案
- TDPilot AI智能体提供智能化的测试用例生成、SQL脚本生成和执行验证服务
- 智能需求分析、测试用例生成、SQL脚本生成、执行验证、知识库管理、上下文记忆、多用户上下文管理和记忆系统
- LLM模型可扩展、可接入新模型
- 数据库产品支持TDSQL-PG、TDSQL-Oracle、TDSQL2、TDSQL3、CynosDB
- 前端功能包括登录、模型配置、测试用例生成、SQL脚本生成、执行验证、知识库管理、上下文记忆、多用户上下文管理和记忆系统
- 后端功能包括用户管理、模型管理、SQL生成、用例生成、知识库管理、上下文记忆、知识图谱管理

### 核心流程
1. 基于用户输入的需求，与用户进行交互式写协作，对用户需求进行深度且全面的分析，进而生成全面、有深度的测试用例
2. 基于生成的测试用例，输出复杂、全面、针对性强的测试SQL
3. 最终目标：帮助用户实现数据库的高质量测试
```mermaid
flowchart TB
    A[需求输出] --> B[TDPilot需求分析]
    B --> C[生成测试用例]
    C --> D[生成测试SQL]
    D --> E[执行测试SQL]
    E --> F{执行结果判断}
    F -- 正常执行 --> G[结束]
    F -- 出现报错 --> H[反馈错误信息]
    H --> I[TDPilot优化分析]
    I --> D
```

### 核心流程详细说明

#### TDPilot Agent核心流程（大概流程，可修改）
获取到用户输入->搜索记忆->构造System Prompt让LLM对用户需求进行需求拆解和意图识别,输出相关的SQL测试点->基于输出的多个个测试点，每一个测试点去搜索用例经验库，获取经验用例，最后组装成一批用例经验用例->基于每一条经验用例去SQL经验库搜索经验SQL->基于搜索的经验用例和SQL经验构造System Prompt让LLM进行测试用例生成->基于生成的测试用例，输出复杂、全面、针对性强的测试SQL->记录记忆->返回给用户
注意：
- LLM的输入和输出都是文本格式
- System Prompt可以人为定制化

#### TDPilot需求分析流程
1. 判断用户是否有提供背景知识，如果有需要分析和解读，从中提取关键点，作为用例生成的辅助依据；
2. 用户是否提供设计文档或者设计文档链接，如果是文档内容，需要对文档内容进行全面的分析和解读，从中提取关键点，作为用例生成的辅助依据；如果是文档链接，需要调用网页搜索引擎，获取内容，然后对内容进行全面的分析和解读，从中提取关键点，作为用例生成的辅助依据；
3. 分析和解读用户输入，判断用户意图，如果用户意图太模糊，不明确，需要询问用户具体需求
4. 最后基于1，2，3的分析和解读，整合进行全面且深度的分析，输出测试方案。

#### 生成测试用例流程
1. 基于分析输出的测试方案，输出如下文本格式
```text
xxx测试方案：
xxx场景
xxxx测试点
1. 用例1
2. 用例2
xxx场景
xxxx测试点
1. 用例1
2. 用例2
....
```
2. 调用工具生成可视化的测试方案在前端展示，方便用户查看，允许用户修改
3. 等待用户确认测试方案后，再执行下一步

#### 生成测试SQL流程
1. 基于生成的测试用例，用户[修改]确认后的测试用例方案
2. 先创建测试表，基于用户的输入分析，需要哪些表类型，然后进行创建
3. 对创建的表插入数据，插入数据要使用批量函数进行批量数据插入。
4. 基于建好的表和用例，每一个用例生成至少3个测试SQL，SQL一定要复杂且针对用例性强
3. SQL不能有语法错误
4. 因为是分布式数据库，为了保证每次执行的结果是一致性的，有limit的必须要有order by
5. SQL输出的文本格式，一个完整可以执行的sql文件
```sql
/*
xxx测试方案：
1. 用例1
2. 用例2
...
*/
-- TestPoint 1: xxx用例
sql1
sql2
...
-- TestPoint 2: xxx用例
sql1
sql2
...
```

#### 执行SQL流程
1. 当TDPilot(LLM) AI Agent输出完整测试SQL后，就可以通过调用数据的MCP服务，进行sql执行
2. 获取执行的结果，判断是否有语法错误，如果有，就需要重新生成可执行的sql
3. 执行没问题后，就保存归档sql文件到sql用例目录中


## RAG知识库管理
- 基于milvus的向量数据库
- 基于milvus的文档管理
- 基于milvus的文档搜索
- 基于milvus的文档分类

## 上下文记忆
- 用户会话和记忆管理
- 多用户上下文管理和记忆系统
- 基于redis的上下文记忆
- 基于redis的会话记忆

## 知识图谱管理
- 基于neo4j的图谱管理
- 基于neo4j的图谱搜索
- 基于neo4j的图谱分类

## MCP
- 多数据库连接和SQL执行
- 基于mysql的数据库连接和SQL执行
- 基于pgsql的数据库连接和SQL执行
- 基于oracle的数据库连接和SQL执行

## 前端功能
### 登录
登录->注册|登录

### 模型配置
输入base_url、模型名称、token，验证模型可用

### SQL生成
必须便捷且好用的
- 用户选择使用的LLM模型
- 用户输入、背景知识、设计文档
- 用户上传用例文件，进行批量生成
- 有下载、拷贝、生成按钮、执行按钮、获取结果等

### 用例生成
- 用户选择使用的LLM模型
- 用户输入、背景知识、设计文档
- 生成可视化用例页面
- 有下载、拷贝、生成按钮等

### 知识库管理
- 用户上传文档
- 用户管理文档
- 用户搜索文档
- 用户查看文档

### 上下文记忆
- 用户查看会话
- 用户查看记忆
- 用户管理会话
- 用户管理记忆

### 知识图谱管理
- 用户查看图谱
- 用户管理图谱
- 用户搜索图谱
- 用户分类图谱

### MCP
- 用户查看数据库连接
- 用户管理数据库连接
- 用户执行SQL
- 用户查看SQL结果

## 📊 项目状态

### ✅ 已完成
- [x] 项目架构设计与初始化
- [x] 后端核心框架搭建 (FastAPI + SQLAlchemy)
- [x] 前端基础框架 (React + Vite + Ant Design)
- [x] 用户认证系统
- [x] LLM模型管理完整系统 (支持6种提供商)
- [x] TDPilot AI智能体核心框架
- [x] MCP服务完整实现 (SQL执行与优化)
- [x] 三层记忆管理系统 (Redis + Milvus + 内存)
- [x] 上下文感知的智能对话
- [x] Docker容器化部署
- [x] 完整API接口体系

### 🚧 开发中
- [ ] 知识库管理系统 (文档上传与检索)
- [ ] 知识图谱管理 (Neo4j集成)
- [ ] 完整的前端界面 (测试生成、SQL执行等页面)
- [ ] 性能监控与统计
- [ ] 用户权限管理

### 📋 待开发
- [ ] 批量测试处理
- [ ] 测试报告生成
- [ ] 数据备份恢复
- [ ] 多租户支持
- [ ] API限流与监控
- [ ] 自动化部署流水线

## 后端功能
### 用户管理
- 用户注册
- 用户登录
- 用户信息管理

### 模型管理
- 模型配置
- 模型列表
- 模型管理

### SQL生成
- SQL生成
- SQL执行
- SQL结果获取

### 用例生成
- 用例生成
- 用例管理
- 用例下载

### 知识库管理
- 知识库管理
- 知识库搜索
- 知识库分类

### 上下文记忆
- 上下文记忆管理
- 上下文记忆搜索
- 上下文记忆分类

### 知识图谱管理
- 知识图谱管理
- 知识图谱搜索
- 知识图谱分类

### MCP
- 数据库连接管理
- SQL执行
- SQL结果获取

## 🛠️ 开发指南

### 本地开发环境搭建

#### 后端开发
```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp ../.env.example .env
# 编辑.env文件，配置数据库连接等

# 运行数据库迁移
alembic upgrade head

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端开发
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 数据库管理

#### 创建迁移文件
```bash
cd backend
alembic revision --autogenerate -m "描述信息"
```

#### 执行迁移
```bash
alembic upgrade head
```

#### 回滚迁移
```bash
alembic downgrade -1
```

### API测试
访问 http://localhost:8000/api/v1/docs 查看和测试API接口

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: <EMAIL>
