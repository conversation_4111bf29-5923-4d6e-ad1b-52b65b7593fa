#!/usr/bin/env python3
"""
SQL脚本生成功能测试脚本
"""
import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8000"
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def login():
    """登录获取token"""
    response = requests.post(f"{BASE_URL}/api/v1/auth/login", json=TEST_USER)
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"登录失败: {response.text}")
        return None

def create_test_case(token):
    """创建测试用例"""
    headers = {"Authorization": f"Bearer {token}"}
    
    test_case_data = {
        "title": "用户管理系统测试",
        "description": "测试用户管理系统的CRUD操作",
        "requirements": "需要测试用户注册、登录、信息修改、删除等功能",
        "llm_model_id": 7
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/tdpilot/analyze-requirements", 
        json=test_case_data,
        headers=headers
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"需求分析成功，ID: {result['analysis_id']}")
        return result['analysis_id']
    else:
        print(f"需求分析失败-13: {response.text}")
        return None

def generate_test_cases(token, analysis_id):
    """生成测试用例"""
    headers = {"Authorization": f"Bearer {token}"}
    
    data = {
        "analysis_id": analysis_id,
        "llm_model_id": 7
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/tdpilot/generate-test-cases",
        json=data,
        headers=headers
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"测试用例生成成功，ID: {result['test_case_id']}")
        return result['test_case_id']
    else:
        print(f"测试用例生成失败: {response.text}")
        return None

def test_sql_generation_template(token, test_case_id):
    """测试模板SQL生成"""
    headers = {"Authorization": f"Bearer {token}"}
    
    data = {
        "test_case_id": test_case_id,
        "llm_model_id": 7,
        "database_type": "tdsql-pg",
        "title": "模板SQL生成测试",
        "use_ai_generation": False,  # 使用模板生成
        "custom_requirements": "生成PostgreSQL兼容的测试SQL脚本"
    }
    
    print("开始模板SQL生成测试...")
    response = requests.post(
        f"{BASE_URL}/api/v1/tdpilot/generate-sql",
        json=data,
        headers=headers
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"模板SQL生成成功!")
        print(f"生成ID: {result['id']}")
        print(f"状态: {result['status']}")
        print(f"测试表数量: {len(result['test_tables'])}")
        print(f"SQL脚本长度: {len(result['sql_content'])} 字符")
        print("\n--- SQL脚本预览 (前500字符) ---")
        print(result['sql_content'][:500] + "..." if len(result['sql_content']) > 500 else result['sql_content'])
        return result
    else:
        print(f"模板SQL生成失败: {response.text}")
        return None

def test_sql_generation_stream(token, test_case_id):
    """测试流式SQL生成"""
    headers = {"Authorization": f"Bearer {token}"}
    
    data = {
        "test_case_id": test_case_id,
        "llm_model_id": 7,
        "database_type": "tdsql-pg",
        "title": "流式SQL生成测试",
        "use_ai_generation": False,  # 先用模板测试
        "custom_requirements": "生成包含性能测试的SQL脚本"
    }
    
    print("\n开始流式SQL生成测试...")
    
    response = requests.post(
        f"{BASE_URL}/api/v1/tdpilot/generate-sql-stream",
        json=data,
        headers=headers,
        stream=True
    )
    
    if response.status_code == 200:
        print("流式响应开始...")
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                if line_str.startswith('data: '):
                    try:
                        data = json.loads(line_str[6:])
                        if data['type'] == 'progress':
                            print(f"进度: {data['message']}")
                        elif data['type'] == 'complete':
                            print("生成完成!")
                            result = data['data']
                            print(f"生成ID: {result['id']}")
                            print(f"状态: {result['status']}")
                            print(f"场景数: {result['scenario_count']}")
                            print(f"测试表数量: {len(result['test_tables'])}")
                            return result
                        elif data['type'] == 'error':
                            print(f"生成错误: {data['message']}")
                            return None
                    except json.JSONDecodeError:
                        continue
    else:
        print(f"流式SQL生成失败: {response.text}")
        return None

def get_sql_history(token):
    """获取SQL生成历史"""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(
        f"{BASE_URL}/api/v1/tdpilot/sql-generations",
        headers=headers
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"\n--- SQL生成历史 ---")
        print(f"总数: {result['total']}")
        for gen in result['generations'][:3]:  # 显示前3个
            print(f"ID: {gen['id']}, 标题: {gen['title']}, 状态: {gen['status']}")
        return result
    else:
        print(f"获取历史失败: {response.text}")
        return None

def main():
    """主测试函数"""
    print("=== TDSQL AI测试平台 - SQL脚本生成功能测试 ===\n")
    
    # 1. 登录
    print("1. 用户登录...")
    token = login()
    if not token:
        print("登录失败，测试终止")
        return
    print("登录成功!\n")
    
    # 2. 创建测试用例（如果需要）
    print("2. 创建测试用例...")
    analysis_id = create_test_case(token)
    if not analysis_id:
        print("需求分析失败，使用现有测试用例")
        # 这里可以手动指定一个已存在的test_case_id
        test_case_id = 16  # 使用我们刚创建的测试用例ID
    else:
        print("3. 生成测试用例...")
        test_case_id = generate_test_cases(token, analysis_id)
        if not test_case_id:
            print("测试用例生成失败，测试终止")
            return
    
    print(f"使用测试用例ID: {test_case_id}\n")
    
    # 4. 测试模板SQL生成
    print("4. 测试模板SQL生成...")
    template_result = test_sql_generation_template(token, test_case_id)
    
    # 5. 测试流式SQL生成
    print("\n5. 测试流式SQL生成...")
    stream_result = test_sql_generation_stream(token, test_case_id)
    
    # 6. 获取生成历史
    print("\n6. 获取SQL生成历史...")
    history = get_sql_history(token)
    
    print("\n=== 测试完成 ===")
    print("SQL脚本生成功能测试结果:")
    print(f"- 模板生成: {'成功' if template_result else '失败'}")
    print(f"- 流式生成: {'成功' if stream_result else '失败'}")
    print(f"- 历史查询: {'成功' if history else '失败'}")

if __name__ == "__main__":
    main()
