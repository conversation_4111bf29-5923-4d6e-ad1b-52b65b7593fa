{"name": "tdsql-ai-testing-frontend", "version": "1.0.0", "description": "TDSQL AI测试智能化平台前端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "axios": "^1.6.2", "zustand": "^4.4.7", "dayjs": "^1.11.10", "monaco-editor": "^0.45.0", "@monaco-editor/react": "^4.6.0", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "mermaid": "^10.6.1", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "prismjs": "^1.29.0", "react-syntax-highlighter": "^15.5.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5"}}