import React, { useState, useEffect } from 'react'
import {
  Card,
  List,
  Tag,
  Typography,
  Button,
  Input,
  Form,
  Modal,
  Space,
  Popconfirm,
  message,
  Collapse
} from 'antd'
import {
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  SaveOutlined,
  CloseOutlined
} from '@ant-design/icons'
import api from '../services/api'

const { Panel } = Collapse
const { Text, Paragraph } = Typography
const { TextArea } = Input

const EditableAnalysisResult = ({ analysisResult, onUpdate }) => {
  const [editingScenarios, setEditingScenarios] = useState(false)
  const [editingTestPoints, setEditingTestPoints] = useState(false)
  const [editingRecommendations, setEditingRecommendations] = useState(false)
  
  const [scenarios, setScenarios] = useState([])
  const [testPoints, setTestPoints] = useState([])
  const [recommendations, setRecommendations] = useState([])
  
  const [scenarioModalVisible, setScenarioModalVisible] = useState(false)
  const [editingScenario, setEditingScenario] = useState(null)
  const [scenarioForm] = Form.useForm()

  useEffect(() => {
    if (analysisResult) {
      // 调试信息（生产环境可移除）
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 分析结果数据结构:', analysisResult)
        console.log('📋 测试场景数据:', analysisResult.test_scenarios)
        console.log('🎯 测试点数据:', analysisResult.test_points)
        console.log('💡 建议数据:', analysisResult.recommendations)
      }

      setScenarios(analysisResult.test_scenarios || [])
      setTestPoints(analysisResult.test_points || [])
      setRecommendations(analysisResult.recommendations || [])
    }
  }, [analysisResult])

  // 从测试场景中提取所有测试点，并与独立测试点合并
  const getAllTestPoints = () => {
    const scenarioTestPoints = []
    scenarios.forEach(scenario => {
      if (scenario.test_points && Array.isArray(scenario.test_points)) {
        scenarioTestPoints.push(...scenario.test_points)
      }
    })

    // 合并场景中的测试点和独立的测试点，去重
    const allPoints = [...scenarioTestPoints, ...testPoints]
    return [...new Set(allPoints)].filter(point => point && point.trim())
  }

  // 当场景变化时，自动更新测试点列表
  useEffect(() => {
    if (editingScenarios || editingTestPoints) {
      const allPoints = getAllTestPoints()
      // 只有在编辑模式下才自动同步，避免覆盖用户的独立测试点编辑
      if (editingScenarios) {
        const scenarioPoints = []
        scenarios.forEach(scenario => {
          if (scenario.test_points && Array.isArray(scenario.test_points)) {
            scenarioPoints.push(...scenario.test_points)
          }
        })

        // 保留原有的独立测试点，添加场景中新增的测试点
        const originalIndependentPoints = analysisResult?.test_points || []
        const newScenarioPoints = scenarioPoints.filter(point =>
          !originalIndependentPoints.includes(point)
        )

        if (newScenarioPoints.length > 0) {
          const updatedTestPoints = [...testPoints, ...newScenarioPoints]
          setTestPoints([...new Set(updatedTestPoints)].filter(point => point && point.trim()))
        }
      }
    }
  }, [scenarios, editingScenarios, editingTestPoints])

  const handleSaveChanges = async () => {
    try {
      const response = await api.put(`/tdpilot/analysis/${analysisResult.id}`, {
        analysis_id: analysisResult.id,
        test_scenarios: scenarios,
        test_points: testPoints,
        recommendations: recommendations
      })

      message.success('分析结果已更新')
      onUpdate(response.data)
      
      // 退出编辑模式
      setEditingScenarios(false)
      setEditingTestPoints(false)
      setEditingRecommendations(false)
    } catch (error) {
      console.error('更新分析结果失败:', error)
      message.error('更新失败: ' + (error.response?.data?.detail || error.message))
    }
  }

  const handleAddScenario = () => {
    setEditingScenario(null)
    scenarioForm.resetFields()
    setScenarioModalVisible(true)
  }

  const handleEditScenario = (scenario, index) => {
    setEditingScenario({ ...scenario, index })
    scenarioForm.setFieldsValue({
      scenario: scenario.scenario,
      description: scenario.description,
      test_points: scenario.test_points ? scenario.test_points.join('\n') : ''
    })
    setScenarioModalVisible(true)
  }

  const handleScenarioModalOk = () => {
    scenarioForm.validateFields().then(values => {
      const newTestPoints = values.test_points ? values.test_points.split('\n').filter(p => p.trim()) : []
      const newScenario = {
        scenario: values.scenario,
        description: values.description,
        test_points: newTestPoints
      }

      let updatedScenarios
      if (editingScenario && editingScenario.index !== undefined) {
        // 编辑现有场景
        updatedScenarios = [...scenarios]
        updatedScenarios[editingScenario.index] = newScenario
      } else {
        // 添加新场景
        updatedScenarios = [...scenarios, newScenario]
      }

      setScenarios(updatedScenarios)

      // 立即同步测试点到独立测试点列表
      syncTestPointsFromScenarios(updatedScenarios)

      setScenarioModalVisible(false)
      scenarioForm.resetFields()
      setEditingScenario(null)
    })
  }

  // 从场景中同步测试点到独立测试点列表
  const syncTestPointsFromScenarios = (updatedScenarios) => {
    const scenarioTestPoints = []
    updatedScenarios.forEach(scenario => {
      if (scenario.test_points && Array.isArray(scenario.test_points)) {
        scenarioTestPoints.push(...scenario.test_points)
      }
    })

    // 获取原始的独立测试点（不在任何场景中的测试点）
    const originalScenarioPoints = []
    ;(analysisResult?.test_scenarios || []).forEach(scenario => {
      if (scenario.test_points && Array.isArray(scenario.test_points)) {
        originalScenarioPoints.push(...scenario.test_points)
      }
    })

    const originalIndependentPoints = (analysisResult?.test_points || []).filter(
      point => !originalScenarioPoints.includes(point)
    )

    // 合并独立测试点和场景中的测试点，去重
    const allPoints = [...originalIndependentPoints, ...scenarioTestPoints]
    const uniquePoints = [...new Set(allPoints)].filter(point => point && point.trim())

    setTestPoints(uniquePoints)
  }

  const handleDeleteScenario = (index) => {
    const newScenarios = scenarios.filter((_, i) => i !== index)
    setScenarios(newScenarios)

    // 同步更新测试点列表
    syncTestPointsFromScenarios(newScenarios)
  }

  const handleAddTestPoint = () => {
    const newPoint = prompt('请输入新的测试点:')
    if (newPoint && newPoint.trim()) {
      setTestPoints([...testPoints, newPoint.trim()])
    }
  }

  const handleEditTestPoint = (index) => {
    const currentPoint = testPoints[index]

    // 检查这个测试点是否来自场景
    const sourceScenarioIndex = scenarios.findIndex(scenario =>
      scenario.test_points && scenario.test_points.includes(currentPoint)
    )

    if (sourceScenarioIndex !== -1) {
      // 如果来自场景，提示用户在场景中编辑
      message.info('此测试点来自场景，请在对应的测试场景中进行编辑')
      return
    }

    const newPoint = prompt('编辑测试点:', currentPoint)
    if (newPoint !== null && newPoint.trim()) {
      const newTestPoints = [...testPoints]
      newTestPoints[index] = newPoint.trim()
      setTestPoints(newTestPoints)
    }
  }

  const handleDeleteTestPoint = (index) => {
    const pointToDelete = testPoints[index]

    // 检查这个测试点是否来自场景
    const sourceScenarioIndex = scenarios.findIndex(scenario =>
      scenario.test_points && scenario.test_points.includes(pointToDelete)
    )

    if (sourceScenarioIndex !== -1) {
      // 如果来自场景，从场景中删除
      const updatedScenarios = [...scenarios]
      updatedScenarios[sourceScenarioIndex] = {
        ...updatedScenarios[sourceScenarioIndex],
        test_points: updatedScenarios[sourceScenarioIndex].test_points.filter(
          point => point !== pointToDelete
        )
      }
      setScenarios(updatedScenarios)

      // 重新同步测试点
      syncTestPointsFromScenarios(updatedScenarios)
    } else {
      // 如果是独立的测试点，直接删除
      const newTestPoints = testPoints.filter((_, i) => i !== index)
      setTestPoints(newTestPoints)
    }
  }

  const handleAddRecommendation = () => {
    const newRecommendation = prompt('请输入新的建议:')
    if (newRecommendation && newRecommendation.trim()) {
      setRecommendations([...recommendations, newRecommendation.trim()])
    }
  }

  const handleEditRecommendation = (index) => {
    const currentRecommendation = recommendations[index]
    const newRecommendation = prompt('编辑建议:', currentRecommendation)
    if (newRecommendation !== null && newRecommendation.trim()) {
      const newRecommendations = [...recommendations]
      newRecommendations[index] = newRecommendation.trim()
      setRecommendations(newRecommendations)
    }
  }

  const handleDeleteRecommendation = (index) => {
    const newRecommendations = recommendations.filter((_, i) => i !== index)
    setRecommendations(newRecommendations)
  }

  const hasChanges = () => {
    return JSON.stringify(scenarios) !== JSON.stringify(analysisResult.test_scenarios || []) ||
           JSON.stringify(testPoints) !== JSON.stringify(analysisResult.test_points || []) ||
           JSON.stringify(recommendations) !== JSON.stringify(analysisResult.recommendations || [])
  }

  const isEditing = editingScenarios || editingTestPoints || editingRecommendations

  return (
    <div>
      {/* 编辑控制按钮 */}
      <div style={{ marginBottom: 16, textAlign: 'right' }}>
        {!isEditing ? (
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingScenarios(true)
              setEditingTestPoints(true)
              setEditingRecommendations(true)
            }}
          >
            编辑分析结果
          </Button>
        ) : (
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSaveChanges}
              disabled={!hasChanges()}
            >
              保存更改
            </Button>
            <Button
              icon={<CloseOutlined />}
              onClick={() => {
                setScenarios(analysisResult.test_scenarios || [])
                setTestPoints(analysisResult.test_points || [])
                setRecommendations(analysisResult.recommendations || [])
                setEditingScenarios(false)
                setEditingTestPoints(false)
                setEditingRecommendations(false)
              }}
            >
              取消编辑
            </Button>
          </Space>
        )}
      </div>

      <Collapse defaultActiveKey={['1', '2', '3']}>
        {/* 测试场景 */}
        <Panel 
          header={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>测试场景</span>
              {editingScenarios && (
                <Button
                  type="link"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleAddScenario()
                  }}
                >
                  添加场景
                </Button>
              )}
            </div>
          } 
          key="1"
        >
          <List
            dataSource={scenarios}
            renderItem={(scenario, index) => {
              // 调试信息（生产环境可移除）
              if (process.env.NODE_ENV === 'development') {
                console.log(`🎭 场景 ${index + 1} 数据:`, scenario)
              }
              return (
                <List.Item
                  actions={editingScenarios ? [
                    <Button
                      type="link"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => handleEditScenario(scenario, index)}
                    >
                      编辑
                    </Button>,
                    <Popconfirm
                      title="确定删除这个测试场景吗？"
                      onConfirm={() => handleDeleteScenario(index)}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button
                        type="link"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  ] : []}
                >
                  <List.Item.Meta
                    avatar={<Tag color="blue">{index + 1}</Tag>}
                    title={
                      <div>
                        <Text strong>
                          {scenario.scenario || scenario.name || scenario.title || `场景 ${index + 1}`}
                        </Text>
                      </div>
                    }
                    description={
                      <div>
                        {/* 场景描述 */}
                        <div style={{ marginBottom: 12 }}>
                          <Paragraph style={{ margin: 0 }}>
                            {scenario.description || scenario.desc || '暂无描述'}
                          </Paragraph>
                        </div>

                        {/* 测试点列表 */}
                        {scenario.test_points && Array.isArray(scenario.test_points) && scenario.test_points.length > 0 ? (
                          <div>
                            <Text strong style={{ color: '#1890ff' }}>测试点：</Text>
                            <div style={{ marginTop: 8, paddingLeft: 16 }}>
                              {scenario.test_points.map((point, idx) => (
                                <div key={idx} style={{ marginBottom: 4 }}>
                                  <Tag color="green" size="small">{idx + 1}</Tag>
                                  <Text>
                                    {typeof point === 'string'
                                      ? point
                                      : point?.name || point?.point || point?.description || JSON.stringify(point)
                                    }
                                  </Text>
                                </div>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <div>
                            <Text type="secondary" style={{ fontStyle: 'italic' }}>
                              暂无测试点
                            </Text>
                          </div>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              )
            }}
          />
        </Panel>

        {/* 测试点 */}
        <Panel
          header={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <span>测试点</span>
                {editingTestPoints && (
                  <Text type="secondary" style={{ marginLeft: 8, fontSize: '12px' }}>
                    (包含场景中的测试点)
                  </Text>
                )}
              </div>
              {editingTestPoints && (
                <Button
                  type="link"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleAddTestPoint()
                  }}
                >
                  添加测试点
                </Button>
              )}
            </div>
          }
          key="2"
        >
          <List
            dataSource={testPoints}
            renderItem={(point, index) => {
              // 确保point是字符串
              const pointText = typeof point === 'string' ? point : point?.name || point?.point || JSON.stringify(point)

              // 检查这个测试点是否来自场景
              const isFromScenario = scenarios.some(scenario =>
                scenario.test_points && Array.isArray(scenario.test_points) &&
                scenario.test_points.some(sp => {
                  const spText = typeof sp === 'string' ? sp : sp?.name || sp?.point || JSON.stringify(sp)
                  return spText === pointText
                })
              )

              // 找到包含此测试点的场景
              const sourceScenario = scenarios.find(scenario =>
                scenario.test_points && Array.isArray(scenario.test_points) &&
                scenario.test_points.some(sp => {
                  const spText = typeof sp === 'string' ? sp : sp?.name || sp?.point || JSON.stringify(sp)
                  return spText === pointText
                })
              )

              // 调试信息（生产环境可移除）
              if (process.env.NODE_ENV === 'development') {
                console.log(`🎯 测试点 ${index + 1}:`, { point, pointText, isFromScenario, sourceScenario: sourceScenario?.scenario })
              }

              return (
                <List.Item
                  actions={editingTestPoints ? [
                    <Button
                      type="link"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => handleEditTestPoint(index)}
                      title={isFromScenario ? "来自场景的测试点请在场景中编辑" : "编辑测试点"}
                    >
                      编辑
                    </Button>,
                    <Popconfirm
                      title={isFromScenario ?
                        "这个测试点来自场景，删除后会从场景中移除，确定删除吗？" :
                        "确定删除这个测试点吗？"
                      }
                      onConfirm={() => handleDeleteTestPoint(index)}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button
                        type="link"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        title={isFromScenario ? "从场景中删除此测试点" : "删除测试点"}
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  ] : []}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8, width: '100%' }}>
                    <Tag color="green">{index + 1}</Tag>
                    <Text style={{ flex: 1 }}>{pointText}</Text>
                    {isFromScenario && (
                      <Tag color="blue" size="small">
                        来自: {sourceScenario?.scenario || sourceScenario?.name || '未知场景'}
                      </Tag>
                    )}
                  </div>
                </List.Item>
              )
            }}
          />
        </Panel>

        {/* 建议 */}
        <Panel 
          header={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>建议</span>
              {editingRecommendations && (
                <Button
                  type="link"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleAddRecommendation()
                  }}
                >
                  添加建议
                </Button>
              )}
            </div>
          } 
          key="3"
        >
          <List
            dataSource={recommendations}
            renderItem={(recommendation, index) => {
              const recommendationText = typeof recommendation === 'string'
                ? recommendation
                : recommendation?.text || recommendation?.content || JSON.stringify(recommendation)

              // 调试信息（生产环境可移除）
              if (process.env.NODE_ENV === 'development') {
                console.log(`💡 建议 ${index + 1}:`, { recommendation, recommendationText })
              }

              return (
                <List.Item
                  actions={editingRecommendations ? [
                    <Button
                      type="link"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => handleEditRecommendation(index)}
                    >
                      编辑
                    </Button>,
                    <Popconfirm
                      title="确定删除这个建议吗？"
                      onConfirm={() => handleDeleteRecommendation(index)}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button
                        type="link"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  ] : []}
                >
                  <div style={{ display: 'flex', alignItems: 'flex-start', gap: 8, width: '100%' }}>
                    <Tag color="orange">{index + 1}</Tag>
                    <Text style={{ flex: 1 }}>{recommendationText}</Text>
                  </div>
                </List.Item>
              )
            }}
          />
        </Panel>
      </Collapse>

      {/* 场景编辑模态框 */}
      <Modal
        title={editingScenario ? "编辑测试场景" : "添加测试场景"}
        open={scenarioModalVisible}
        onOk={handleScenarioModalOk}
        onCancel={() => {
          setScenarioModalVisible(false)
          scenarioForm.resetFields()
          setEditingScenario(null)
        }}
        width={600}
      >
        <Form
          form={scenarioForm}
          layout="vertical"
        >
          <Form.Item
            name="scenario"
            label="场景名称"
            rules={[{ required: true, message: '请输入场景名称' }]}
          >
            <Input placeholder="请输入测试场景名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="场景描述"
            rules={[{ required: true, message: '请输入场景描述' }]}
          >
            <TextArea 
              rows={3} 
              placeholder="请输入测试场景的详细描述" 
            />
          </Form.Item>
          
          <Form.Item
            name="test_points"
            label="测试点"
            extra="每行一个测试点"
          >
            <TextArea 
              rows={4} 
              placeholder="请输入该场景下的测试点，每行一个" 
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default EditableAnalysisResult
