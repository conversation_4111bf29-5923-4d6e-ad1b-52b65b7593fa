import React, { useState, useEffect } from 'react'
import {
  Card,
  List,
  Tag,
  Typography,
  Button,
  Input,
  Form,
  Modal,
  Space,
  Popconfirm,
  message,
  Collapse
} from 'antd'
import {
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  SaveOutlined,
  CloseOutlined
} from '@ant-design/icons'
import api from '../services/api'

const { Panel } = Collapse
const { Text, Paragraph } = Typography
const { TextArea } = Input

const EditableAnalysisResult = ({ analysisResult, onUpdate }) => {
  const [editingScenarios, setEditingScenarios] = useState(false)
  const [editingTestPoints, setEditingTestPoints] = useState(false)
  const [editingRecommendations, setEditingRecommendations] = useState(false)
  
  const [scenarios, setScenarios] = useState([])
  const [testPoints, setTestPoints] = useState([])
  const [recommendations, setRecommendations] = useState([])
  
  const [scenarioModalVisible, setScenarioModalVisible] = useState(false)
  const [editingScenario, setEditingScenario] = useState(null)
  const [scenarioForm] = Form.useForm()

  useEffect(() => {
    if (analysisResult) {
      setScenarios(analysisResult.test_scenarios || [])
      setTestPoints(analysisResult.test_points || [])
      setRecommendations(analysisResult.recommendations || [])
    }
  }, [analysisResult])

  const handleSaveChanges = async () => {
    try {
      const response = await api.put(`/tdpilot/analysis/${analysisResult.id}`, {
        analysis_id: analysisResult.id,
        test_scenarios: scenarios,
        test_points: testPoints,
        recommendations: recommendations
      })

      message.success('分析结果已更新')
      onUpdate(response.data)
      
      // 退出编辑模式
      setEditingScenarios(false)
      setEditingTestPoints(false)
      setEditingRecommendations(false)
    } catch (error) {
      console.error('更新分析结果失败:', error)
      message.error('更新失败: ' + (error.response?.data?.detail || error.message))
    }
  }

  const handleAddScenario = () => {
    setEditingScenario(null)
    scenarioForm.resetFields()
    setScenarioModalVisible(true)
  }

  const handleEditScenario = (scenario, index) => {
    setEditingScenario({ ...scenario, index })
    scenarioForm.setFieldsValue({
      scenario: scenario.scenario,
      description: scenario.description,
      test_points: scenario.test_points ? scenario.test_points.join('\n') : ''
    })
    setScenarioModalVisible(true)
  }

  const handleScenarioModalOk = () => {
    scenarioForm.validateFields().then(values => {
      const newScenario = {
        scenario: values.scenario,
        description: values.description,
        test_points: values.test_points ? values.test_points.split('\n').filter(p => p.trim()) : []
      }

      if (editingScenario && editingScenario.index !== undefined) {
        // 编辑现有场景
        const newScenarios = [...scenarios]
        newScenarios[editingScenario.index] = newScenario
        setScenarios(newScenarios)
      } else {
        // 添加新场景
        setScenarios([...scenarios, newScenario])
      }

      setScenarioModalVisible(false)
      scenarioForm.resetFields()
      setEditingScenario(null)
    })
  }

  const handleDeleteScenario = (index) => {
    const newScenarios = scenarios.filter((_, i) => i !== index)
    setScenarios(newScenarios)
  }

  const handleAddTestPoint = () => {
    const newPoint = prompt('请输入新的测试点:')
    if (newPoint && newPoint.trim()) {
      setTestPoints([...testPoints, newPoint.trim()])
    }
  }

  const handleEditTestPoint = (index) => {
    const currentPoint = testPoints[index]
    const newPoint = prompt('编辑测试点:', currentPoint)
    if (newPoint !== null && newPoint.trim()) {
      const newTestPoints = [...testPoints]
      newTestPoints[index] = newPoint.trim()
      setTestPoints(newTestPoints)
    }
  }

  const handleDeleteTestPoint = (index) => {
    const newTestPoints = testPoints.filter((_, i) => i !== index)
    setTestPoints(newTestPoints)
  }

  const handleAddRecommendation = () => {
    const newRecommendation = prompt('请输入新的建议:')
    if (newRecommendation && newRecommendation.trim()) {
      setRecommendations([...recommendations, newRecommendation.trim()])
    }
  }

  const handleEditRecommendation = (index) => {
    const currentRecommendation = recommendations[index]
    const newRecommendation = prompt('编辑建议:', currentRecommendation)
    if (newRecommendation !== null && newRecommendation.trim()) {
      const newRecommendations = [...recommendations]
      newRecommendations[index] = newRecommendation.trim()
      setRecommendations(newRecommendations)
    }
  }

  const handleDeleteRecommendation = (index) => {
    const newRecommendations = recommendations.filter((_, i) => i !== index)
    setRecommendations(newRecommendations)
  }

  const hasChanges = () => {
    return JSON.stringify(scenarios) !== JSON.stringify(analysisResult.test_scenarios || []) ||
           JSON.stringify(testPoints) !== JSON.stringify(analysisResult.test_points || []) ||
           JSON.stringify(recommendations) !== JSON.stringify(analysisResult.recommendations || [])
  }

  const isEditing = editingScenarios || editingTestPoints || editingRecommendations

  return (
    <div>
      {/* 编辑控制按钮 */}
      <div style={{ marginBottom: 16, textAlign: 'right' }}>
        {!isEditing ? (
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingScenarios(true)
              setEditingTestPoints(true)
              setEditingRecommendations(true)
            }}
          >
            编辑分析结果
          </Button>
        ) : (
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSaveChanges}
              disabled={!hasChanges()}
            >
              保存更改
            </Button>
            <Button
              icon={<CloseOutlined />}
              onClick={() => {
                setScenarios(analysisResult.test_scenarios || [])
                setTestPoints(analysisResult.test_points || [])
                setRecommendations(analysisResult.recommendations || [])
                setEditingScenarios(false)
                setEditingTestPoints(false)
                setEditingRecommendations(false)
              }}
            >
              取消编辑
            </Button>
          </Space>
        )}
      </div>

      <Collapse defaultActiveKey={['1', '2', '3']}>
        {/* 测试场景 */}
        <Panel 
          header={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>测试场景</span>
              {editingScenarios && (
                <Button
                  type="link"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleAddScenario()
                  }}
                >
                  添加场景
                </Button>
              )}
            </div>
          } 
          key="1"
        >
          <List
            dataSource={scenarios}
            renderItem={(scenario, index) => (
              <List.Item
                actions={editingScenarios ? [
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => handleEditScenario(scenario, index)}
                  >
                    编辑
                  </Button>,
                  <Popconfirm
                    title="确定删除这个测试场景吗？"
                    onConfirm={() => handleDeleteScenario(index)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      type="link"
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                    >
                      删除
                    </Button>
                  </Popconfirm>
                ] : []}
              >
                <List.Item.Meta
                  avatar={<Tag color="blue">{index + 1}</Tag>}
                  title={scenario.scenario}
                  description={
                    <div>
                      <Paragraph>{scenario.description}</Paragraph>
                      {scenario.test_points && scenario.test_points.length > 0 && (
                        <div>
                          <Text strong>测试点：</Text>
                          <ul>
                            {scenario.test_points.map((point, idx) => (
                              <li key={idx}>{point}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </Panel>

        {/* 测试点 */}
        <Panel 
          header={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>测试点</span>
              {editingTestPoints && (
                <Button
                  type="link"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleAddTestPoint()
                  }}
                >
                  添加测试点
                </Button>
              )}
            </div>
          } 
          key="2"
        >
          <List
            dataSource={testPoints}
            renderItem={(point, index) => (
              <List.Item
                actions={editingTestPoints ? [
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => handleEditTestPoint(index)}
                  >
                    编辑
                  </Button>,
                  <Popconfirm
                    title="确定删除这个测试点吗？"
                    onConfirm={() => handleDeleteTestPoint(index)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      type="link"
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                    >
                      删除
                    </Button>
                  </Popconfirm>
                ] : []}
              >
                <Text>
                  <Tag color="green">{index + 1}</Tag>
                  {point}
                </Text>
              </List.Item>
            )}
          />
        </Panel>

        {/* 建议 */}
        <Panel 
          header={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>建议</span>
              {editingRecommendations && (
                <Button
                  type="link"
                  size="small"
                  icon={<PlusOutlined />}
                  onClick={(e) => {
                    e.stopPropagation()
                    handleAddRecommendation()
                  }}
                >
                  添加建议
                </Button>
              )}
            </div>
          } 
          key="3"
        >
          <List
            dataSource={recommendations}
            renderItem={(recommendation, index) => (
              <List.Item
                actions={editingRecommendations ? [
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => handleEditRecommendation(index)}
                  >
                    编辑
                  </Button>,
                  <Popconfirm
                    title="确定删除这个建议吗？"
                    onConfirm={() => handleDeleteRecommendation(index)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      type="link"
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                    >
                      删除
                    </Button>
                  </Popconfirm>
                ] : []}
              >
                <Text>
                  <Tag color="orange">{index + 1}</Tag>
                  {recommendation}
                </Text>
              </List.Item>
            )}
          />
        </Panel>
      </Collapse>

      {/* 场景编辑模态框 */}
      <Modal
        title={editingScenario ? "编辑测试场景" : "添加测试场景"}
        open={scenarioModalVisible}
        onOk={handleScenarioModalOk}
        onCancel={() => {
          setScenarioModalVisible(false)
          scenarioForm.resetFields()
          setEditingScenario(null)
        }}
        width={600}
      >
        <Form
          form={scenarioForm}
          layout="vertical"
        >
          <Form.Item
            name="scenario"
            label="场景名称"
            rules={[{ required: true, message: '请输入场景名称' }]}
          >
            <Input placeholder="请输入测试场景名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="场景描述"
            rules={[{ required: true, message: '请输入场景描述' }]}
          >
            <TextArea 
              rows={3} 
              placeholder="请输入测试场景的详细描述" 
            />
          </Form.Item>
          
          <Form.Item
            name="test_points"
            label="测试点"
            extra="每行一个测试点"
          >
            <TextArea 
              rows={4} 
              placeholder="请输入该场景下的测试点，每行一个" 
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default EditableAnalysisResult
