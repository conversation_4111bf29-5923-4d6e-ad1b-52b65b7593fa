import React, { useState, useEffect } from 'react'
import {
  Modal,
  Steps,
  Form,
  Input,
  Select,
  Button,
  Card,
  Alert,
  Typography,
  Space,
  Divider,
  message,
  Switch
} from 'antd'
import { InfoCircleOutlined, CheckCircleOutlined } from '@ant-design/icons'
import api from '../../services/api'
import LLMResponseDisplay from './LLMResponseDisplay'

const { Step } = Steps
const { Option } = Select
const { Title, Text, Paragraph } = Typography

const ModelConfigWizard = ({ visible, onCancel, onFinish, providers = [] }) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [form] = Form.useForm()
  const [selectedProvider, setSelectedProvider] = useState(null)
  const [testResult, setTestResult] = useState(null)
  const [testing, setTesting] = useState(false)

  // 根据提供商设置正确的默认max_tokens值
  const getDefaultMaxTokens = (providerName) => {
    switch (providerName) {
      case 'deepseek':
        return 8192  // DeepSeek的限制是8192
      case 'anthropic':
        return 32768  // Claude的常见限制
      case 'google':
        return 32768  // Gemini的常见限制
      case 'openai':
        return 32768 // GPT-4的常见限制
      case 'ollama':
        return 8192  // 本地模型通常较小
      default:
        return 32768  // 保守的默认值
    }
  }

  useEffect(() => {
    if (visible) {
      setCurrentStep(0)
      setSelectedProvider(null)
      setTestResult(null)
      form.resetFields()
    }
  }, [visible, form])

  const handleProviderSelect = (providerName) => {
    const provider = providers.find(p => p.name === providerName)
    console.log('DEBUG: handleProviderSelect called with:', providerName)
    console.log('DEBUG: Found provider:', provider)
    setSelectedProvider(provider)

    if (provider) {
      const fieldsToSet = {
        name: `${provider.display_name} 配置`,
        provider: provider.name,
        model_name: provider.default_model,
        base_url: provider.default_base_url
      }
      console.log('DEBUG: Setting form fields:', fieldsToSet)
      form.setFieldsValue(fieldsToSet)

      // 验证字段是否设置成功
      setTimeout(() => {
        const currentValues = form.getFieldsValue()
        console.log('DEBUG: Current form values after setting:', currentValues)
      }, 100)
    }
  }

  const handleNext = async () => {
    try {
      console.log('DEBUG: handleNext called, current step:', currentStep)
      console.log('DEBUG: Selected provider before validation:', selectedProvider)
      await form.validateFields()
      console.log('DEBUG: Form validation passed, moving to next step')
      setCurrentStep(currentStep + 1)
    } catch (error) {
      console.error('Validation failed:', error)
    }
  }

  const handlePrev = () => {
    setCurrentStep(currentStep - 1)
  }

  const handleTest = async () => {
    console.log('DEBUG: handleTest function called')
    setTestResult(null)
    let modelId = null
    try {
      console.log('DEBUG: Entering try block')
      setTesting(true)

      // 检查是否选择了提供商
      if (!selectedProvider) {
        console.error('DEBUG: No provider selected')
        message.error('请先选择一个提供商')
        setTesting(false)
        return
      }

      // 获取当前表单值（包括用户输入的内容）
      const currentValues = form.getFieldsValue()
      console.log('DEBUG: Current form values before setting defaults:', currentValues)
      console.log('DEBUG: selectedProvider.requires_api_key:', selectedProvider?.requires_api_key)
      console.log('DEBUG: API key field value:', form.getFieldValue('api_key'))

      // 直接使用 getFieldValue 获取每个字段的值
      const currentApiKey = form.getFieldValue('api_key')
      const currentName = form.getFieldValue('name')
      const currentModelName = form.getFieldValue('model_name')
      const currentBaseUrl = form.getFieldValue('base_url')
      const currentMaxTokens = form.getFieldValue('max_tokens')
      const currentTemperature = form.getFieldValue('temperature')
      const currentEnableStream = form.getFieldValue('enable_stream')

      console.log('DEBUG: Individual field values:', {
        api_key: currentApiKey,
        name: currentName,
        model_name: currentModelName,
        base_url: currentBaseUrl,
        max_tokens: currentMaxTokens,
        temperature: currentTemperature,
        enable_stream: currentEnableStream
      })

      // 确保表单字段有正确的值，但不覆盖用户已输入的值
      const defaultValues = {
        name: currentName || `${selectedProvider.display_name} 配置`,
        provider: selectedProvider.name,
        model_name: currentModelName || selectedProvider.default_model,
        base_url: currentBaseUrl || selectedProvider.default_base_url,
        api_key: currentApiKey, // 使用直接获取的API密钥值
        max_tokens: currentMaxTokens || getDefaultMaxTokens(selectedProvider.name),
        temperature: currentTemperature || 0.7,
        enable_stream: currentEnableStream !== undefined ? currentEnableStream : true
      }

      console.log('DEBUG: Setting merged values:', defaultValues)
      form.setFieldsValue(defaultValues)

      // 等待一下确保表单字段设置完成
      await new Promise(resolve => setTimeout(resolve, 100))

      // 再次检查表单值
      console.log('DEBUG: Form values after setting:', form.getFieldsValue())

      // 获取表单值
      let values
      try {
        values = await form.validateFields()
        console.log('DEBUG: Form validation successful, values:', values)
      } catch (error) {
        console.log('DEBUG: Form validation failed:', error)
        // 如果验证失败，使用默认值
        values = defaultValues
      }

      // 如果表单值为空，使用默认值
      if (!values || Object.keys(values).length === 0) {
        console.log('DEBUG: Form values empty, using default values')
        values = defaultValues
      }

      // 添加调试日志
      console.log('DEBUG: Final form values:', values)
      console.log('DEBUG: Selected provider:', selectedProvider)

      // 先创建模型配置，然后测试
      const modelConfig = {
        name: values.name,
        provider: selectedProvider.name,
        model_name: values.model_name,
        base_url: values.base_url,
        api_key: values.api_key,
        max_tokens: values.max_tokens || getDefaultMaxTokens(selectedProvider.name),
        temperature: values.temperature || 0.7,
        enable_stream: true,
        is_active: true
      }

      console.log('DEBUG: Model config to send:', modelConfig)

      // 创建模型配置
      console.log('DEBUG: About to send create model request to /llm/models')
      const createResponse = await api.post('/llm/models', modelConfig)
      console.log('DEBUG: Create model response:', createResponse)
      modelId = createResponse.data.id
      console.log('DEBUG: Created model ID:', modelId)

      // 测试模型
      console.log('DEBUG: About to send test request to /llm/models/test')
      const testResponse = await api.post('/llm/models/test', {
        model_id: modelId,
        test_prompt: "Hello, this is a test message. Please respond to confirm the connection is working."
      })
      console.log('DEBUG: Test response:', testResponse)
      console.log('DEBUG: Test response data:', testResponse.data)

      const responseData = testResponse.data

      setTestResult({
        success: responseData.success,
        response: responseData.success ? responseData.content : responseData.error,
        latency: responseData.response_time || 0,
        tokens_used: responseData.tokens_used || 0,
        model_id: modelId
      })
      setTesting(false)

    } catch (error) {
      console.error('DEBUG: Test failed with error:', error)
      console.error('DEBUG: Error response:', error.response)
      console.error('DEBUG: Error message:', error.message)
      console.error('DEBUG: Error response data:', error.response?.data)

      // 如果测试失败且已创建模型，删除该模型配置
      if (modelId) {
        try {
          await api.delete(`/llm/models/${modelId}`)
        } catch (deleteError) {
          console.error('删除失败的模型配置时出错:', deleteError)
        }
      }

      const errorMessage = error.response?.data?.detail || error.response?.data?.message || error.message || '测试失败'
      console.log('DEBUG: Final error message:', errorMessage)

      setTestResult({
        success: false,
        error: errorMessage
      })
      setTesting(false)
    }
  }

  const handleFinish = async () => {
    try {
      if (testResult?.success && testResult?.model_id) {
        // 模型已经在测试时创建，直接完成
        onFinish({
          success: true,
          model_id: testResult.model_id,
          message: '模型配置创建成功'
        })
      } else {
        throw new Error('请先测试模型连接')
      }
    } catch (error) {
      console.error('完成配置失败:', error)
      message.error(error.message || '完成配置失败')
    }
  }

  const steps = [
    {
      title: '选择提供商',
      content: (
        <div>
          <Title level={4}>选择LLM提供商</Title>
          <Paragraph type="secondary">
            请选择您要使用的LLM服务提供商。每个提供商都有不同的特点和优势。
          </Paragraph>
          
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: 16 }}>
            {providers.map(provider => (
              <Card
                key={provider.name}
                hoverable
                onClick={() => {
                  console.log('DEBUG: Card clicked for provider:', provider.name)
                  handleProviderSelect(provider.name)
                }}
                style={{
                  border: selectedProvider?.name === provider.name ? '2px solid #1890ff' : '1px solid #d9d9d9'
                }}
              >
                <Card.Meta
                  title={provider.display_name}
                  description={
                    <div>
                      <Text type="secondary">默认模型: {provider.default_model}</Text>
                      <br />
                      <Text type="secondary">
                        需要API密钥: {provider.requires_api_key ? '是' : '否'}
                      </Text>
                    </div>
                  }
                />
              </Card>
            ))}
          </div>
        </div>
      )
    },
    {
      title: '配置参数',
      content: (
        <div>
          <Title level={4}>配置模型参数</Title>
          <Paragraph type="secondary">
            请填写模型的配置参数。系统已为您预填了推荐的默认值。
          </Paragraph>

          <Form form={form} layout="vertical">
            <Form.Item
              name="name"
              label="配置名称"
              rules={[{ required: true, message: '请输入配置名称' }]}
            >
              <Input placeholder="为这个配置起个名字" />
            </Form.Item>

            <Form.Item name="provider" hidden>
              <Input />
            </Form.Item>

            <Form.Item
              name="model_name"
              label="模型名称"
              rules={[{ required: true, message: '请输入模型名称' }]}
            >
              <Input placeholder="模型标识符" />
            </Form.Item>

            <Form.Item
              name="base_url"
              label="API端点"
              rules={selectedProvider?.supports_custom_base_url ? [] : []}
            >
              <Input placeholder="API基础URL" />
            </Form.Item>

            <Form.Item
              name="api_key"
              label="API密钥"
              rules={selectedProvider?.requires_api_key ? [{ required: true, message: '请输入API密钥' }] : []}
              style={{ display: selectedProvider?.requires_api_key ? 'block' : 'none' }}
            >
              <Input.Password placeholder="请输入API密钥" />
            </Form.Item>

            <Form.Item
              name="max_tokens"
              label="最大Token数"
              initialValue={32768}
            >
              <Input type="number" placeholder="32768" />
            </Form.Item>

            <Form.Item
              name="temperature"
              label="Temperature"
              initialValue={0.7}
            >
              <Input type="number" step="0.1" min="0" max="2" placeholder="0.7" />
            </Form.Item>

            <Form.Item
              name="enable_stream"
              label="启用流式输出"
              initialValue={true}
              valuePropName="checked"
            >
              <Switch
                checkedChildren="启用"
                unCheckedChildren="禁用"
              />
            </Form.Item>
          </Form>

          {selectedProvider && (
            <Alert
              message="配置提示"
              description={
                <div>
                  <p><strong>提供商:</strong> {selectedProvider.display_name}</p>
                  <p><strong>支持的模型:</strong> {selectedProvider.models.join(', ')}</p>
                  {selectedProvider.requires_api_key && (
                    <p><strong>获取API密钥:</strong> 请访问提供商官网获取API密钥</p>
                  )}
                </div>
              }
              type="info"
              icon={<InfoCircleOutlined />}
              style={{ marginTop: 16 }}
            />
          )}
        </div>
      )
    },
    {
      title: '测试连接',
      content: (
        <div>
          <Title level={4}>测试模型连接</Title>
          <Paragraph type="secondary">
            在保存配置之前，建议先测试一下模型连接是否正常。
          </Paragraph>

          <Card>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button 
                type="primary" 
                onClick={handleTest}
                loading={testing}
                disabled={!selectedProvider}
              >
                {testing ? '测试中...' : '开始测试'}
              </Button>

              {testResult && (
                <Alert
                  message={testResult.success ? '测试成功' : '测试失败'}
                  description={
                    testResult.success ? (
                      <LLMResponseDisplay response={testResult} compact={true} />
                    ) : (
                      <p><strong>错误:</strong> {testResult.error}</p>
                    )
                  }
                  type={testResult.success ? 'success' : 'error'}
                  icon={testResult.success ? <CheckCircleOutlined /> : undefined}
                />
              )}
            </Space>
          </Card>
        </div>
      )
    }
  ]

  return (
    <Modal
      title="LLM模型配置向导"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={onCancel}>取消</Button>
            {currentStep > 0 && (
              <Button onClick={handlePrev}>上一步</Button>
            )}
            {currentStep < steps.length - 1 && (
              <Button 
                type="primary" 
                onClick={handleNext}
                disabled={!selectedProvider}
              >
                下一步
              </Button>
            )}
            {currentStep === steps.length - 1 && (
              <Button 
                type="primary" 
                onClick={handleFinish}
                disabled={!testResult?.success}
              >
                完成配置
              </Button>
            )}
          </Space>
        </div>
      }
    >
      <Steps current={currentStep} style={{ marginBottom: 24 }}>
        {steps.map(step => (
          <Step key={step.title} title={step.title} />
        ))}
      </Steps>

      <div style={{ minHeight: 400 }}>
        {steps[currentStep].content}
      </div>
    </Modal>
  )
}

export default ModelConfigWizard
