import React from 'react'
import { Card, Divider, Tag, Space } from 'antd'

const LLMResponseDisplay = ({ response, showMetrics = true, compact = false }) => {
  if (!response) return null

  const cardStyle = compact ? { fontSize: 12 } : {}
  const contentStyle = compact ? { padding: 8 } : { padding: 12 }

  return (
    <div style={cardStyle}>
      {/* 思考内容 */}
      {response.reasoning_content && (
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8, fontWeight: 'bold', color: '#1890ff' }}>
            🤔 思考过程
          </div>
          <div style={{ 
            background: '#f0f8ff', 
            border: '1px solid #d6e4ff',
            ...contentStyle,
            borderRadius: 4,
            whiteSpace: 'pre-wrap',
            fontSize: compact ? 11 : 13,
            color: '#666'
          }}>
            {response.reasoning_content}
          </div>
        </div>
      )}

      {/* 主要内容 */}
      <div style={{ marginBottom: 16 }}>
        <div style={{ marginBottom: 8, fontWeight: 'bold', color: '#52c41a' }}>
          💬 响应内容
        </div>
        <div style={{ 
          background: '#f6ffed', 
          border: '1px solid #b7eb8f',
          ...contentStyle,
          borderRadius: 4,
          whiteSpace: 'pre-wrap',
          fontSize: compact ? 12 : 14
        }}>
          {response.content}
        </div>
      </div>

      {/* 工具调用 */}
      {response.tool_calls && response.tool_calls.length > 0 && (
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8, fontWeight: 'bold', color: '#fa8c16' }}>
            🔧 工具调用
          </div>
          <div style={{ 
            background: '#fff7e6', 
            border: '1px solid #ffd591',
            ...contentStyle,
            borderRadius: 4
          }}>
            {response.tool_calls.map((call, index) => (
              <div key={index} style={{ marginBottom: index < response.tool_calls.length - 1 ? 8 : 0 }}>
                <Tag color="orange" style={{ marginBottom: 4 }}>
                  {call.function?.name || call.name}
                </Tag>
                <div style={{ 
                  fontSize: compact ? 11 : 12, 
                  color: '#666',
                  fontFamily: 'monospace',
                  background: '#fff',
                  padding: 4,
                  borderRadius: 2,
                  border: '1px solid #f0f0f0'
                }}>
                  {call.function?.arguments || call.arguments}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 指标信息 */}
      {showMetrics && (
        <div>
          <Divider style={{ margin: compact ? '8px 0' : '16px 0' }} />
          <Space wrap size={compact ? 'small' : 'middle'}>
            <Tag color="blue">
              ⏱️ {response.response_time?.toFixed(2)}s
            </Tag>
            <Tag color="green">
              📥 输入: {response.prompt_tokens || 0}
            </Tag>
            <Tag color="orange">
              📤 输出: {response.completion_tokens || 0}
            </Tag>
            <Tag color="purple">
              📊 总计: {response.total_tokens || 0}
            </Tag>
            {response.model && (
              <Tag color="geekblue">
                🤖 {response.model}
              </Tag>
            )}
          </Space>
        </div>
      )}
    </div>
  )
}

export default LLMResponseDisplay
