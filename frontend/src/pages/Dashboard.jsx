import React, { useState, useEffect } from 'react'
import { Row, Col, Card, Statistic, Typography, List, Tag, Progress, Button } from 'antd'
import {
  DatabaseOutlined,
  FileTextOutlined,
  CodeOutlined,
  BulbOutlined,
  RocketOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import EChartsReact from 'echarts-for-react'

const { Title, Text } = Typography

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalProjects: 12,
    testCasesGenerated: 156,
    sqlScriptsGenerated: 89,
    successRate: 94.5
  })

  const [recentActivities, setRecentActivities] = useState([
    {
      id: 1,
      type: 'test_generation',
      title: '用户管理系统测试用例生成',
      status: 'completed',
      time: '2小时前'
    },
    {
      id: 2,
      type: 'sql_generation',
      title: 'CRUD操作SQL脚本生成',
      status: 'completed',
      time: '3小时前'
    },
    {
      id: 3,
      type: 'requirement_analysis',
      title: '订单系统需求分析',
      status: 'in_progress',
      time: '5小时前'
    }
  ])

  // 图表配置
  const chartOption = {
    title: {
      text: '最近7天生成统计',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['测试用例', 'SQL脚本'],
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '测试用例',
        type: 'line',
        data: [12, 19, 15, 22, 18, 25, 20],
        smooth: true
      },
      {
        name: 'SQL脚本',
        type: 'line',
        data: [8, 12, 10, 15, 12, 18, 14],
        smooth: true
      }
    ]
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />
      case 'in_progress':
        return <ClockCircleOutlined style={{ color: '#1890ff' }} />
      default:
        return <ClockCircleOutlined />
    }
  }

  const getStatusTag = (status) => {
    switch (status) {
      case 'completed':
        return <Tag color="success">已完成</Tag>
      case 'in_progress':
        return <Tag color="processing">进行中</Tag>
      default:
        return <Tag>未知</Tag>
    }
  }

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        仪表板
      </Title>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总项目数"
              value={stats.totalProjects}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="测试用例生成"
              value={stats.testCasesGenerated}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="SQL脚本生成"
              value={stats.sqlScriptsGenerated}
              prefix={<CodeOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="成功率"
              value={stats.successRate}
              suffix="%"
              prefix={<RocketOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 生成趋势图表 */}
        <Col xs={24} lg={16}>
          <Card title="生成趋势" style={{ height: 400 }}>
            <EChartsReact 
              option={chartOption} 
              style={{ height: 300 }}
            />
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={8}>
          <Card 
            title="最近活动" 
            style={{ height: 400 }}
            extra={<Button type="link">查看全部</Button>}
          >
            <List
              dataSource={recentActivities}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={getStatusIcon(item.status)}
                    title={
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text ellipsis style={{ maxWidth: 150 }}>
                          {item.title}
                        </Text>
                        {getStatusTag(item.status)}
                      </div>
                    }
                    description={
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {item.time}
                      </Text>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="快速操作">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <Card 
                  hoverable
                  style={{ textAlign: 'center' }}
                  bodyStyle={{ padding: '24px 16px' }}
                >
                  <FileTextOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 16 }} />
                  <div>
                    <Title level={4} style={{ margin: 0 }}>生成测试用例</Title>
                    <Text type="secondary">基于需求智能生成测试用例</Text>
                  </div>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card 
                  hoverable
                  style={{ textAlign: 'center' }}
                  bodyStyle={{ padding: '24px 16px' }}
                >
                  <CodeOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 16 }} />
                  <div>
                    <Title level={4} style={{ margin: 0 }}>生成SQL脚本</Title>
                    <Text type="secondary">自动生成复杂SQL测试脚本</Text>
                  </div>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card 
                  hoverable
                  style={{ textAlign: 'center' }}
                  bodyStyle={{ padding: '24px 16px' }}
                >
                  <DatabaseOutlined style={{ fontSize: 32, color: '#722ed1', marginBottom: 16 }} />
                  <div>
                    <Title level={4} style={{ margin: 0 }}>数据库连接</Title>
                    <Text type="secondary">管理多数据库连接配置</Text>
                  </div>
                </Card>
              </Col>
              <Col xs={24} sm={12} md={6}>
                <Card 
                  hoverable
                  style={{ textAlign: 'center' }}
                  bodyStyle={{ padding: '24px 16px' }}
                >
                  <BulbOutlined style={{ fontSize: 32, color: '#fa8c16', marginBottom: 16 }} />
                  <div>
                    <Title level={4} style={{ margin: 0 }}>知识库管理</Title>
                    <Text type="secondary">管理测试知识和经验</Text>
                  </div>
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
