import React, { useState, useEffect, useRef } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import {
  Card,
  Typography,
  Button,
  Space,
  Divider,
  Tag,
  Alert,
  Spin
} from 'antd'
import { ArrowLeftOutlined, StopOutlined } from '@ant-design/icons'
import api from '../services/api'

const { Title, Text, Paragraph } = Typography

const StreamingTest = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const { model, testPrompt } = location.state || {}
  
  const [isStreaming, setIsStreaming] = useState(false)
  const [content, setContent] = useState('')
  const [reasoningContent, setReasoningContent] = useState('')
  const [error, setError] = useState(null)
  const [metrics, setMetrics] = useState({
    startTime: null,
    endTime: null,
    responseTime: 0,
    promptTokens: 0,
    completionTokens: 0,
    totalTokens: 0,
    model: ''
  })
  const [toolCalls, setToolCalls] = useState([])
  
  const abortControllerRef = useRef(null)
  const contentRef = useRef(null)

  useEffect(() => {
    if (!model || !testPrompt) {
      navigate('/model-config')
      return
    }
    
    startStreamingTest()
    
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [model, testPrompt, navigate])

  // 自动滚动到底部
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight
    }
  }, [content, reasoningContent])

  const startStreamingTest = async () => {
    setIsStreaming(true)
    setError(null)
    setContent('')
    setReasoningContent('')
    setMetrics(prev => ({ ...prev, startTime: Date.now() }))
    
    try {
      abortControllerRef.current = new AbortController()
      
      const response = await fetch(`${api.defaults.baseURL}/llm/models/test-stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${JSON.parse(localStorage.getItem('auth-storage'))?.state?.token}`
        },
        body: JSON.stringify({
          model_id: model.id,
          test_prompt: testPrompt
        }),
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          setMetrics(prev => ({ 
            ...prev, 
            endTime: Date.now(),
            responseTime: (Date.now() - prev.startTime) / 1000
          }))
          setIsStreaming(false)
          break
        }

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split('\n')
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              
              if (data.type === 'content') {
                setContent(prev => prev + data.content)
              } else if (data.type === 'reasoning') {
                setReasoningContent(prev => prev + data.content)
              } else if (data.type === 'tool_call') {
                setToolCalls(prev => [...prev, data.tool_call])
              } else if (data.type === 'metrics') {
                setMetrics(prev => ({
                  ...prev,
                  promptTokens: data.prompt_tokens || 0,
                  completionTokens: data.completion_tokens || 0,
                  totalTokens: data.total_tokens || 0,
                  model: data.model || ''
                }))
              } else if (data.type === 'error') {
                setError(data.error)
                setIsStreaming(false)
              }
            } catch (e) {
              console.warn('Failed to parse SSE data:', line)
            }
          }
        }
      }
    } catch (err) {
      if (err.name !== 'AbortError') {
        setError(err.message)
      }
      setIsStreaming(false)
    }
  }

  const stopStreaming = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    setIsStreaming(false)
  }

  const goBack = () => {
    navigate('/model-config')
  }

  if (!model || !testPrompt) {
    return null
  }

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button icon={<ArrowLeftOutlined />} onClick={goBack}>
            返回模型配置
          </Button>
          <Title level={3} style={{ margin: 0 }}>
            流式测试: {model.name}
          </Title>
          {isStreaming && (
            <Button 
              danger 
              icon={<StopOutlined />} 
              onClick={stopStreaming}
            >
              停止测试
            </Button>
          )}
        </Space>
      </div>

      {/* 测试信息 */}
      <Card title="测试信息" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>模型: </Text>
            <Tag color="blue">{model.provider}</Tag>
            <Text>{model.model_name}</Text>
          </div>
          <div>
            <Text strong>测试提示: </Text>
            <Paragraph 
              style={{ 
                background: '#f5f5f5', 
                padding: '8px', 
                borderRadius: '4px',
                marginTop: '8px'
              }}
            >
              {testPrompt}
            </Paragraph>
          </div>
        </Space>
      </Card>

      {/* 错误信息 */}
      {error && (
        <Alert
          message="测试失败"
          description={error}
          type="error"
          style={{ marginBottom: '16px' }}
          showIcon
        />
      )}

      {/* 思考内容 */}
      {reasoningContent && (
        <Card 
          title={
            <Space>
              <span>🤔 思考过程</span>
              {isStreaming && <Spin size="small" />}
            </Space>
          }
          style={{ marginBottom: '16px' }}
        >
          <div
            ref={contentRef}
            style={{
              maxHeight: '200px',
              overflow: 'auto',
              whiteSpace: 'pre-wrap',
              fontFamily: 'monospace',
              fontSize: '14px',
              lineHeight: '1.5'
            }}
          >
            {reasoningContent}
            {isStreaming && <span className="blinking-cursor">|</span>}
          </div>
        </Card>
      )}

      {/* 主要回复 */}
      <Card 
        title={
          <Space>
            <span>💬 回复内容</span>
            {isStreaming && <Spin size="small" />}
          </Space>
        }
        style={{ marginBottom: '16px' }}
      >
        <div
          ref={contentRef}
          style={{
            minHeight: '200px',
            maxHeight: '400px',
            overflow: 'auto',
            whiteSpace: 'pre-wrap',
            fontSize: '14px',
            lineHeight: '1.6'
          }}
        >
          {content || (isStreaming ? '正在生成回复...' : '暂无内容')}
          {isStreaming && <span className="blinking-cursor">|</span>}
        </div>
      </Card>

      {/* 工具调用 */}
      {toolCalls.length > 0 && (
        <Card title="🔧 工具调用" style={{ marginBottom: '16px' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            {toolCalls.map((call, index) => (
              <div key={index} style={{ 
                background: '#f0f2f5', 
                padding: '12px', 
                borderRadius: '6px' 
              }}>
                <div><Text strong>函数:</Text> {call.function?.name}</div>
                <div><Text strong>参数:</Text></div>
                <pre style={{ 
                  background: '#fff', 
                  padding: '8px', 
                  borderRadius: '4px',
                  margin: '4px 0 0 0',
                  fontSize: '12px'
                }}>
                  {call.function?.arguments}
                </pre>
              </div>
            ))}
          </Space>
        </Card>
      )}

      {/* 性能指标 */}
      <Card title="📊 性能指标">
        <Space wrap>
          <Tag color="blue">
            ⏱️ 响应时间: {metrics.responseTime.toFixed(2)}s
          </Tag>
          <Tag color="green">
            📥 输入Token: {metrics.promptTokens}
          </Tag>
          <Tag color="orange">
            📤 输出Token: {metrics.completionTokens}
          </Tag>
          <Tag color="purple">
            📊 总Token: {metrics.totalTokens}
          </Tag>
          {metrics.model && (
            <Tag color="geekblue">
              🤖 {metrics.model}
            </Tag>
          )}
          <Tag color={isStreaming ? 'processing' : 'success'}>
            {isStreaming ? '🔄 流式输出中...' : '✅ 完成'}
          </Tag>
        </Space>
      </Card>

      <style jsx>{`
        .blinking-cursor {
          animation: blink 1s infinite;
          color: #1890ff;
          font-weight: bold;
        }
        
        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }
      `}</style>
    </div>
  )
}

export default StreamingTest
