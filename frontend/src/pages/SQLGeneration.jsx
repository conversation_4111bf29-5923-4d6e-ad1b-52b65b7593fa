import React, { useState, useEffect } from 'react'
import {
  Typo<PERSON>, Card, Form, Select, Button, Input, Switch, Space,
  Steps, Row, Col, Tabs, message, Spin, Alert, Divider, Tag,
  Modal, Tooltip, Progress, List, Statistic
} from 'antd'
import {
  CodeOutlined, PlayCircleOutlined, DownloadOutlined,
  HistoryOutlined, SettingOutlined, FileTextOutlined,
  LoadingOutlined, CheckCircleOutlined, ExclamationCircleOutlined,
  RobotOutlined, FileSearchOutlined, DatabaseOutlined, CloseOutlined
} from '@ant-design/icons'
import { Editor } from '@monaco-editor/react'
import api from '../services/api'

const { Title, Text, Paragraph } = Typography
const { TextArea } = Input
const { Step } = Steps
const { TabPane } = Tabs

const SQLGeneration = () => {
  // 状态管理
  const [currentStep, setCurrentStep] = useState(0)
  const [loading, setLoading] = useState(false)
  const [progressMessage, setProgressMessage] = useState('')
  const [activeTab, setActiveTab] = useState('generation')

  // 实时输出状态
  const [llmOutput, setLlmOutput] = useState('')
  const [showLlmOutput, setShowLlmOutput] = useState(false)

  // 表单和数据
  const [form] = Form.useForm()
  const [testCases, setTestCases] = useState([])
  const [models, setModels] = useState([])
  const [selectedTestCase, setSelectedTestCase] = useState(null)
  const [sqlResult, setSqlResult] = useState(null)
  const [generationHistory, setGenerationHistory] = useState([])

  // 配置状态
  const [useAiGeneration, setUseAiGeneration] = useState(true)
  const [previewMode, setPreviewMode] = useState(false)

  // 编辑器配置
  const [editorOptions] = useState({
    theme: 'vs-dark',
    language: 'sql',
    minimap: { enabled: false },
    fontSize: 14,
    wordWrap: 'on',
    automaticLayout: true
  })

  // 初始化数据
  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = async () => {
    try {
      // 加载LLM模型列表
      const modelsResponse = await api.get('/llm/models')
      setModels(modelsResponse.data || [])

      // 加载测试用例列表
      const testCasesResponse = await api.get('/tdpilot/test-cases')
      setTestCases(testCasesResponse.data.test_cases || [])

      // 加载生成历史
      const historyResponse = await api.get('/tdpilot/sql-generations')
      setGenerationHistory(historyResponse.data.generations || [])

    } catch (error) {
      console.error('初始化数据加载失败:', error)
      message.error('数据加载失败，请刷新页面重试')
    }
  }

  // 获取认证token
  const getAuthToken = () => {
    const authData = localStorage.getItem('auth-storage')
    if (authData) {
      try {
        const { state } = JSON.parse(authData)
        return state.token
      } catch (error) {
        console.error('Parse auth data error:', error)
      }
    }
    return null
  }

  // 处理流式响应
  const handleStreamResponse = async (url, data, onComplete) => {
    try {
      setLoading(true)
      setProgressMessage('正在连接服务器...')
      setLlmOutput('')
      setShowLlmOutput(true)

      const token = getAuthToken()
      const headers = {
        'Content-Type': 'application/json'
      }

      if (token) {
        headers.Authorization = `Bearer ${token}`
      }

      const response = await fetch(`/api${url}`, {
        method: 'POST',
        headers,
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))

              if (data.type === 'progress') {
                setProgressMessage(data.message)
              } else if (data.type === 'llm_reasoning') {
                // 处理思考过程 - 累积显示
                setLlmOutput(prev => {
                  // 如果是第一个思考内容，添加标题
                  if (!prev.includes('[思考]')) {
                    return prev + '\n[思考] ' + data.content
                  }
                  return prev + data.content
                })
              } else if (data.type === 'llm_output') {
                // 处理LLM实际输出 - 直接拼接，不强制换行
                setLlmOutput(prev => {
                  // 如果之前有思考内容，先换行分隔
                  if (prev.includes('[思考]') && !prev.endsWith('\n\n')) {
                    return prev + '\n\n' + data.content
                  }
                  // 直接拼接token，让它们在同一行连续显示
                  return prev + data.content
                })
              } else if (data.type === 'complete') {
                onComplete(data.data)
                setProgressMessage('生成完成')
              } else if (data.type === 'error') {
                throw new Error(data.message)
              }
            } catch (e) {
              console.error('解析流式数据失败:', e)
            }
          }
        }
      }
    } catch (error) {
      console.error('流式请求失败:', error)
      message.error('请求失败: ' + error.message)
    } finally {
      setLoading(false)
      setProgressMessage('')
    }
  }

  // SQL生成处理
  const handleSqlGeneration = async (values) => {
    if (!selectedTestCase) {
      message.error('请先选择测试用例')
      return
    }

    const requestData = {
      test_case_id: selectedTestCase.id,
      llm_model_id: values.llm_model_id,
      database_type: values.database_type,
      title: values.title || `SQL脚本生成 - ${selectedTestCase.title}`,
      custom_requirements: values.custom_requirements,
      use_ai_generation: useAiGeneration
    }

    await handleStreamResponse('/v1/tdpilot/generate-sql-stream', requestData, (data) => {
      setSqlResult(data)
      setCurrentStep(2)
      message.success('SQL脚本生成完成')

      // 刷新历史记录
      loadInitialData()
    })
  }

  // 重置表单
  const handleReset = () => {
    setCurrentStep(0)
    setSelectedTestCase(null)
    setSqlResult(null)
    form.resetFields()
  }

  // 查看历史记录
  const handleViewHistory = (record) => {
    console.log('查看历史记录:', record)  // 调试信息
    setSqlResult(record)
    setCurrentStep(2)
    setActiveTab('generation')  // 切换到SQL生成标签页
    message.success('已加载历史记录')
  }

  // 删除历史记录
  const handleDeleteHistory = async (recordId) => {
    try {
      await api.delete(`/tdpilot/sql-generations/${recordId}`)
      message.success('删除成功')
      // 刷新历史记录
      loadInitialData()
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败，请重试')
    }
  }

  // 下载SQL脚本
  const handleDownload = (record) => {
    // 如果没有传入record，使用当前的sqlResult
    const targetRecord = record || sqlResult

    console.log('下载函数调用，传入的record:', record)
    console.log('使用的targetRecord:', targetRecord)
    console.log('当前sqlResult:', sqlResult)

    if (!targetRecord) {
      message.error('没有可下载的SQL脚本')
      return
    }

    // 检查SQL内容是否存在 - 尝试多个可能的字段名
    let sqlContent = targetRecord.sql_content || targetRecord.sqlContent || targetRecord.content || targetRecord.sql

    console.log('SQL内容字段检查:')
    console.log('targetRecord.sql_content:', targetRecord.sql_content)
    console.log('targetRecord.sqlContent:', targetRecord.sqlContent)
    console.log('targetRecord.content:', targetRecord.content)
    console.log('targetRecord.sql:', targetRecord.sql)
    console.log('最终使用的sqlContent:', sqlContent)

    if (!sqlContent || sqlContent === 'undefined' || sqlContent.trim() === '') {
      message.error('SQL脚本内容为空，无法下载')
      console.error('SQL内容为空，完整记录:', targetRecord)
      console.error('记录的所有字段:', Object.keys(targetRecord))
      return
    }

    try {
      const blob = new Blob([sqlContent], { type: 'text/sql' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `sql_script_${targetRecord.id || Date.now()}.sql`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      message.success('SQL脚本已下载')
    } catch (error) {
      console.error('下载失败:', error)
      message.error('下载失败，请重试')
    }
  }

  // 渲染测试用例选择步骤
  const renderTestCaseSelection = () => (
    <Card title="选择测试用例" style={{ marginBottom: 24 }}>
      <Row gutter={[16, 16]}>
        {testCases.map(testCase => (
          <Col xs={24} sm={12} md={8} key={testCase.id}>
            <Card
              hoverable
              size="small"
              className={selectedTestCase?.id === testCase.id ? 'selected-card' : ''}
              onClick={() => setSelectedTestCase(testCase)}
              style={{
                border: selectedTestCase?.id === testCase.id ? '2px solid #1890ff' : '1px solid #d9d9d9'
              }}
            >
              <div style={{ marginBottom: 8 }}>
                <Text strong>{testCase.title}</Text>
                <Tag color={testCase.status === 'completed' ? 'green' : 'orange'} style={{ float: 'right' }}>
                  {testCase.status}
                </Tag>
              </div>
              <Paragraph ellipsis={{ rows: 2 }} style={{ margin: 0, color: '#666' }}>
                {testCase.description || '暂无描述'}
              </Paragraph>
              <div style={{ marginTop: 8, fontSize: '12px', color: '#999' }}>
                场景数: {testCase.test_scenarios?.length || 0} |
                创建时间: {new Date(testCase.created_at).toLocaleDateString()}
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      {testCases.length === 0 && (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <FileTextOutlined style={{ fontSize: 48, color: '#ccc', marginBottom: 16 }} />
          <div>暂无可用的测试用例</div>
          <div style={{ color: '#999', marginTop: 8 }}>
            请先在"测试用例生成"页面创建测试用例
          </div>
        </div>
      )}

      <div style={{ marginTop: 24, textAlign: 'right' }}>
        <Button
          type="primary"
          disabled={!selectedTestCase}
          onClick={() => setCurrentStep(1)}
        >
          下一步：配置生成参数
        </Button>
      </div>
    </Card>
  )

  // 渲染配置步骤
  const renderConfigurationStep = () => (
    <Card title="配置SQL生成参数" style={{ marginBottom: 24 }}>
      {selectedTestCase && (
        <Alert
          message={`已选择测试用例: ${selectedTestCase.title}`}
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSqlGeneration}
        initialValues={{
          database_type: 'tdsql-pg',
          llm_model_id: 7,
          use_ai_generation: true
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="database_type"
              label="目标数据库类型"
              rules={[{ required: true, message: '请选择数据库类型' }]}
            >
              <Select>
                <Select.Option value="tdsql-pg">TDSQL-PostgreSQL</Select.Option>
                <Select.Option value="tdsql-oracle">TDSQL-Oracle</Select.Option>
                <Select.Option value="tdsql2">TDSQL2</Select.Option>
                <Select.Option value="tdsql3">TDSQL3</Select.Option>
                <Select.Option value="cynosdb">CynosDB</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="llm_model_id"
              label="AI模型选择"
              rules={[{ required: true, message: '请选择AI模型' }]}
            >
              <Select placeholder="选择用于生成SQL的AI模型">
                {models.map(model => (
                  <Select.Option key={model.id} value={model.id}>
                    {model.model_name} ({model.provider})
                    {model.id === 7 && <span style={{color: '#52c41a'}}> (推荐)</span>}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="title"
          label="生成任务标题"
        >
          <Input placeholder="为本次SQL生成任务命名（可选）" />
        </Form.Item>

        <Form.Item label="生成模式">
          <Space>
            <Switch
              checked={useAiGeneration}
              onChange={setUseAiGeneration}
              checkedChildren={<RobotOutlined />}
              unCheckedChildren={<FileSearchOutlined />}
            />
            <Text>
              {useAiGeneration ? 'AI智能生成' : '模板生成'}
            </Text>
            <Tooltip title={useAiGeneration ?
              '使用AI模型根据测试用例智能生成SQL脚本，更灵活但耗时较长' :
              '使用预定义模板快速生成SQL脚本，速度快但灵活性有限'
            }>
              <ExclamationCircleOutlined style={{ color: '#999' }} />
            </Tooltip>
          </Space>
        </Form.Item>

        <Form.Item
          name="custom_requirements"
          label="自定义需求（可选）"
        >
          <TextArea
            rows={4}
            placeholder="如有特殊的SQL生成需求或约束条件，请在此说明..."
          />
        </Form.Item>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Space>
            <Button onClick={() => setCurrentStep(0)}>
              上一步
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<CodeOutlined />}
            >
              {loading ? progressMessage : '开始生成SQL脚本'}
            </Button>
          </Space>
        </div>
      </Form>
    </Card>
  )

  // 格式化LLM输出内容
  const formatLlmOutput = (content) => {
    if (!content) return '等待LLM输出...'

    // 检查是否包含思考内容
    if (content.includes('[思考]')) {
      // 按行处理内容，保持原始格式
      const lines = content.split('\n')
      const formattedParts = []
      let currentThinkingBlock = []
      let currentOutputBlock = []
      let inThinkingMode = false

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]

        if (line.startsWith('[思考]')) {
          // 如果之前有输出内容，先处理它
          if (currentOutputBlock.length > 0) {
            const outputContent = currentOutputBlock.join('\n')
            formattedParts.push(
              <div key={`output-${i}`} style={{
                color: '#d4d4d4',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                margin: '4px 0',
                fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
              }}>
                {outputContent}
              </div>
            )
            currentOutputBlock = []
          }

          // 开始思考模式
          inThinkingMode = true
          formattedParts.push(
            <div key={`thinking-${i}`} style={{
              color: '#ffd700',
              fontWeight: 'bold',
              marginTop: '8px',
              marginBottom: '4px',
              borderLeft: '3px solid #ffd700',
              backgroundColor: 'rgba(255, 215, 0, 0.1)',
              borderRadius: '4px',
              padding: '6px 8px'
            }}>
              💭 {line.replace('[思考]', '').trim()}
            </div>
          )
        } else if (inThinkingMode && line.trim() === '') {
          // 空行可能表示思考结束
          inThinkingMode = false
        } else if (inThinkingMode) {
          // 思考内容
          if (line.trim()) {
            formattedParts.push(
              <div key={`thinking-content-${i}`} style={{
                color: '#d4d4d4',
                lineHeight: '1.6',
                margin: '2px 0',
                paddingLeft: '12px',
                fontStyle: 'italic'
              }}>
                {line.trim()}
              </div>
            )
          }
        } else {
          // 普通输出内容，累积到输出块中
          currentOutputBlock.push(line)
        }
      }

      // 处理最后的输出块
      if (currentOutputBlock.length > 0) {
        const outputContent = currentOutputBlock.join('\n')
        formattedParts.push(
          <div key="final-output" style={{
            color: '#d4d4d4',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            margin: '4px 0',
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
          }}>
            {outputContent}
          </div>
        )
      }

      return (
        <div style={{
          color: '#d4d4d4',
          lineHeight: '1.6',
          margin: '2px 0'
        }}>
          {formattedParts}
        </div>
      )
    } else {
      // 没有思考内容，直接显示为连续文本，保持换行
      return (
        <div style={{
          color: '#d4d4d4',
          lineHeight: '1.6',
          margin: '2px 0',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace'
        }}>
          {content}
        </div>
      )
    }
  }

  // 渲染LLM实时输出窗口
  const renderLlmOutputWindow = () => (
    showLlmOutput && (
      <Card
        title={
          <Space>
            <RobotOutlined style={{ color: '#52c41a' }} />
            <span style={{ color: '#52c41a', fontWeight: 'bold' }}>🤖 AI思维过程</span>
            <Button
              type="text"
              size="small"
              icon={<CloseOutlined />}
              onClick={() => setShowLlmOutput(false)}
              style={{ marginLeft: 'auto' }}
            />
          </Space>
        }
        style={{ marginBottom: 24 }}
        styles={{
          body: { padding: 0 },
          header: { backgroundColor: '#f6ffed', borderBottom: '1px solid #b7eb8f' }
        }}
      >
        <div style={{
          height: '350px',
          overflow: 'auto',
          backgroundColor: '#1a1a1a',
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          fontSize: '13px',
          lineHeight: '1.5',
          padding: '16px',
          position: 'relative'
        }}>
          <div style={{ marginBottom: '8px' }}>
            {formatLlmOutput(llmOutput)}
          </div>

          {loading && (
            <div style={{
              display: 'inline-flex',
              alignItems: 'center',
              color: '#52c41a',
              marginTop: '8px'
            }}>
              <div style={{
                display: 'inline-block',
                width: '8px',
                height: '16px',
                backgroundColor: '#52c41a',
                animation: 'blink 1s infinite',
                marginRight: '8px'
              }} />
              <span style={{ fontSize: '12px' }}>AI正在思考中...</span>
            </div>
          )}
        </div>
        <style jsx>{`
          @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
          }
        `}</style>
      </Card>
    )
  )

  // 渲染SQL结果步骤
  const renderSqlResultStep = () => (
    <div>
      {/* 结果统计 */}
      <Card title="生成结果统计" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="测试场景数"
              value={sqlResult?.scenario_count || 0}
              prefix={<DatabaseOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="测试表数量"
              value={sqlResult?.test_tables?.length || 0}
              prefix={<FileTextOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="生成状态"
              value={sqlResult?.status || 'unknown'}
              valueStyle={{ color: sqlResult?.status === 'completed' ? '#3f8600' : '#cf1322' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="生成时间"
              value={new Date(sqlResult?.created_at).toLocaleString()}
            />
          </Col>
        </Row>
      </Card>

      {/* SQL脚本展示 */}
      <Card
        title="生成的SQL脚本"
        style={{ marginBottom: 24 }}
        extra={
          <Space>
            <Button
              icon={<DownloadOutlined />}
              onClick={() => handleDownload()}
            >
              下载脚本
            </Button>
            <Button
              icon={<PlayCircleOutlined />}
              onClick={() => message.info('SQL执行功能开发中...')}
            >
              执行脚本
            </Button>
          </Space>
        }
      >
        <div style={{ height: '500px', border: '1px solid #d9d9d9' }}>
          <Editor
            value={sqlResult?.sql_content || ''}
            options={{
              ...editorOptions,
              readOnly: !previewMode
            }}
            onChange={(value) => {
              if (previewMode && sqlResult) {
                setSqlResult({
                  ...sqlResult,
                  sql_content: value
                })
              }
            }}
          />
        </div>

        <div style={{ marginTop: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Switch
              checked={previewMode}
              onChange={setPreviewMode}
              checkedChildren="编辑"
              unCheckedChildren="只读"
            />
            <Text type="secondary">
              {previewMode ? '可编辑模式' : '只读模式'}
            </Text>
          </Space>

          <Space>
            <Text type="secondary">
              脚本长度: {sqlResult?.sql_content?.length || 0} 字符
            </Text>
            {sqlResult?.test_tables && (
              <Text type="secondary">
                涉及表: {sqlResult.test_tables.join(', ')}
              </Text>
            )}
          </Space>
        </div>
      </Card>

      {/* 操作按钮 */}
      <div style={{ textAlign: 'center' }}>
        <Space>
          <Button
            icon={<HistoryOutlined />}
            onClick={() => setActiveTab('history')}
          >
            返回历史记录
          </Button>
          <Button onClick={handleReset}>
            重新生成
          </Button>
          <Button
            type="primary"
            onClick={() => message.info('保存到知识库功能开发中...')}
          >
            保存到知识库
          </Button>
        </Space>
      </div>
    </div>
  )

  // 渲染历史记录
  const renderHistoryTab = () => (
    <Card title="SQL生成历史">
      <List
        dataSource={generationHistory}
        renderItem={item => (
          <List.Item
            actions={[
              <Button
                type="link"
                onClick={() => handleViewHistory(item)}
              >
                查看
              </Button>,
              <Button
                type="link"
                onClick={() => handleDownload(item)}
              >
                下载
              </Button>,
              <Button
                type="link"
                danger
                onClick={() => {
                  Modal.confirm({
                    title: '确认删除',
                    content: '确定要删除这条SQL生成记录吗？此操作不可恢复。',
                    onOk: () => handleDeleteHistory(item.id)
                  })
                }}
              >
                删除
              </Button>
            ]}
          >
            <List.Item.Meta
              title={
                <Space>
                  <Text>{item.title}</Text>
                  <Tag color={item.status === 'completed' ? 'green' : 'red'}>
                    {item.status}
                  </Tag>
                </Space>
              }
              description={
                <div>
                  <div>数据库类型: {item.database_type}</div>
                  <div>生成时间: {new Date(item.created_at).toLocaleString()}</div>
                  <div>场景数: {item.scenario_count || 0} | 表数: {item.test_tables?.length || 0}</div>
                </div>
              }
            />
          </List.Item>
        )}
      />
    </Card>
  )

  // 主渲染
  return (
    <div style={{ padding: 24 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>SQL脚本生成</Title>
        <Space>
          <Button icon={<HistoryOutlined />} onClick={() => loadInitialData()}>
            刷新数据
          </Button>
        </Space>
      </div>

      {/* 面包屑导航 - 只在查看历史记录时显示 */}
      {activeTab === 'generation' && currentStep === 2 && sqlResult && (
        <div style={{ marginBottom: 16 }}>
          <Text type="secondary">
            <HistoryOutlined style={{ marginRight: 8 }} />
            正在查看历史记录：{sqlResult.test_case_name || `记录 ${sqlResult.id}`}
            <Button
              type="link"
              size="small"
              onClick={() => setActiveTab('history')}
              style={{ padding: '0 8px' }}
            >
              返回历史列表
            </Button>
            <Button
              type="link"
              size="small"
              onClick={() => {
                setActiveTab('generation');
                setCurrentStep(0);
                setSqlResult(null);
              }}
              style={{ padding: '0 8px' }}
            >
              返回用例列表
            </Button>
          </Text>
        </div>
      )}

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <CodeOutlined />
              SQL生成
            </span>
          }
          key="generation"
        >
          <div>
            <Steps current={currentStep} style={{ marginBottom: 24 }}>
              <Step
                title="选择测试用例"
                description="选择要生成SQL脚本的测试用例"
                icon={currentStep === 0 && loading ? <LoadingOutlined /> : <FileTextOutlined />}
              />
              <Step
                title="配置参数"
                description="设置数据库类型和生成参数"
                icon={currentStep === 1 && loading ? <LoadingOutlined /> : <SettingOutlined />}
              />
              <Step
                title="查看结果"
                description="查看和编辑生成的SQL脚本"
                icon={currentStep === 2 ? <CheckCircleOutlined /> : <CodeOutlined />}
              />
            </Steps>

            {currentStep === 0 && renderTestCaseSelection()}
            {currentStep === 1 && (
              <div>
                {renderConfigurationStep()}
                {renderLlmOutputWindow()}
              </div>
            )}
            {currentStep === 2 && renderSqlResultStep()}
          </div>
        </TabPane>

        <TabPane
          tab={
            <span>
              <HistoryOutlined />
              历史记录
            </span>
          }
          key="history"
        >
          {renderHistoryTab()}
        </TabPane>
      </Tabs>
    </div>
  )
}

export default SQLGeneration
