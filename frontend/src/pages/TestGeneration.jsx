import React, { useState, useEffect } from 'react'
import {
  Card,
  Steps,
  Form,
  Input,
  Select,
  Button,
  Typography,
  Alert,
  Spin,
  message,
  Row,
  Col,
  Divider,
  Tag,
  List,
  Collapse,
  Progress,
  Tabs,
  Table,
  Modal,
  Descriptions,
  Space
} from 'antd'
import {
  FileTextOutlined,
  BulbOutlined,
  CheckCircleOutlined,
  LoadingOutlined,
  ExperimentOutlined,
  HistoryOutlined,
  EyeOutlined,
  DeleteOutlined,
  RobotOutlined,
  CloseOutlined
} from '@ant-design/icons'
import api from '../services/api'

const { Title, Text, Paragraph } = Typography
const { TextArea } = Input
const { Option } = Select
const { Step } = Steps
const { Panel } = Collapse

const TestGeneration = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const [loading, setLoading] = useState(false)
  const [models, setModels] = useState([])
  const [analysisResult, setAnalysisResult] = useState(null)
  const [testCases, setTestCases] = useState(null)
  const [progressMessage, setProgressMessage] = useState('')
  const [streamMessages, setStreamMessages] = useState([])
  const [form] = Form.useForm()
  const [testCaseForm] = Form.useForm()

  // 实时输出状态
  const [llmOutput, setLlmOutput] = useState('')
  const [showLlmOutput, setShowLlmOutput] = useState(false)

  // 历史记录相关状态
  const [historyData, setHistoryData] = useState([])
  const [historyLoading, setHistoryLoading] = useState(false)
  const [historyDetailVisible, setHistoryDetailVisible] = useState(false)
  const [historyDetail, setHistoryDetail] = useState(null)
  const [activeTab, setActiveTab] = useState('generation')
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [historyTypeFilter, setHistoryTypeFilter] = useState(null) // 历史记录类型筛选

  useEffect(() => {
    fetchModels()
  }, [])

  // 获取历史记录
  const fetchHistory = async (typeFilter = null) => {
    try {
      setHistoryLoading(true)
      const params = {}
      if (typeFilter) {
        params.type_filter = typeFilter
      }
      console.log('Fetching history with params:', params)
      const response = await api.get('/tdpilot/history', { params })
      console.log('History response:', response.data)
      setHistoryData(response.data)
    } catch (error) {
      console.error('Fetch history error:', error)
      message.error('获取历史记录失败')
    } finally {
      setHistoryLoading(false)
    }
  }

  // 查看历史记录详情
  const viewHistoryDetail = async (record) => {
    try {
      const response = await api.get(`/tdpilot/history/${record.type}/${record.id}`)
      setHistoryDetail(response.data)
      setHistoryDetailVisible(true)
    } catch (error) {
      message.error('获取历史记录详情失败')
    }
  }

  // 从历史记录恢复数据
  const restoreFromHistory = async (record) => {
    try {
      // 先获取完整的历史记录详情
      const response = await api.get(`/tdpilot/history/${record.type}/${record.id}`)
      const fullRecord = response.data

      if (record.type === 'requirement_analysis') {
        setAnalysisResult(fullRecord)
        setCurrentStep(1)
        setActiveTab('generation')
        message.success('已恢复需求分析结果')
      } else if (record.type === 'test_case_generation') {
        setTestCases(fullRecord)
        setCurrentStep(2)
        setActiveTab('generation')
        message.success('已恢复测试用例结果')
      }
    } catch (error) {
      console.error('恢复历史记录失败:', error)
      message.error('恢复历史记录失败，请重试')
    }
  }

  // 删除历史记录
  const deleteHistoryItem = async (record) => {
    try {
      await api.delete(`/tdpilot/history/${record.type}/${record.id}`)
      message.success('删除成功')
      fetchHistory(historyTypeFilter) // 重新获取历史记录
    } catch (error) {
      console.error('删除历史记录失败:', error)
      message.error('删除失败')
    }
  }

  // 确认删除对话框
  const confirmDelete = (record) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除这条${record.type === 'requirement_analysis' ? '需求分析' :
                record.type === 'test_case_generation' ? '测试用例生成' : 'SQL生成'}记录吗？`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: () => deleteHistoryItem(record)
    })
  }

  // 批量删除历史记录
  const batchDeleteHistory = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录')
      return
    }

    const items = selectedRowKeys.map(key => {
      const record = historyData.find(item => item.id === key)
      return {
        id: record.id,
        type: record.type
      }
    })

    try {
      await api.delete('/tdpilot/history/batch', {
        data: { items }
      })
      message.success(`成功删除 ${selectedRowKeys.length} 条记录`)
      setSelectedRowKeys([])
      fetchHistory(historyTypeFilter) // 重新获取历史记录
    } catch (error) {
      console.error('批量删除失败:', error)
      message.error('批量删除失败')
    }
  }

  // 确认批量删除
  const confirmBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的记录')
      return
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedRowKeys.length} 条记录吗？此操作不可恢复。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: batchDeleteHistory
    })
  }

  // 处理类型筛选变化
  const handleTypeFilterChange = (value) => {
    console.log('Type filter changed to:', value)
    setHistoryTypeFilter(value)
    fetchHistory(value)
  }

  // 历史记录表格列定义
  const historyColumns = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        const typeMap = {
          'requirement_analysis': { text: '需求分析', color: 'blue' },
          'test_case_generation': { text: '测试用例', color: 'green' },
          'sql_generation': { text: 'SQL生成', color: 'purple' }
        }
        const config = typeMap[type] || { text: type, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          'analyzing': { text: '分析中', color: 'processing' },
          'generating': { text: '生成中', color: 'processing' },
          'completed': { text: '已完成', color: 'success' },
          'failed': { text: '失败', color: 'error' }
        }
        const config = statusMap[status] || { text: status, color: 'default' }
        return <Tag color={config.color}>{config.text}</Tag>
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => viewHistoryDetail(record)}
          >
            查看详情
          </Button>
          {(record.type === 'requirement_analysis' || record.type === 'test_case_generation') &&
           record.status === 'completed' && (
            <Button
              type="link"
              onClick={() => restoreFromHistory(record)}
            >
              恢复使用
            </Button>
          )}
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => confirmDelete(record)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ]

  const fetchModels = async () => {
    try {
      const response = await api.get('/llm/models')
      setModels(response.data.filter(model => model.is_active))
    } catch (error) {
      message.error('获取模型列表失败')
    }
  }

  // 处理流式响应的通用函数
  const handleStreamResponse = async (url, data, onComplete) => {
    setLoading(true)
    setStreamMessages([])
    setLlmOutput('')
    setShowLlmOutput(true)

    try {
      const authData = localStorage.getItem('auth-storage')
      let token = ''
      if (authData) {
        const { state } = JSON.parse(authData)
        token = state.token
      }

      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1'}${url}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') {
              setLoading(false)
              return
            }

            try {
              const parsed = JSON.parse(data)

              if (parsed.type === 'start' || parsed.type === 'progress') {
                setStreamMessages(prev => [...prev, parsed.message])
                setProgressMessage(parsed.message)
              } else if (parsed.type === 'llm_reasoning') {
                // 处理思考过程 - 累积显示
                setLlmOutput(prev => {
                  // 如果是第一个思考内容，添加标题
                  if (!prev.includes('[思考]')) {
                    return prev + '\n[思考] ' + parsed.content
                  }
                  return prev + parsed.content
                })
              } else if (parsed.type === 'llm_output') {
                // 处理LLM实际输出
                setLlmOutput(prev => {
                  // 如果之前有思考内容，先换行分隔
                  if (prev.includes('[思考]') && !prev.endsWith('\n\n')) {
                    return prev + '\n\n' + parsed.content
                  }
                  return prev + parsed.content
                })
              } else if (parsed.type === 'complete') {
                setStreamMessages(prev => [...prev, '✅ 完成！'])
                onComplete(parsed.data)
              } else if (parsed.type === 'error') {
                setStreamMessages(prev => [...prev, `❌ ${parsed.message}`])
                message.error(parsed.message)
              }
            } catch (e) {
              console.error('解析流式数据失败:', e)
            }
          }
        }
      }
    } catch (error) {
      console.error('流式请求失败:', error)
      message.error('请求失败: ' + error.message)
    } finally {
      setLoading(false)
      setProgressMessage('')
    }
  }

  const handleRequirementAnalysis = async (values) => {
    // 立即跳转到步骤1（查看分析结果步骤）
    setCurrentStep(1)

    await handleStreamResponse('/tdpilot/analyze-requirement-stream', values, (data) => {
      // 从返回的数据中提取analysis_result
      console.log('Received analysis data:', data)
      const analysisResult = data.analysis_result || data
      console.log('Setting analysis result:', analysisResult)
      console.log('Test points in analysis result:', analysisResult.test_points)
      console.log('Test scenarios:', analysisResult.test_scenarios)
      setAnalysisResult(analysisResult)
      message.success('需求分析完成')
    })
  }



  const handleTestCaseGeneration = async (values) => {
    // 立即跳转到步骤2（生成测试用例步骤）
    setCurrentStep(2)

    await handleStreamResponse('/tdpilot/generate-testcases-stream', {
      analysis_result_id: analysisResult.id,
      llm_model_id: values.llm_model_id,
      custom_requirements: values.custom_requirements
    }, (data) => {
      // 从返回的数据中提取test_cases
      const testCases = data.test_cases || data
      setTestCases(testCases)
      message.success('测试用例生成完成')
    })
  }



  const handleReset = () => {
    setCurrentStep(0)
    setAnalysisResult(null)
    setTestCases(null)
    form.resetFields()
    testCaseForm.resetFields()
  }

  const renderRequirementAnalysisStep = () => (
    <Card title="需求分析" style={{ marginTop: 16 }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleRequirementAnalysis}
      >
        <Form.Item
          name="requirement_text"
          label="需求描述"
          rules={[{ required: true, message: '请输入需求描述' }]}
        >
          <TextArea
            rows={6}
            placeholder="请详细描述您的测试需求，包括功能点、业务场景等..."
          />
        </Form.Item>

        <Form.Item
          name="background_knowledge"
          label="背景知识（可选）"
        >
          <TextArea
            rows={4}
            placeholder="提供相关的背景知识、业务规则、技术约束等..."
          />
        </Form.Item>

        <Form.Item
          name="design_document_url"
          label="设计文档链接（可选）"
        >
          <Input placeholder="如有设计文档，请提供链接" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="database_type"
              label="数据库类型"
              initialValue="tdsql-pg"
              rules={[{ required: true, message: '请选择数据库类型' }]}
            >
              <Select>
                <Option value="tdsql-pg">TDSQL-PostgreSQL</Option>
                <Option value="tdsql-oracle">TDSQL-Oracle</Option>
                <Option value="tdsql2">TDSQL2</Option>
                <Option value="tdsql3">TDSQL3</Option>
                <Option value="cynosdb">CynosDB</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="llm_model_id"
              label="选择AI模型"
              rules={[{ required: true, message: '请选择AI模型' }]}
              initialValue={7} // DeepSeek模型ID
            >
              <Select placeholder="选择用于分析的AI模型">
                {models.map(model => (
                  <Option key={model.id} value={model.id}>
                    {model.name} ({model.provider})
                    {model.id === 7 && <span style={{color: '#52c41a'}}> (推荐)</span>}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading} icon={<BulbOutlined />}>
            开始分析
          </Button>
        </Form.Item>

        {(loading || streamMessages.length > 0) && (
          <div style={{ marginTop: 16 }}>
            <Card title="AI分析进度" size="small">
              <div style={{ maxHeight: 200, overflowY: 'auto' }}>
                {streamMessages.map((msg, index) => (
                  <div key={index} style={{ marginBottom: 8, fontSize: '14px' }}>
                    <Text type={msg.includes('❌') ? 'danger' : msg.includes('✅') ? 'success' : 'secondary'}>
                      {msg}
                    </Text>
                  </div>
                ))}
                {loading && (
                  <div style={{ textAlign: 'center', marginTop: 8 }}>
                    <Spin size="small" />
                    <Text style={{ marginLeft: 8 }}>AI正在处理中...</Text>
                  </div>
                )}
              </div>
            </Card>
          </div>
        )}
      </Form>
    </Card>
  )

  const renderAnalysisResultStep = () => (
    <Card title="分析结果" style={{ marginTop: 16 }}>
      {loading && !analysisResult && (
        <div>
          <Alert
            message="正在分析需求"
            description="AI正在分析您的需求描述，生成测试场景和测试点，请稍候..."
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          {(loading || streamMessages.length > 0) && (
            <div style={{ marginBottom: 16 }}>
              <Card title="AI分析进度" size="small">
                <div style={{ maxHeight: 200, overflowY: 'auto' }}>
                  {streamMessages.map((msg, index) => (
                    <div key={index} style={{ marginBottom: 8, fontSize: '14px' }}>
                      <Text type={msg.includes('❌') ? 'danger' : msg.includes('✅') ? 'success' : 'secondary'}>
                        {msg}
                      </Text>
                    </div>
                  ))}
                  {loading && (
                    <div style={{ textAlign: 'center', marginTop: 8 }}>
                      <Spin size="small" />
                      <Text style={{ marginLeft: 8 }}>AI正在分析中...</Text>
                    </div>
                  )}
                </div>
              </Card>
            </div>
          )}

          {renderLlmOutputWindow()}
        </div>
      )}

      {analysisResult && (
        <div>
          <Alert
            message="需求分析完成"
            description="AI已完成对您需求的分析，请查看分析结果并继续生成测试用例。"
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Collapse defaultActiveKey={['1', '2', '3']}>
            <Panel header="测试场景" key="1">
              <List
                dataSource={analysisResult.test_scenarios}
                renderItem={(scenario, index) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Tag color="blue">{index + 1}</Tag>}
                      title={scenario.scenario}
                      description={
                        <div>
                          <Paragraph>{scenario.description}</Paragraph>
                          {scenario.test_points && scenario.test_points.length > 0 && (
                            <div>
                              <Text strong>测试点：</Text>
                              <ul>
                                {scenario.test_points.map((point, idx) => (
                                  <li key={idx}>{point}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Panel>

            <Panel header="测试点" key="2">
              <List
                dataSource={analysisResult.test_points}
                renderItem={(point, index) => (
                  <List.Item>
                    <Text>
                      <Tag color="green">{index + 1}</Tag>
                      {point}
                    </Text>
                  </List.Item>
                )}
              />
            </Panel>

            <Panel header="建议" key="3">
              <List
                dataSource={analysisResult.recommendations}
                renderItem={(recommendation, index) => (
                  <List.Item>
                    <Text>
                      <Tag color="orange">{index + 1}</Tag>
                      {recommendation}
                    </Text>
                  </List.Item>
                )}
              />
            </Panel>
          </Collapse>

          <Divider />

          <Form
            form={testCaseForm}
            layout="vertical"
            onFinish={handleTestCaseGeneration}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="llm_model_id"
                  label="选择AI模型"
                  rules={[{ required: true, message: '请选择AI模型' }]}
                  initialValue={7} // DeepSeek模型ID
                >
                  <Select placeholder="选择用于生成测试用例的AI模型">
                    {models.map(model => (
                      <Option key={model.id} value={model.id}>
                        {model.model_name} ({model.provider})
                        {model.id === 7 && <span style={{color: '#52c41a'}}> (推荐)</span>}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="custom_requirements"
              label="自定义需求（可选）"
            >
              <TextArea
                rows={4}
                placeholder="如有额外的测试需求或约束条件，请在此说明..."
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit" loading={loading} icon={<ExperimentOutlined />}>
                  生成测试用例
                </Button>
                <Button onClick={handleReset}>
                  重新开始
                </Button>
              </Space>
            </Form.Item>

            {(loading || streamMessages.length > 0) && (
              <div style={{ marginTop: 16 }}>
                <Card title="AI生成进度" size="small">
                  <div style={{ maxHeight: 200, overflowY: 'auto' }}>
                    {streamMessages.map((msg, index) => (
                      <div key={index} style={{ marginBottom: 8, fontSize: '14px' }}>
                        <Text type={msg.includes('❌') ? 'danger' : msg.includes('✅') ? 'success' : 'secondary'}>
                          {msg}
                        </Text>
                      </div>
                    ))}
                    {loading && (
                      <div style={{ textAlign: 'center', marginTop: 8 }}>
                        <Spin size="small" />
                        <Text style={{ marginLeft: 8 }}>AI正在处理中...</Text>
                      </div>
                    )}
                  </div>
                </Card>
              </div>
            )}
          </Form>

          {renderLlmOutputWindow()}
        </div>
      )}
    </Card>
  )

  // 格式化LLM输出内容
  const formatLlmOutput = (content) => {
    if (!content) return '等待LLM输出...'

    // 将内容按行分割
    const lines = content.split('\n')
    const formattedLines = []
    let inJsonBlock = false

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]

      // 处理思考过程标记
      if (line.startsWith('[思考]')) {
        formattedLines.push(
          <div key={i} style={{
            color: '#ffd700',
            fontWeight: 'bold',
            marginTop: '8px',
            marginBottom: '4px',
            borderLeft: '3px solid #ffd700',
            backgroundColor: 'rgba(255, 215, 0, 0.1)',
            borderRadius: '4px',
            padding: '6px 8px'
          }}>
            💭 {line.replace('[思考]', '').trim()}
          </div>
        )
      }
      // 处理JSON代码块开始
      else if (line.includes('```json')) {
        inJsonBlock = true
        formattedLines.push(
          <div key={i} style={{
            color: '#98c379',
            fontWeight: 'bold',
            marginTop: '8px',
            marginBottom: '4px'
          }}>
            📋 JSON输出:
          </div>
        )
      }
      // 处理JSON代码块结束
      else if (line.includes('```') && inJsonBlock) {
        inJsonBlock = false
        continue
      }
      // 处理JSON内容或普通内容
      else if (line.trim()) {
        const trimmedLine = line.trim()

        // 如果在JSON代码块中，或者看起来像JSON
        if (inJsonBlock ||
            trimmedLine.startsWith('{') ||
            trimmedLine.startsWith('[') ||
            trimmedLine.includes('"test_scenarios"') ||
            trimmedLine.includes('"test_points"') ||
            trimmedLine.includes('"recommendations"') ||
            trimmedLine.includes('"scenario"') ||
            trimmedLine.includes('"description"') ||
            trimmedLine.match(/^\s*"[\w_]+"\s*:/)) {

          formattedLines.push(
            <div key={i} style={{
              color: '#61dafb',
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
              fontSize: '12px',
              backgroundColor: '#2d3748',
              padding: '2px 8px',
              borderRadius: '3px',
              margin: '1px 0',
              lineHeight: '1.4'
            }}>
              {line}
            </div>
          )
        } else {
          // 普通文本内容
          formattedLines.push(
            <div key={i} style={{
              color: '#d4d4d4',
              lineHeight: '1.6',
              margin: '2px 0',
              fontSize: '13px'
            }}>
              {line}
            </div>
          )
        }
      }
      // 空行
      else {
        formattedLines.push(<div key={i} style={{ height: '6px' }} />)
      }
    }

    return formattedLines
  }

  // 渲染LLM实时输出窗口
  const renderLlmOutputWindow = () => (
    showLlmOutput && (
      <Card
        title={
          <Space>
            <RobotOutlined style={{ color: '#52c41a' }} />
            <span style={{ color: '#52c41a', fontWeight: 'bold' }}>🤖 AI思维过程</span>
            <Button
              type="text"
              size="small"
              icon={<CloseOutlined />}
              onClick={() => setShowLlmOutput(false)}
              style={{ marginLeft: 'auto' }}
            />
          </Space>
        }
        style={{ marginTop: 16 }}
        styles={{
          body: { padding: 0 },
          header: { backgroundColor: '#f6ffed', borderBottom: '1px solid #b7eb8f' }
        }}
      >
        <div style={{
          height: '350px',
          overflow: 'auto',
          backgroundColor: '#1a1a1a',
          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
          fontSize: '13px',
          lineHeight: '1.5',
          padding: '16px',
          position: 'relative'
        }}>
          <div style={{ marginBottom: '8px' }}>
            {formatLlmOutput(llmOutput)}
          </div>

          {loading && (
            <div style={{
              display: 'inline-flex',
              alignItems: 'center',
              color: '#52c41a',
              marginTop: '8px'
            }}>
              <div style={{
                display: 'inline-block',
                width: '8px',
                height: '16px',
                backgroundColor: '#52c41a',
                animation: 'blink 1s infinite',
                marginRight: '8px'
              }} />
              <span style={{ fontSize: '12px' }}>AI正在思考中...</span>
            </div>
          )}
        </div>
        <style jsx>{`
          @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
          }
        `}</style>
      </Card>
    )
  )

  const renderTestCasesStep = () => (
    <Card title="测试用例" style={{ marginTop: 16 }}>
      {loading && !testCases && (
        <div>
          <Alert
            message="正在生成测试用例"
            description="AI正在根据需求分析结果生成详细的测试用例，请稍候..."
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          {(loading || streamMessages.length > 0) && (
            <div style={{ marginBottom: 16 }}>
              <Card title="AI生成进度" size="small">
                <div style={{ maxHeight: 200, overflowY: 'auto' }}>
                  {streamMessages.map((msg, index) => (
                    <div key={index} style={{ marginBottom: 8, fontSize: '14px' }}>
                      <Text type={msg.includes('❌') ? 'danger' : msg.includes('✅') ? 'success' : 'secondary'}>
                        {msg}
                      </Text>
                    </div>
                  ))}
                  {loading && (
                    <div style={{ textAlign: 'center', marginTop: 8 }}>
                      <Spin size="small" />
                      <Text style={{ marginLeft: 8 }}>AI正在处理中...</Text>
                    </div>
                  )}
                </div>
              </Card>
            </div>
          )}

          {renderLlmOutputWindow()}
        </div>
      )}

      {testCases && (
        <div>
          <Alert
            message="测试用例生成完成"
            description="AI已成功生成测试用例，您可以查看详细内容并进行下一步操作。"
            type="success"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Collapse defaultActiveKey={['1']}>
            <Panel header="测试场景、测试点和测试用例" key="1">
              <List
                dataSource={testCases.test_scenarios}
                renderItem={(scenario, scenarioIndex) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Tag color="purple">{scenarioIndex + 1}</Tag>}
                      title={scenario.scenario_name || scenario.scenario || scenario.name}
                      description={
                        <div>
                          <Paragraph>{scenario.scenario_description || scenario.description}</Paragraph>

                          {/* 显示测试点和测试用例的三级层级结构 */}
                          {scenario.test_points && scenario.test_points.length > 0 ? (
                            <div style={{ marginTop: 16 }}>
                              <Text strong>测试点：</Text>
                              <List
                                size="small"
                                dataSource={scenario.test_points}
                                renderItem={(testPoint, pointIndex) => (
                                  <List.Item style={{ paddingLeft: 16, borderLeft: '2px solid #f0f0f0' }}>
                                    <div style={{ width: '100%' }}>
                                      <div style={{ marginBottom: 8 }}>
                                        <Tag color="green" size="small">{pointIndex + 1}</Tag>
                                        <Text strong>{testPoint.point_name || testPoint.name}</Text>
                                      </div>
                                      {testPoint.point_description && (
                                        <div style={{ marginLeft: 24, marginBottom: 8 }}>
                                          <Text type="secondary">{testPoint.point_description}</Text>
                                        </div>
                                      )}

                                      {/* 显示该测试点下的测试用例 */}
                                      {testPoint.test_cases && testPoint.test_cases.length > 0 && (
                                        <div style={{ marginLeft: 24 }}>
                                          <Text strong style={{ fontSize: '12px' }}>测试用例：</Text>
                                          <List
                                            size="small"
                                            dataSource={testPoint.test_cases}
                                            renderItem={(testCase, caseIndex) => (
                                              <List.Item style={{ paddingLeft: 16, borderLeft: '1px solid #e8e8e8' }}>
                                                <div style={{ width: '100%' }}>
                                                  <div>
                                                    <Tag color="cyan" size="small">{testCase.case_id || `TC${caseIndex + 1}`}</Tag>
                                                    <Text>{testCase.name || testCase.title}</Text>
                                                    <Tag color="orange" size="small" style={{ marginLeft: 8 }}>
                                                      {testCase.priority || 'Medium'}
                                                    </Tag>
                                                    <Tag color="blue" size="small">
                                                      {testCase.category || '功能'}
                                                    </Tag>
                                                  </div>
                                                  {testCase.description && (
                                                    <div style={{ marginTop: 4, fontSize: '12px' }}>
                                                      <Text type="secondary">{testCase.description}</Text>
                                                    </div>
                                                  )}
                                                  {testCase.expected_result && (
                                                    <div style={{ marginTop: 4, fontSize: '12px' }}>
                                                      <Text type="secondary">
                                                        <strong>预期结果：</strong>{testCase.expected_result}
                                                      </Text>
                                                    </div>
                                                  )}
                                                </div>
                                              </List.Item>
                                            )}
                                          />
                                        </div>
                                      )}
                                    </div>
                                  </List.Item>
                                )}
                              />
                            </div>
                          ) : (
                            // 兼容旧格式：直接显示测试用例（没有测试点层级）
                            scenario.test_cases && scenario.test_cases.length > 0 && (
                              <div style={{ marginTop: 16 }}>
                                <Text strong>测试用例：</Text>
                                <List
                                  size="small"
                                  dataSource={scenario.test_cases}
                                  renderItem={(testCase, idx) => (
                                    <List.Item>
                                      <Text>
                                        <Tag color="cyan" size="small">{idx + 1}</Tag>
                                        {testCase.name || testCase.title}
                                      </Text>
                                      {testCase.description && (
                                        <div style={{ marginLeft: 24, marginTop: 4 }}>
                                          <Text type="secondary">{testCase.description}</Text>
                                        </div>
                                      )}
                                    </List.Item>
                                  )}
                                />
                              </div>
                            )
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Panel>

            {/* 测试统计面板 */}
            <Panel header="测试统计" key="2">
              <Row gutter={16}>
                <Col span={6}>
                  <Card size="small">
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                        {testCases.test_scenarios?.length || 0}
                      </div>
                      <div style={{ marginTop: 4 }}>
                        <Text strong>测试场景</Text>
                      </div>
                    </div>
                  </Card>
                </Col>
                <Col span={6}>
                  <Card size="small">
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                        {testCases.test_scenarios?.reduce((total, scenario) =>
                          total + (scenario.test_points?.length || 0), 0) || 0}
                      </div>
                      <div style={{ marginTop: 4 }}>
                        <Text strong>测试点</Text>
                      </div>
                    </div>
                  </Card>
                </Col>
                <Col span={6}>
                  <Card size="small">
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                        {testCases.test_scenarios?.reduce((total, scenario) =>
                          total + (scenario.test_points?.reduce((pointTotal, point) =>
                            pointTotal + (point.test_cases?.length || 0), 0) || 0), 0) || 0}
                      </div>
                      <div style={{ marginTop: 4 }}>
                        <Text strong>测试用例</Text>
                      </div>
                    </div>
                  </Card>
                </Col>
                <Col span={6}>
                  <Card size="small">
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                        {testCases.summary?.total_test_cases ||
                         testCases.test_scenarios?.reduce((total, scenario) =>
                           total + (scenario.test_points?.reduce((pointTotal, point) =>
                             pointTotal + (point.test_cases?.length || 0), 0) || 0), 0) || 0}
                      </div>
                      <div style={{ marginTop: 4 }}>
                        <Text strong>预期用例数</Text>
                      </div>
                    </div>
                  </Card>
                </Col>
              </Row>
            </Panel>

            {testCases.visualization_data && (
              <Panel header="测试覆盖率" key="3">
                <Row gutter={16}>
                  <Col span={8}>
                    <Card size="small">
                      <div style={{ textAlign: 'center' }}>
                        <Progress
                          type="circle"
                          percent={testCases.visualization_data.coverage_percentage || 85}
                          format={percent => `${percent}%`}
                        />
                        <div style={{ marginTop: 8 }}>
                          <Text strong>整体覆盖率</Text>
                        </div>
                      </div>
                    </Card>
                  </Col>
                  <Col span={16}>
                    <Card size="small" title="覆盖详情">
                      <List
                        size="small"
                        dataSource={[
                          { name: '功能覆盖', value: testCases.visualization_data.functional_coverage || 90 },
                          { name: '边界覆盖', value: testCases.visualization_data.boundary_coverage || 80 },
                          { name: '异常覆盖', value: testCases.visualization_data.exception_coverage || 75 }
                        ]}
                        renderItem={item => (
                          <List.Item>
                            <div style={{ width: '100%' }}>
                              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                                <Text>{item.name}</Text>
                                <Text>{item.value}%</Text>
                              </div>
                              <Progress percent={item.value} showInfo={false} size="small" />
                            </div>
                          </List.Item>
                        )}
                      />
                    </Card>
                  </Col>
                </Row>
              </Panel>
            )}
          </Collapse>

          <Divider />

          <Space>
            <Button type="primary" onClick={() => message.info('SQL脚本生成功能开发中...')}>
              生成SQL脚本
            </Button>
            <Button onClick={handleReset}>
              重新开始
            </Button>
          </Space>
        </div>
      )}
    </Card>
  )

  return (
    <div style={{ padding: 24 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>测试用例生成</Title>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'generation',
            label: (
              <span>
                <ExperimentOutlined />
                生成测试用例
              </span>
            ),
            children: (
              <div>
                <Steps current={currentStep} style={{ marginBottom: 24 }}>
                  <Step
                    title="需求分析"
                    description="输入需求并进行AI分析"
                    icon={currentStep === 0 && loading ? <LoadingOutlined /> : <FileTextOutlined />}
                  />
                  <Step
                    title="查看分析结果"
                    description="查看AI分析结果并配置生成参数"
                    icon={currentStep === 1 && loading ? <LoadingOutlined /> : <BulbOutlined />}
                  />
                  <Step
                    title="生成测试用例"
                    description="AI生成详细测试用例"
                    icon={currentStep === 2 ? <CheckCircleOutlined /> : <ExperimentOutlined />}
                  />
                </Steps>

                {currentStep === 0 && renderRequirementAnalysisStep()}
                {currentStep === 1 && renderAnalysisResultStep()}
                {currentStep === 2 && renderTestCasesStep()}
              </div>
            )
          },
          {
            key: 'history',
            label: (
              <span>
                <HistoryOutlined />
                历史记录
              </span>
            ),
            children: (
              <Card
                title="历史记录"
                extra={
                  selectedRowKeys.length > 0 && (
                    <Button
                      type="primary"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={confirmBatchDelete}
                    >
                      批量删除 ({selectedRowKeys.length})
                    </Button>
                  )
                }
              >
                {/* 类型筛选器 */}
                <div style={{ marginBottom: 16 }}>
                  <Space>
                    <span>类型筛选：</span>
                    <Select
                      placeholder="选择类型"
                      allowClear
                      style={{ width: 150 }}
                      value={historyTypeFilter}
                      onChange={handleTypeFilterChange}
                      options={[
                        { label: '需求分析', value: 'requirement_analysis' },
                        { label: '测试用例生成', value: 'test_case_generation' },
                        { label: 'SQL生成', value: 'sql_generation' }
                      ]}
                    />
                  </Space>
                </div>
                <Table
                  columns={historyColumns}
                  dataSource={historyData}
                  loading={historyLoading}
                  rowKey="id"
                  rowSelection={{
                    selectedRowKeys,
                    onChange: setSelectedRowKeys,
                    getCheckboxProps: (record) => ({
                      name: record.title,
                    }),
                  }}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条记录`
                  }}
                  onRow={(record) => ({
                    onDoubleClick: () => viewHistoryDetail(record)
                  })}
                />
              </Card>
            )
          }
        ]}
        onTabClick={(key) => {
          if (key === 'history') {
            fetchHistory(historyTypeFilter)
          }
        }}
      />

      {/* 历史记录详情弹窗 */}
      <Modal
        title="历史记录详情"
        open={historyDetailVisible}
        onCancel={() => setHistoryDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setHistoryDetailVisible(false)}>
            关闭
          </Button>,
          historyDetail && (historyDetail.type === 'requirement_analysis' || historyDetail.type === 'test_case_generation') &&
          historyDetail.status === 'completed' && (
            <Button
              key="restore"
              type="primary"
              onClick={() => {
                restoreFromHistory(historyDetail)
                setHistoryDetailVisible(false)
              }}
            >
              恢复使用
            </Button>
          )
        ]}
        width={800}
      >
        {historyDetail && (
          <Descriptions column={1} bordered>
            <Descriptions.Item label="类型">
              {historyDetail.type === 'requirement_analysis' ? '需求分析' :
               historyDetail.type === 'test_case_generation' ? '测试用例生成' :
               historyDetail.type === 'sql_generation' ? 'SQL生成' : historyDetail.type}
            </Descriptions.Item>
            <Descriptions.Item label="标题">{historyDetail.title}</Descriptions.Item>
            <Descriptions.Item label="状态">{historyDetail.status}</Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {historyDetail.created_at ? new Date(historyDetail.created_at).toLocaleString() : '-'}
            </Descriptions.Item>
            {historyDetail.completed_at && (
              <Descriptions.Item label="完成时间">
                {new Date(historyDetail.completed_at).toLocaleString()}
              </Descriptions.Item>
            )}

            {historyDetail.type === 'requirement_analysis' && (
              <>
                <Descriptions.Item label="需求描述">{historyDetail.requirement_text}</Descriptions.Item>
                {historyDetail.background_knowledge && (
                  <Descriptions.Item label="背景知识">{historyDetail.background_knowledge}</Descriptions.Item>
                )}
                <Descriptions.Item label="数据库类型">{historyDetail.database_type}</Descriptions.Item>
                <Descriptions.Item label="测试场景数量">
                  {historyDetail.test_scenarios ? historyDetail.test_scenarios.length : 0}
                </Descriptions.Item>
              </>
            )}

            {historyDetail.type === 'test_case_generation' && (
              <>
                <Descriptions.Item label="关联需求">{historyDetail.analysis_title}</Descriptions.Item>
                {historyDetail.custom_requirements && (
                  <Descriptions.Item label="自定义要求">{historyDetail.custom_requirements}</Descriptions.Item>
                )}
                <Descriptions.Item label="测试场景数量">
                  {historyDetail.test_scenarios ? historyDetail.test_scenarios.length : 0}
                </Descriptions.Item>
              </>
            )}

            {historyDetail.type === 'sql_generation' && (
              <>
                <Descriptions.Item label="关联测试用例">{historyDetail.test_case_title}</Descriptions.Item>
                <Descriptions.Item label="数据库类型">{historyDetail.database_type}</Descriptions.Item>
                <Descriptions.Item label="SQL长度">
                  {historyDetail.sql_length || 0} 字符
                </Descriptions.Item>
              </>
            )}
          </Descriptions>
        )}
      </Modal>
    </div>
  )
}

export default TestGeneration
