import React, { useState, useEffect } from 'react'
import {
  Card,
  Button,
  Select,
  Input,
  Space,
  message,
  Divider,
  Switch,
  Form,
  Row,
  Col,
  Typography
} from 'antd'
import { SendOutlined, ClearOutlined } from '@ant-design/icons'
import api from '../services/api'
import LLMResponseDisplay from '../components/llm/LLMResponseDisplay'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

const LLMDemo = () => {
  const [models, setModels] = useState([])
  const [loading, setLoading] = useState(false)
  const [response, setResponse] = useState(null)
  const [form] = Form.useForm()

  useEffect(() => {
    loadModels()
  }, [])

  const loadModels = async () => {
    try {
      const response = await api.get('/llm/models')
      setModels(response.data)
    } catch (error) {
      message.error('加载模型列表失败')
    }
  }

  const handleSubmit = async (values) => {
    setLoading(true)
    try {
      const requestData = {
        model_id: values.model_id,
        messages: [{ role: 'user', content: values.prompt }],
        max_tokens: values.max_tokens,
        temperature: values.temperature,
        enable_stream: values.enable_stream
      }

      // 添加工具调用（如果启用）
      if (values.enable_tools) {
        requestData.tools = [
          {
            type: "function",
            function: {
              name: "get_weather",
              description: "获取指定城市的天气信息",
              parameters: {
                type: "object",
                properties: {
                  city: {
                    type: "string",
                    description: "城市名称"
                  }
                },
                required: ["city"]
              }
            }
          },
          {
            type: "function", 
            function: {
              name: "calculate",
              description: "执行数学计算",
              parameters: {
                type: "object",
                properties: {
                  expression: {
                    type: "string",
                    description: "数学表达式"
                  }
                },
                required: ["expression"]
              }
            }
          }
        ]
      }

      const result = await api.post('/llm/models/generate', requestData)
      setResponse(result.data)
      message.success('生成完成')
    } catch (error) {
      message.error('生成失败: ' + (error.response?.data?.detail || error.message))
    } finally {
      setLoading(false)
    }
  }

  const handleClear = () => {
    setResponse(null)
    form.resetFields(['prompt'])
  }

  const examplePrompts = [
    {
      label: "普通对话",
      value: "你好！请介绍一下你自己。"
    },
    {
      label: "思考模型演示",
      value: "<thinking>用户想了解我的能力，我应该简洁地介绍自己的功能和特点。</thinking>你好！我是一个AI助手，可以帮助你回答问题、分析问题和提供建议。"
    },
    {
      label: "工具调用演示",
      value: "请帮我查询北京今天的天气情况。"
    },
    {
      label: "数学计算演示", 
      value: "请帮我计算 (25 + 15) * 3 - 8 的结果。"
    }
  ]

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>LLM 服务演示</Title>
      <Text type="secondary">
        演示重构后的 LLM 服务功能，包括思考模型、工具调用、详细 token 统计等。
      </Text>

      <Row gutter={24} style={{ marginTop: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="配置和输入" style={{ height: '100%' }}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              initialValues={{
                model_id: 1,
                max_tokens: 1000,
                temperature: 0.7,
                enable_stream: false,
                enable_tools: false
              }}
            >
              <Form.Item name="model_id" label="选择模型">
                <Select placeholder="选择一个模型">
                  {models.map(model => (
                    <Option key={model.id} value={model.id}>
                      {model.name} ({model.provider})
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item name="prompt" label="输入内容" rules={[{ required: true, message: '请输入内容' }]}>
                <TextArea
                  rows={6}
                  placeholder="输入你的问题或指令..."
                />
              </Form.Item>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item name="max_tokens" label="最大Token">
                    <Input type="number" min={1} max={4000} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="temperature" label="Temperature">
                    <Input type="number" step={0.1} min={0} max={2} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="enable_stream" label="流式输出" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item name="enable_tools" label="启用工具调用" valuePropName="checked">
                <Switch />
              </Form.Item>

              <Space>
                <Button type="primary" htmlType="submit" loading={loading} icon={<SendOutlined />}>
                  生成
                </Button>
                <Button onClick={handleClear} icon={<ClearOutlined />}>
                  清空
                </Button>
              </Space>
            </Form>

            <Divider />

            <div>
              <Text strong>示例提示词：</Text>
              <div style={{ marginTop: 8 }}>
                {examplePrompts.map((example, index) => (
                  <Button
                    key={index}
                    size="small"
                    style={{ margin: '4px 4px 4px 0' }}
                    onClick={() => form.setFieldsValue({ prompt: example.value })}
                  >
                    {example.label}
                  </Button>
                ))}
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="响应结果" style={{ height: '100%' }}>
            {response ? (
              <LLMResponseDisplay response={response} />
            ) : (
              <div style={{ 
                textAlign: 'center', 
                color: '#999', 
                padding: '60px 20px',
                background: '#fafafa',
                borderRadius: 8
              }}>
                <Text type="secondary">
                  配置参数并输入内容，点击"生成"按钮查看结果
                </Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default LLMDemo
