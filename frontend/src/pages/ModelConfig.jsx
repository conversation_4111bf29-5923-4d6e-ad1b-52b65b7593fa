import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Space,
  Tag,
  Typography,
  Divider
} from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, ExperimentOutlined, SettingOutlined } from '@ant-design/icons'
import api from '../services/api'
import ModelConfigWizard from '../components/llm/ModelConfigWizard'

const { Title } = Typography
const { Option } = Select

const ModelConfig = () => {
  const navigate = useNavigate()
  const [models, setModels] = useState([])
  const [providers, setProviders] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [wizardVisible, setWizardVisible] = useState(false)
  const [editingModel, setEditingModel] = useState(null)
  const [testModalVisible, setTestModalVisible] = useState(false)
  const [testingModel, setTestingModel] = useState(null)
  const [selectedProvider, setSelectedProvider] = useState(null)
  const [form] = Form.useForm()
  const [testForm] = Form.useForm()

  useEffect(() => {
    fetchModels()
    fetchProviders()
  }, [])

  const fetchModels = async () => {
    setLoading(true)
    try {
      const response = await api.get('/llm/models')
      setModels(response.data)
    } catch (error) {
      message.error('获取模型列表失败')
    } finally {
      setLoading(false)
    }
  }

  const fetchProviders = async () => {
    try {
      const response = await api.get('/llm/providers')
      setProviders(response.data)
    } catch (error) {
      message.error('获取提供商列表失败')
    }
  }

  const handleAdd = () => {
    setEditingModel(null)
    setSelectedProvider(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleWizardAdd = () => {
    setWizardVisible(true)
  }

  const handleWizardFinish = async (result) => {
    try {
      if (result.success) {
        message.success(result.message || '模型配置创建成功')
        setWizardVisible(false)
        fetchModels()
      } else {
        message.error('创建模型配置失败')
      }
    } catch (error) {
      message.error('模型配置创建失败')
    }
  }

  const handleProviderChange = (provider) => {
    const providerInfo = providers.find(p => p.name === provider)
    if (providerInfo) {
      form.setFieldsValue({
        model_name: providerInfo.default_model,
        base_url: providerInfo.default_base_url
      })

      // 如果不需要 API Key，清空该字段
      if (!providerInfo.requires_api_key) {
        form.setFieldsValue({
          api_key: ''
        })
      }
    }
    setSelectedProvider(provider)
  }

  const handleEdit = (record) => {
    setEditingModel(record)
    setSelectedProvider(record.provider)
    form.setFieldsValue({
      ...record,
      enable_stream: record.enable_stream !== undefined ? record.enable_stream : true
    })
    setModalVisible(true)
  }

  const handleDelete = async (id) => {
    try {
      await api.delete(`/llm/models/${id}`)
      message.success('删除成功')
      fetchModels()
    } catch (error) {
      message.error('删除失败')
    }
  }

  const handleSubmit = async (values) => {
    try {
      if (editingModel) {
        await api.put(`/llm/models/${editingModel.id}`, values)
        message.success('更新成功')
      } else {
        await api.post('/llm/models', values)
        message.success('创建成功')
      }
      setModalVisible(false)
      fetchModels()
    } catch (error) {
      message.error(editingModel ? '更新失败' : '创建失败')
    }
  }

  const handleTest = (record) => {
    setTestingModel(record)
    testForm.resetFields()
    setTestModalVisible(true)
  }

  const handleTestSubmit = async (values) => {
    // 跳转到流式测试页面
    navigate('/streaming-test', {
      state: {
        model: testingModel,
        testPrompt: values.test_prompt
      }
    })

    // 关闭测试模态框
    setTestModalVisible(false)
  }

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '提供商',
      dataIndex: 'provider',
      key: 'provider',
      render: (provider) => {
        const providerInfo = providers.find(p => p.name === provider)
        return providerInfo ? providerInfo.display_name : provider
      }
    },
    {
      title: '模型',
      dataIndex: 'model_name',
      key: 'model_name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          available: { color: 'success', text: '可用' },
          unavailable: { color: 'error', text: '不可用' },
          testing: { color: 'processing', text: '测试中' }
        }
        const statusInfo = statusMap[status] || { color: 'default', text: status }
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
      }
    },
    {
      title: '启用状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Switch checked={isActive} disabled />
      )
    },
    {
      title: '流式输出',
      dataIndex: 'enable_stream',
      key: 'enable_stream',
      render: (enableStream) => (
        <Switch checked={enableStream} disabled />
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<ExperimentOutlined />}
            onClick={() => handleTest(record)}
          >
            测试
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>LLM模型配置</Title>
        <Space>
          <Button icon={<SettingOutlined />} onClick={handleWizardAdd}>
            配置向导
          </Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            快速添加
          </Button>
        </Space>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={models}
          loading={loading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 添加/编辑模型弹窗 */}
      <Modal
        title={editingModel ? '编辑模型' : '添加模型'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="模型名称"
            rules={[{ required: true, message: '请输入模型名称' }]}
          >
            <Input placeholder="请输入模型名称" />
          </Form.Item>

          <Form.Item
            name="provider"
            label="提供商"
            rules={[{ required: true, message: '请选择提供商' }]}
          >
            <Select
              placeholder="请选择提供商"
              onChange={handleProviderChange}
            >
              {providers.map(provider => (
                <Option key={provider.name} value={provider.name}>
                  {provider.display_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="model_name"
            label="模型标识"
            rules={[{ required: true, message: '请输入模型标识' }]}
            help="选择提供商后会自动填入默认模型名称"
          >
            <Input placeholder="如: gpt-4, claude-3-sonnet-20240229" />
          </Form.Item>

          <Form.Item
            name="base_url"
            label="Base URL"
            help="API端点地址，选择提供商后会自动填入默认地址"
          >
            <Input placeholder="如: https://api.openai.com/v1" />
          </Form.Item>

          <Form.Item
            name="api_key"
            label="API Key"
            rules={[
              {
                required: selectedProvider ? providers.find(p => p.name === selectedProvider)?.requires_api_key !== false : true,
                message: '请输入API Key'
              }
            ]}
            help={selectedProvider && !providers.find(p => p.name === selectedProvider)?.requires_api_key ? "此提供商不需要API Key" : undefined}
          >
            <Input.Password
              placeholder={selectedProvider && !providers.find(p => p.name === selectedProvider)?.requires_api_key ? "此提供商不需要API Key" : "请输入API Key"}
              disabled={selectedProvider && !providers.find(p => p.name === selectedProvider)?.requires_api_key}
            />
          </Form.Item>

          <Form.Item
            name="max_tokens"
            label="最大Token数"
            initialValue={32768}
          >
            <Input type="number" placeholder="32768" />
          </Form.Item>

          <Form.Item
            name="temperature"
            label="Temperature"
            initialValue={0.7}
          >
            <Input type="number" step="0.1" min="0" max="2" placeholder="0.7" />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="启用状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="enable_stream"
            label="启用流式输出"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch
              checkedChildren="启用"
              unCheckedChildren="禁用"
            />
          </Form.Item>

          <Divider />

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingModel ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 测试模型弹窗 */}
      <Modal
        title={`测试模型: ${testingModel?.name}`}
        open={testModalVisible}
        onCancel={() => setTestModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form
          form={testForm}
          layout="vertical"
          onFinish={handleTestSubmit}
        >
          <Form.Item
            name="test_prompt"
            label="测试提示"
            initialValue="Hello, this is a test message."
            rules={[{ required: true, message: '请输入测试提示' }]}
          >
            <Input.TextArea 
              rows={4} 
              placeholder="请输入测试提示" 
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setTestModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                开始测试
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 配置向导 */}
      <ModelConfigWizard
        visible={wizardVisible}
        onCancel={() => setWizardVisible(false)}
        onFinish={handleWizardFinish}
        providers={providers}
      />
    </div>
  )
}

export default ModelConfig
