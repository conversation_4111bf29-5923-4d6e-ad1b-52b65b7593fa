import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'
import Login from './pages/Login'
import Dashboard from './pages/Dashboard'
import ModelConfig from './pages/ModelConfig'
import StreamingTest from './pages/StreamingTest'
import TestGeneration from './pages/TestGeneration'
import SQLGeneration from './pages/SQLGeneration'
import Knowledge from './pages/Knowledge'
import Memory from './pages/Memory'
import MCPManagement from './pages/MCPManagement'
import LLMDemo from './pages/LLMDemo'
import MainLayout from './components/common/MainLayout'
import { useAuthStore } from './store/authStore'

function App() {
  const { isAuthenticated } = useAuthStore()

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Routes>
        <Route 
          path="/login" 
          element={!isAuthenticated ? <Login /> : <Navigate to="/dashboard" />} 
        />
        <Route 
          path="/*" 
          element={
            isAuthenticated ? (
              <MainLayout>
                <Routes>
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/model-config" element={<ModelConfig />} />
                  <Route path="/streaming-test" element={<StreamingTest />} />
                  <Route path="/llm-demo" element={<LLMDemo />} />
                  <Route path="/test-generation" element={<TestGeneration />} />
                  <Route path="/sql-generation" element={<SQLGeneration />} />
                  <Route path="/knowledge" element={<Knowledge />} />
                  <Route path="/memory" element={<Memory />} />
                  <Route path="/mcp" element={<MCPManagement />} />
                  <Route path="/" element={<Navigate to="/dashboard" />} />
                </Routes>
              </MainLayout>
            ) : (
              <Navigate to="/login" />
            )
          } 
        />
      </Routes>
    </Layout>
  )
}

export default App
