import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import api from '../services/api'

export const useAuthStore = create(
  persist(
    (set, get) => ({
      // 状态
      isAuthenticated: false,
      user: null,
      token: null,
      
      // 登录
      login: async (credentials) => {
        try {
          const response = await api.post('/auth/login', credentials)
          const { access_token, token_type } = response.data
          
          // 设置token
          set({ 
            isAuthenticated: true, 
            token: access_token 
          })
          
          // 获取用户信息
          const userResponse = await api.get('/auth/me', {
            headers: { Authorization: `${token_type} ${access_token}` }
          })
          
          set({ user: userResponse.data })
          
          return { success: true }
        } catch (error) {
          return { 
            success: false, 
            message: error.response?.data?.message || '登录失败' 
          }
        }
      },
      
      // 注册
      register: async (userData) => {
        try {
          await api.post('/auth/register', userData)
          return { success: true, message: '注册成功' }
        } catch (error) {
          return { 
            success: false, 
            message: error.response?.data?.message || '注册失败' 
          }
        }
      },
      
      // 登出
      logout: async () => {
        try {
          await api.post('/auth/logout')
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          set({ 
            isAuthenticated: false, 
            user: null, 
            token: null 
          })
        }
      },
      
      // 更新用户信息
      updateUser: (userData) => {
        set({ user: { ...get().user, ...userData } })
      },
      
      // 检查token有效性
      checkAuth: async () => {
        const { token } = get()
        if (!token) {
          set({ isAuthenticated: false, user: null, token: null })
          return false
        }
        
        try {
          const response = await api.get('/auth/me', {
            headers: { Authorization: `Bearer ${token}` }
          })
          set({ user: response.data, isAuthenticated: true })
          return true
        } catch (error) {
          set({ isAuthenticated: false, user: null, token: null })
          return false
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        token: state.token
      })
    }
  )
)
