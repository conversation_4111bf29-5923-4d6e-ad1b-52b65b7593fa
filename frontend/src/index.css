/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

#root {
  min-height: 100vh;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 代码编辑器样式 */
.monaco-editor {
  border-radius: 6px;
}

/* 自定义Ant Design组件样式 */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
}

.ant-menu {
  border-right: none;
}

.ant-layout-header {
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  padding: 0 24px;
}

.ant-layout-content {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-content {
    margin: 16px;
    padding: 16px;
  }
}

/* SQL生成页面样式 */
.selected-card {
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.selected-card:hover {
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

/* 代码编辑器样式 */
.monaco-editor {
  border-radius: 6px;
}

/* 步骤条样式 */
.ant-steps-item-finish .ant-steps-item-icon {
  background-color: #52c41a;
  border-color: #52c41a;
}

.ant-steps-item-process .ant-steps-item-icon {
  background-color: #1890ff;
  border-color: #1890ff;
}

/* 统计卡片样式 */
.ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 测试用例卡片样式 */
.test-case-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.test-case-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 历史记录列表样式 */
.ant-list-item {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.ant-list-item:hover {
  background-color: #fafafa;
}
